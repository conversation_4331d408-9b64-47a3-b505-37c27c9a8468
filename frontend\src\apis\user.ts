import { request } from '@/utils/request';

/**
 * 用户相关接口
 */

// 用户登录接口
export interface LoginParams {
  username: string;
  password: string;
}

// 基础用户信息接口
export interface UserInfo {
  id: string;
  username: string;
  email: string;
  is_active: boolean;
  is_locked: boolean;
  account_status: string;
  status_reason?: string;
  create_time: string;
  avatar_url?: string;
  bio?: string;
  timezone: string;
  locale: string;
  phone_number?: string;
  display_name?: string;
  full_name?: string;
  roles: string[];
}

// 可为空的用户信息接口，适用于store的状态定义
export type NullableUserInfo = {
  [K in keyof UserInfo]: K extends 'roles' ? UserInfo[K] : UserInfo[K] | null;
};

// 实际的登录返回结果
export interface LoginResult {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

/**
 * 用户登录
 */
export function login(data: LoginParams) {
  const form = new FormData();
  form.append('username', data.username);
  form.append('password', data.password);
  return request.post<LoginResult>('/api/v1/auth/login', form, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return request.get<UserInfo>('/api/v1/users/me');
}
