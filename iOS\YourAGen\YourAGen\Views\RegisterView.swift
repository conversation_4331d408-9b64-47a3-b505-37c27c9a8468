import SwiftUI

struct RegisterView: View {
    @EnvironmentObject var userData: UserData
    @EnvironmentObject var themeSettings: ThemeSettings
    @EnvironmentObject var languageManager: LanguageManager
    @Binding var isPresented: Bool
    
    @State private var name = ""
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var agreeToTerms = false
    @State private var isLoading = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @FocusState private var isNameFocused: Bool
    @FocusState private var isEmailFocused: Bool
    @FocusState private var isPasswordFocused: Bool
    @FocusState private var isConfirmPasswordFocused: Bool
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 标题区域
                    VStack(spacing: 8) {
                        Image(systemName: "person.crop.circle.badge.plus")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 80, height: 80)
                            .foregroundColor(.blue)
                            .padding(.top, 40)
                        
                        Text(languageManager.localizedString("auth.register.title"))
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text(languageManager.localizedString("auth.register.subtitle"))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.horizontal)
                    
                    // 表单区域
                    VStack(spacing: 16) {
                        // 姓名输入
                        VStack(alignment: .leading, spacing: 8) {
                            Text(languageManager.localizedString("auth.register.fields.name"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            TextField(languageManager.localizedString("auth.register.fields.name"), text: $name)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .autocapitalization(.words)
                                .autocorrectionDisabled()
                                .focused($isNameFocused)
                        }
                        
                        // 邮箱输入
                        VStack(alignment: .leading, spacing: 8) {
                            Text(languageManager.localizedString("auth.register.fields.email"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            TextField(languageManager.localizedString("auth.register.fields.email"), text: $email)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .keyboardType(.emailAddress)
                                .autocapitalization(.none)
                                .autocorrectionDisabled()
                                .focused($isEmailFocused)
                        }
                        
                        // 密码输入
                        VStack(alignment: .leading, spacing: 8) {
                            Text(languageManager.localizedString("auth.register.fields.password"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            SecureField(languageManager.localizedString("auth.register.fields.password"), text: $password)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .focused($isPasswordFocused)
                        }
                        
                        // 确认密码输入
                        VStack(alignment: .leading, spacing: 8) {
                            Text(languageManager.localizedString("auth.register.fields.confirmPassword"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            SecureField(languageManager.localizedString("auth.register.fields.confirmPassword"), text: $confirmPassword)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .focused($isConfirmPasswordFocused)
                        }
                        
                        // 同意条款
                        Button(action: {
                            agreeToTerms.toggle()
                        }) {
                            HStack(spacing: 8) {
                                Image(systemName: agreeToTerms ? "checkmark.square.fill" : "square")
                                    .foregroundColor(agreeToTerms ? .blue : .gray)
                                Text(languageManager.localizedString("auth.register.actions.agreeToTerms"))
                                    .font(.footnote)
                                    .foregroundColor(.primary)
                                Spacer()
                            }
                        }
                        
                        // 注册按钮
                        Button(action: performRegister) {
                            HStack {
                                if isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                }
                                Text(languageManager.localizedString("auth.register.actions.signUp"))
                                    .fontWeight(.semibold)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(isLoading ? Color.gray : Color.blue)
                            )
                        }
                        .disabled(isLoading || !isFormValid)
                        
                        // 登录链接
                        HStack {
                            Text(languageManager.localizedString("auth.register.actions.hasAccount"))
                                .font(.footnote)
                                .foregroundColor(.secondary)
                            
                            Button(action: {
                                isPresented = false
                            }) {
                                Text(languageManager.localizedString("auth.register.actions.signIn"))
                                    .font(.footnote)
                                    .fontWeight(.medium)
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                    .padding(.horizontal, 24)
                    
                    Spacer()
                }
            }
            .navigationTitle("")
            .navigationBarHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(languageManager.localizedString("common.cancel")) {
                        dismissKeyboard()
                        isPresented = false
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text(languageManager.localizedString("auth.register.error.title")),
                    message: Text(alertMessage),
                    dismissButton: .default(Text(languageManager.localizedString("common.ok")))
                )
            }
            .onTapGesture {
                dismissKeyboard()
            }
        }
        .preferredColorScheme(themeSettings.colorScheme)
    }
    
    private var isFormValid: Bool {
        !name.isEmpty && 
        !email.isEmpty && 
        !password.isEmpty && 
        password == confirmPassword && 
        agreeToTerms
    }
    
    private func performRegister() {
        guard !isLoading else { return }
        
        // 验证输入
        guard isFormValid else {
            if password != confirmPassword {
                alertMessage = languageManager.localizedString("auth.register.error.passwordMismatch")
            } else if !agreeToTerms {
                alertMessage = languageManager.localizedString("auth.register.error.mustAgreeToTerms")
            } else {
                alertMessage = languageManager.localizedString("auth.register.error.emptyFields")
            }
            showingAlert = true
            return
        }
        
        dismissKeyboard()
        isLoading = true
        
        Task {
            do {
                let success = await userData.register(name: name, email: email, password: password)
                
                await MainActor.run {
                    isLoading = false
                    if success {
                        isPresented = false
                    } else {
                        alertMessage = languageManager.localizedString("auth.register.error.registrationFailed")
                        showingAlert = true
                    }
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    alertMessage = error.localizedDescription
                    showingAlert = true
                }
            }
        }
    }
    
    private func dismissKeyboard() {
        isNameFocused = false
        isEmailFocused = false
        isPasswordFocused = false
        isConfirmPasswordFocused = false
    }
}

#Preview {
    RegisterView(isPresented: .constant(true))
        .environmentObject(UserData())
        .environmentObject(ThemeSettings())
        .environmentObject(LanguageManager())
} 