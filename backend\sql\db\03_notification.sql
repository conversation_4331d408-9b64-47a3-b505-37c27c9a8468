------------------------------------------
-- 通知系统
-- 处理系统内各类通知的发送、存储和用户偏好设置
------------------------------------------
-- 通知渠道表
-- 定义系统支持的各种通知发送渠道（如邮件、短信等）
CREATE TABLE notification.channels
(
    id          SERIAL PRIMARY KEY,                                         -- 渠道ID
    name        VARCHAR(50) UNIQUE NOT NULL CHECK (LENGTH(TRIM(name)) > 0), -- 渠道名称
    description VARCHAR(255),                                               -- 渠道描述
    handler     VARCHAR(100)       NOT NULL,                                -- 处理器名称
    is_enabled  BOOLEAN            NOT NULL DEFAULT TRUE,                   -- 是否启用
    config      JSONB                       DEFAULT '{}'::jsonb,            -- 渠道配置
    create_time TIMESTAMPTZ        NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
    update_time TIMESTAMPTZ        NOT NULL DEFAULT CURRENT_TIMESTAMP       -- 更新时间
);

-- 通知模板表
-- 存储各类通知的模板内容，支持文本和HTML格式
CREATE TABLE notification.templates
(
    id               SERIAL PRIMARY KEY,                                         -- 模板ID
    key              VARCHAR(100) UNIQUE NOT NULL CHECK (LENGTH(TRIM(key)) > 0), -- 模板唯一键
    name             VARCHAR(100)        NOT NULL,                               -- 模板名称
    description      TEXT,                                                       -- 模板描述
    channel_id       INTEGER             NOT NULL,                               -- 关联的渠道ID
    subject_template TEXT,                                                       -- 主题模板
    body_template    TEXT                NOT NULL,                               -- 内容模板
    html_template    TEXT,                                                       -- HTML格式模板
    variables        JSONB,                                                      -- 模板变量定义
    is_enabled       BOOLEAN             NOT NULL DEFAULT TRUE,                  -- 是否启用
    create_time      TIMESTAMPTZ         NOT NULL DEFAULT CURRENT_TIMESTAMP,     -- 创建时间
    update_time      TIMESTAMPTZ         NOT NULL DEFAULT CURRENT_TIMESTAMP,     -- 更新时间
    is_deleted        BOOLEAN             NOT NULL DEFAULT FALSE                  -- 是否已删除
);

CREATE INDEX idx_notification_templates_key ON notification.templates (key) WHERE is_deleted = FALSE;
CREATE INDEX idx_notification_templates_channel ON notification.templates (channel_id) WHERE is_deleted = FALSE;

-- 用户通知偏好表
-- 存储用户对不同类型通知的接收偏好设置
CREATE TABLE notification.user_preferences
(
    user_id           UUID         NOT NULL,                           -- 用户ID
    notification_type VARCHAR(100) NOT NULL,                           -- 通知类型
    channel_id        INTEGER      NOT NULL,                           -- 渠道ID
    is_enabled        BOOLEAN      NOT NULL DEFAULT TRUE,              -- 是否启用
    create_time       TIMESTAMPTZ  NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time       TIMESTAMPTZ  NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    PRIMARY KEY (user_id, notification_type, channel_id)
);

-- 通知记录表
-- 存储发送给用户的所有通知记录
CREATE TABLE notification.notifications
(
    id                SERIAL PRIMARY KEY,                             -- 通知ID
    user_id           UUID         NOT NULL,                          -- 用户ID
    notification_type VARCHAR(100) NOT NULL,                          -- 通知类型
    title             VARCHAR(255) NOT NULL,                          -- 通知标题
    content           TEXT,                                           -- 通知内容
    data              JSONB,                                          -- 通知相关数据
    link              VARCHAR(255),                                   -- 通知链接
    is_read           BOOLEAN      NOT NULL DEFAULT FALSE,            -- 是否已读
    read_at           TIMESTAMPTZ,                                    -- 阅读时间
    create_time       TIMESTAMPTZ  NOT NULL DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

CREATE INDEX idx_notifications_user ON notification.notifications (user_id, is_read, create_time DESC);
CREATE INDEX idx_notifications_type ON notification.notifications (notification_type, create_time DESC);
CREATE INDEX idx_notifications_create_time ON notification.notifications (create_time DESC);


-- 更新时间戳触发器
-- 自动更新记录的更新时间
CREATE TRIGGER update_notification_channels_timestamp
    BEFORE UPDATE
    ON notification.channels
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();

CREATE TRIGGER update_notification_templates_timestamp
    BEFORE UPDATE
    ON notification.templates
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();

CREATE TRIGGER update_notification_preferences_timestamp
    BEFORE UPDATE
    ON notification.user_preferences
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();


-- 初始通知渠道数据
-- 插入系统默认支持的通知渠道
INSERT INTO notification.channels (name, description, handler)
VALUES ('email', '电子邮件通知', 'email_handler'),
       ('push', '推送通知', 'push_handler'),
       ('sms', '短信通知', 'sms_handler'),
       ('in_app', '应用内通知', 'in_app_handler');

