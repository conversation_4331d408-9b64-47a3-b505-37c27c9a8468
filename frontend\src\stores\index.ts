// 导出所有store
export * from './user';
export * from './theme';
export * from './settings';
export * from './playground';

// 初始化函数
import { useThemeStore } from './theme';
import { useSettingsStore } from './settings';
import { useUserStore } from './user';
import { usePlaygroundStore } from './playground';

export function initializeStores() {
  const themeStore = useThemeStore();
  const settingsStore = useSettingsStore();
  const userStore = useUserStore();
  const playgroundStore = usePlaygroundStore();

  // 初始化主题
  themeStore.init();

  // 检查用户认证状态
  userStore.checkAuth();

  // 初始化playground
  playgroundStore.setHydrated(true);

  return {
    themeStore,
    settingsStore,
    userStore,
    playgroundStore,
  };
}
