"""
配置管理模块

该模块负责加载应用程序配置，按以下优先级处理配置项:
1. 环境变量
2. .env.local 文件（本地开发环境配置）
3. .env 文件（默认环境配置）

通过这种方式，开发者可以在不修改代码的情况下覆盖默认配置:
- 在生产环境中，通过环境变量设置配置
- 在开发环境中，通过 .env.local 文件覆盖默认配置
- 在任何环境中，都可以保持 .env 作为默认配置基准

这个模块使用 pydantic_settings 进行类型检查和验证。
"""

import asyncio
import sys
from pathlib import Path
from typing import List

from dotenv import load_dotenv
from pydantic_settings import BaseSettings, SettingsConfigDict

# 自动适配 Windows 事件循环
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# 获取项目根目录的绝对路径
ROOT_DIR = Path(__file__).parent.parent.parent.parent.absolute()
ENV_LOCAL_FILE = ROOT_DIR / ".env.local"
ENV_FILE = ROOT_DIR / ".env"

if ENV_LOCAL_FILE.exists():
    load_dotenv(dotenv_path=ENV_LOCAL_FILE, override=True)
    print(f"已加载 .env.local 文件并覆盖相同的环境变量")


class Settings(BaseSettings):
    # Database settings
    db_host: str
    db_port: int
    db_user: str
    db_password: str
    db_name: str
    db_type: str = "pg"  # "pg" or "mysql"

    # Server settings
    backend_port: int
    debug: bool = False

    # Security settings
    SECRET_KEY: str = "请修改为安全的随机字符串"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 10080
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    CORS_ORIGINS: List[str] = ["*"]

    # Auth settings
    MAX_LOGIN_ATTEMPTS: int = 5

    # 代理设置
    HTTP_PROXY: str | None = None
    HTTPS_PROXY: str | None = None
    NO_PROXY: str | None = None

    OPENAI_API_KEY: str | None = "sk-1unWSuwAQ6hoLg0aftlsxNXd4Af2VA3TJyWSqpdKVNjGT23M"
    OPENAI_API_URL: str | None = "https://api.chatanywhere.tech/v1"

    model_config = SettingsConfigDict(
        extra='allow',  # 允许额外的字段
        case_sensitive=False,  # 不区分大小写的环境变量
        env_file_encoding='utf-8'
    )


# 实例化配置对象
settings = Settings()
if __name__ == '__main__':
    print(settings.model_dump())
