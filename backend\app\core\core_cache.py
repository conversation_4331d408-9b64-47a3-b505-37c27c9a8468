import json
import pickle
from datetime import timedelta
from functools import wraps
from typing import Any, Callable, Dict, TypeVar

import redis
from fastapi import Request

from app.core.core_config import settings
from app.core.core_log import logger

T = TypeVar("T")

# Redis连接池
redis_client = redis.Redis(
    host=settings.redis_host,
    port=settings.redis_port,
    db=settings.redis_db,
    decode_responses=True,  # 字符串解码
    socket_timeout=5,  # 套接字超时
    socket_connect_timeout=5,  # 连接超时
    retry_on_timeout=True,  # 超时重试
)


class Cache:
    """
    缓存工具类
    """

    @staticmethod
    def key(*args: Any) -> str:
        """
        生成缓存键名
        
        Args:
            *args: 键名组成部分
            
        Returns:
            缓存键名
        """
        return ":".join(str(arg) for arg in args)

    @staticmethod
    def get(key: str) -> str | None:
        """
        获取字符串缓存
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值
        """
        try:
            return redis_client.get(key)
        except redis.RedisError as e:
            logger.error(f"Redis获取缓存失败: {e}")
            return None

    @staticmethod
    def set(
            key: str,
            value: str,
            expire: int | timedelta | None = None
    ) -> bool:
        """
        设置字符串缓存
        
        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间(秒)
            
        Returns:
            是否成功
        """
        try:
            return redis_client.set(key, value, ex=expire)
        except redis.RedisError as e:
            logger.error(f"Redis设置缓存失败: {e}")
            return False

    @staticmethod
    def delete(key: str) -> int:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            删除的键数量
        """
        try:
            return redis_client.delete(key)
        except redis.RedisError as e:
            logger.error(f"Redis删除缓存失败: {e}")
            return 0

    @staticmethod
    def exists(key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        try:
            return bool(redis_client.exists(key))
        except redis.RedisError as e:
            logger.error(f"Redis检查缓存失败: {e}")
            return False

    @staticmethod
    def get_object(key: str) -> Any | None:
        """
        获取对象缓存
        
        Args:
            key: 缓存键
            
        Returns:
            缓存对象
        """
        try:
            data = redis_client.get(key)
            if data:
                return pickle.loads(data.encode())
            return None
        except (redis.RedisError, pickle.PickleError) as e:
            logger.error(f"Redis获取对象缓存失败: {e}")
            return None

    @staticmethod
    def set_object(
            key: str,
            value: Any,
            expire: int | timedelta | None = None
    ) -> bool:
        """
        设置对象缓存
        
        Args:
            key: 缓存键
            value: 缓存对象
            expire: 过期时间(秒)
            
        Returns:
            是否成功
        """
        try:
            data = pickle.dumps(value)
            return redis_client.set(key, data, ex=expire)
        except (redis.RedisError, pickle.PickleError) as e:
            logger.error(f"Redis设置对象缓存失败: {e}")
            return False

    @staticmethod
    def get_json(key: str) -> Dict | None:
        """
        获取JSON缓存
        
        Args:
            key: 缓存键
            
        Returns:
            缓存JSON对象
        """
        try:
            data = redis_client.get(key)
            if data:
                return json.loads(data)
            return None
        except (redis.RedisError, json.JSONDecodeError) as e:
            logger.error(f"Redis获取JSON缓存失败: {e}")
            return None

    @staticmethod
    def set_json(
            key: str,
            value: Dict,
            expire: int | timedelta | None = None
    ) -> bool:
        """
        设置JSON缓存
        
        Args:
            key: 缓存键
            value: 缓存JSON对象
            expire: 过期时间(秒)
            
        Returns:
            是否成功
        """
        try:
            data = json.dumps(value)
            return redis_client.set(key, data, ex=expire)
        except (redis.RedisError, TypeError) as e:
            logger.error(f"Redis设置JSON缓存失败: {e}")
            return False

    @staticmethod
    def increment(key: str, amount: int = 1) -> int | None:
        """
        增加缓存值
        
        Args:
            key: 缓存键
            amount: 增加量
            
        Returns:
            增加后的值
        """
        try:
            return redis_client.incrby(key, amount)
        except redis.RedisError as e:
            logger.error(f"Redis增加缓存值失败: {e}")
            return None

    @staticmethod
    def decrement(key: str, amount: int = 1) -> int | None:
        """
        减少缓存值
        
        Args:
            key: 缓存键
            amount: 减少量
            
        Returns:
            减少后的值
        """
        try:
            return redis_client.decrby(key, amount)
        except redis.RedisError as e:
            logger.error(f"Redis减少缓存值失败: {e}")
            return None


def cache_response(expire: int = 3600, key_prefix: str = "api"):
    """
    缓存API响应装饰器
    
    Args:
        expire: 过期时间(秒)
        key_prefix: 缓存键前缀
        
    Returns:
        装饰函数
    """

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> Any:
            # 构建缓存键
            request = next((arg for arg in args if isinstance(arg, Request)), None)

            if not request:
                # 无法获取请求对象，直接执行原函数
                return await func(*args, **kwargs)

            # 使用URL和查询参数作为缓存键的一部分
            url_path = request.url.path
            query_string = str(request.query_params)
            cache_key = Cache.key(key_prefix, url_path, query_string)

            # 尝试从缓存获取结果
            cached_result = Cache.get_json(cache_key)
            if cached_result:
                return cached_result

            # 执行原函数
            result = await func(*args, **kwargs)

            # 存入缓存
            Cache.set_json(cache_key, result, expire)

            return result

        return wrapper

    return decorator
