import dayjs from 'dayjs';

/**
 * 格式化时间戳为年月日时分秒格式
 * @param timestamp 时间戳（秒或毫秒）
 * @returns 格式化后的时间字符串 YYYY-MM-DD HH:mm:ss
 */
export const formatTimestamp = (timestamp: number): string => {
  if (!timestamp || timestamp <= 0) {
    return dayjs().format('YYYY-MM-DD HH:mm:ss');
  }

  // 如果timestamp看起来已经是毫秒，就直接使用
  const ms = timestamp > 1000000000000 ? timestamp : timestamp * 1000;
  return dayjs(ms).format('YYYY-MM-DD HH:mm:ss');
};

/**
 * 格式化日期时间戳 - formatTimestamp 的别名，用于语义化
 * @param timestamp 时间戳（秒或毫秒）
 * @returns 格式化后的时间字符串 YYYY-MM-DD HH:mm:ss
 */
export const formatDateTime = formatTimestamp;
