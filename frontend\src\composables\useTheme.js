import { onMounted, ref, watch } from 'vue';

export function useTheme() {
  const isDark = ref(false);

  const toggleTheme = () => {
    isDark.value = !isDark.value;
    updateTheme();
  };

  const updateTheme = () => {
    if (isDark.value) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  onMounted(() => {
    // 检查本地存储中是否有保存的主题偏好
    const savedTheme = localStorage.getItem('theme');

    // 如果有保存的主题设置或者系统偏好是深色模式
    if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      isDark.value = true;
      updateTheme();
    }
  });

  watch(isDark, updateTheme);

  return {
    isDark,
    toggleTheme,
  };
}
