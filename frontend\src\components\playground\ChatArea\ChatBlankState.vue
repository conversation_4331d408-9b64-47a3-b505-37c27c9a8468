<template>
  <div class="flex flex-col items-center justify-center h-full min-h-80 text-center px-6 py-16">
    <!-- 主图标区域 -->
    <div class="mb-8">
      <div class="w-14 h-14 flex items-center justify-center rounded-xl shadow-sm bg-white dark:bg-[#2d2d2d]">
        <n-icon class="text-gray-800 dark:text-gray-300" size="28">
          <ChatbubbleEllipsesOutline />
        </n-icon>
      </div>
    </div>

    <!-- 主标题区域 -->
    <div class="mb-10 max-w-lg">
      <h1 class="text-xl font-medium mb-3 tracking-tight text-gray-900 dark:text-white">{{ $t('playground.welcome.title') }}</h1>
      <p class="text-sm leading-relaxed text-gray-700 dark:text-[#b0b0b0]">{{ $t('playground.welcome.subtitle') }}</p>
    </div>

    <!-- 状态指示器 -->
    <div class="flex items-center justify-center gap-6 mb-10">
      <div class="flex items-center gap-2">
        <div class="w-1.5 h-1.5 rounded-full transition-colors duration-300" :class="systemStatus.ready ? 'bg-green-400' : 'bg-gray-500'" />
        <span class="font-normal text-xs" :class="systemStatus.ready ? 'text-gray-800 dark:text-[#e0e0e0]' : 'text-gray-600 dark:text-[#888]'">
          {{ systemStatus.ready ? $t('playground.status.ready') : $t('playground.status.preparing') }}
        </span>
      </div>

      <div v-if="selectedAgent" class="flex items-center gap-2">
        <div class="w-3 h-3 rounded-full flex items-center justify-center bg-white dark:bg-[#404040]">
          <n-icon size="8" color="#6B7280" class="text-gray-800 dark:text-gray-400">
            <RocketOutline />
          </n-icon>
        </div>
        <span class="text-gray-700 dark:text-gray-400 font-normal text-xs">
          {{ selectedAgent.label }}
        </span>
      </div>
    </div>

    <!-- 建议问题区域 -->
    <div v-if="systemStatus.ready && selectedAgent" class="w-full max-w-xl">
      <div class="text-base font-medium mb-6 text-gray-900 dark:text-[#e0e0e0]">{{ $t('playground.welcome.suggestions') }}</div>

      <div class="grid grid-cols-1 gap-2">
        <button
          v-for="suggestion in suggestions"
          :key="suggestion.id"
          @click="$emit('send-suggestion', $t(`playground.welcome.suggestion${suggestion.id}`))"
          :disabled="isStreaming"
          class="group relative p-3 text-left rounded-lg transition-all duration-150 disabled:opacity-50 disabled:cursor-not-allowed bg-white dark:bg-[#2d2d2d] hover:bg-gray-50 dark:hover:bg-[#353535]"
          @mousedown.prevent.stop
          @selectstart.prevent
          @dragstart.prevent
        >
          <div class="flex items-center gap-3">
            <div class="flex-shrink-0 w-6 h-6 rounded flex items-center justify-center bg-gray-100 dark:bg-[#404040]">
              <n-icon size="12" class="text-gray-800 dark:text-gray-400">
                <component :is="suggestion.icon" />
              </n-icon>
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-sm font-normal group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors duration-150 text-gray-900 dark:text-white">
                {{ $t(`playground.welcome.suggestion${suggestion.id}`) }}
              </div>
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- 设置提示区域 -->
    <div v-else class="w-full max-w-xs">
      <!-- 系统初始化提示 -->
      <div v-if="!systemStatus.ready" class="rounded-lg p-5 bg-white dark:bg-[#2d2d2d]">
        <div class="flex flex-col items-center text-center">
          <div class="w-8 h-8 rounded flex items-center justify-center mb-3 bg-gray-100 dark:bg-[#404040]">
            <n-icon size="14" class="text-gray-800 dark:text-gray-400">
              <WarningOutline />
            </n-icon>
          </div>
          <h3 class="text-sm font-medium mb-2 text-gray-900 dark:text-white">{{ $t('playground.status.initializing') }}</h3>
          <p class="text-xs mb-3 text-gray-700 dark:text-[#b0b0b0]">{{ $t('playground.status.loadingConfig') }}</p>
          <div class="flex space-x-1">
            <div class="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 0ms"></div>
            <div class="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 150ms"></div>
            <div class="w-1 h-1 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 300ms"></div>
          </div>
        </div>
      </div>

      <!-- 选择智能体提示 -->
      <div v-else-if="!selectedAgent" class="rounded-lg p-5 bg-white dark:bg-[#2d2d2d]">
        <div class="flex flex-col items-center text-center">
          <div class="w-8 h-8 rounded flex items-center justify-center mb-3 bg-gray-100 dark:bg-[#404040]">
            <n-icon size="14" class="text-gray-800 dark:text-gray-400">
              <RocketOutline />
            </n-icon>
          </div>
          <h3 class="text-sm font-medium mb-2 text-gray-900 dark:text-white">{{ $t('playground.welcome.selectAgent') }}</h3>
          <p class="text-xs mb-4 text-gray-700 dark:text-[#b0b0b0]">{{ $t('playground.welcome.selectAgentHint') }}</p>
          <button
            @click="$emit('open-sidebar')"
            class="px-3 py-1.5 text-xs font-medium rounded transition-all duration-150 bg-gray-800 text-white hover:bg-gray-900 dark:bg-white dark:text-gray-800 dark:hover:bg-gray-100 shadow-sm hover:shadow-md"
          >
            {{ $t('playground.welcome.selectAgentButton') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { NButton, NIcon } from 'naive-ui';
import {
  BulbOutline,
  ChatbubbleEllipsesOutline,
  CodeSlashOutline,
  DocumentTextOutline,
  InformationCircleOutline,
  RocketOutline,
  SearchOutline,
  SparklesOutline,
  WarningOutline,
} from '@vicons/ionicons5';
import { useI18n } from 'vue-i18n';

interface Props {
  systemStatus: { ready: boolean };
  selectedAgent?: { label: string } | null;
  isStreaming?: boolean;
}

interface Emits {
  (e: 'send-suggestion', suggestion: string): void;
  (e: 'open-sidebar'): void;
}

const props = withDefaults(defineProps<Props>(), { isStreaming: false });
const emit = defineEmits<Emits>();
const { t } = useI18n();

const suggestions = [
  { id: '1', icon: BulbOutline },
  { id: '2', icon: CodeSlashOutline },
  { id: '3', icon: DocumentTextOutline },
  { id: '4', icon: SearchOutline },
];
</script>
