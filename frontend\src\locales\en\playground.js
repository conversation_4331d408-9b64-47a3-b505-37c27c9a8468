export default {
  title: 'AI Chat Playground',
  description: 'Experience intelligent conversations and start your personalized AI assistant journey!',
  sidebar: {
    agent: 'Agent',
    newChat: 'New Chat',
    agents: 'Agents',
    collapseLabel: 'Collapse Sidebar',
    expandLabel: 'Expand Sidebar',
    homeLabel: 'Go to Home',
  },
  chat: {
    inputPlaceholder: 'Enter your question... (Shift+Enter for new line)',
    send: 'Send',
    stop: 'Stop',
    characterCount: '{count}/2000',
    sendShortcut: 'Enter',
    sendLabel: 'Send',
    stopGeneration: 'Stop Generation',
    sendFailed: 'Failed to send message: {error}',
    generationStopped: 'Generation stopped',
  },
  messages: {
    thinking: 'Thinking...',
    error: 'Error occurred',
    retry: 'Retry',
    copy: 'Copy',
    copied: 'Copied',
  },
  sessions: {
    title: 'Recent Conversations',
    loading: 'Loading conversations',
    empty: 'No conversation records yet',
    emptyHint: 'Click the "New Chat" button above\nto start your first conversation',
    delete: 'Delete Session',
    rename: '<PERSON><PERSON>',
  },
  welcome: {
    title: 'Your First AI Agent',
    subtitle: 'Choose an agent model to start your AI conversation experience',
    suggestions: 'Try these questions',
    suggestion1: 'Latest BTC market analysis',
    suggestion2: 'Help me write a Python script',
    suggestion3: 'Explain quantum computing principles',
    suggestion4: 'Recommend learning resources',
    selectAgent: 'Select Agent',
    selectAgentHint: 'Choose a model from the sidebar to start',
    selectAgentButton: 'Select Agent',
  },
  status: {
    ready: 'Ready',
    preparing: 'System preparing',
    initializing: 'System initializing',
    loadingConfig: 'Loading agent configuration',
    textSupport: 'Currently supports text chat',
  },
};
