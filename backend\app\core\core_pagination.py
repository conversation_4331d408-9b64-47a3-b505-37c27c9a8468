from typing import Any, Generic, List, Type, TypeVar

from fastapi import Query
from pydantic import BaseModel
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import InstrumentedAttribute

T = TypeVar("T")


class PageParams:
    """
    分页参数
    """

    def __init__(
            self,
            page: int = Query(1, ge=1, description="页码，从1开始"),
            size: int = Query(20, ge=1, le=100, description="每页条数，最大100"),
    ):
        self.page = page
        self.size = size
        self.offset = (page - 1) * size


class PageResponse(BaseModel, Generic[T]):
    """
    分页响应模型
    """
    total: int
    page: int
    size: int
    items: List[T]

    class Config:
        arbitrary_types_allowed = True


async def paginate(
        db: AsyncSession,
        model: Type[T],
        page_params: PageParams,
        filters: List[Any] | None = None,
        sorts: List[Any] | None = None,
        schema: Type[BaseModel] | None = None,
) -> PageResponse:
    """
    通用分页函数
    
    Args:
        db: 数据库会话
        model: SQLAlchemy模型
        page_params: 分页参数
        filters: 过滤条件列表
        sorts: 排序条件列表
        schema: Pydantic响应模型
        
    Returns:
        分页响应对象
    """
    filters = filters or []
    sorts = sorts or []

    # 查询总数
    count_query = select(func.count()).select_from(model)
    if filters:
        count_query = count_query.filter(*filters)
    total_count = await db.scalar(count_query)

    # 查询数据
    query = select(model)
    if filters:
        query = query.filter(*filters)
    if sorts:
        query = query.order_by(*sorts)

    query = query.offset(page_params.offset).limit(page_params.size)
    result = await db.execute(query)
    items = result.scalars().all()

    # 返回结果
    if schema:
        items = [schema.model_validate(item) for item in items]

    return PageResponse(
        total=total_count,
        page=page_params.page,
        size=page_params.size,
        items=items,
    )


def parse_order_by(order_by: str | None, model: Type[T]) -> List[Any]:
    """
    解析排序参数
    
    Args:
        order_by: 排序字符串，例如"id:desc,name:asc"
        model: SQLAlchemy模型
        
    Returns:
        排序条件列表
    """
    if not order_by:
        return []

    sorts = []
    for item in order_by.split(","):
        parts = item.strip().split(":")
        field_name = parts[0]
        direction = parts[1] if len(parts) > 1 else "asc"

        # 获取模型字段
        field = getattr(model, field_name, None)
        if not field or not isinstance(field, InstrumentedAttribute):
            continue

        # 添加排序方向
        if direction.lower() == "desc":
            sorts.append(field.desc())
        else:
            sorts.append(field.asc())

    return sorts
