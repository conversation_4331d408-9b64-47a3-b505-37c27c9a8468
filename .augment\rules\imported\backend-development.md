---
type: "always_apply"
---

# 后端开发规范 

## 核心技术栈
- 编程语言: Python 3.12+
- 核心框架: FastAPI, Agno
- 数据库: PostgreSQL, TimescaleDB
- ORM: SQLModel
- 数据验证: Pydantic
- 认证授权: JWT, OAuth2

## 项目结构
- 后端 ([backend/](mdc:backend))
  - `app/`: 核心应用代码
    - `api/v1/`: API路由和端点
      - [endpoint_auth.py](mdc:backend/app/api/v1/endpoint_auth.py) - 认证相关API
      - [endpoint_users.py](mdc:backend/app/api/v1/endpoint_users.py) - 用户管理API
      - [endpoint_agents.py](mdc:backend/app/api/v1/endpoint_agents.py) - AI代理API
      - [endpoint_playground.py](mdc:backend/app/api/v1/endpoint_playground.py) - 游乐场API
    - [core/]: 核心配置、安全、依赖注入
    - `db/`: 数据库连接和会话管理
    - `models/`: 数据模型定义
      - `auth/`: 认证相关模型
      - `crypto/`: 加密货币相关模型
      - `notification/`: 通知模型
      - `rbac/`: 基于角色的访问控制模型
    - `services/`: 业务逻辑服务
    - `utils/`: 工具函数
  - `agents/`: 核心AI代理开发框架
  - `quant/`: 量化交易相关代码
  - [main.py](mdc:backend/main.py) - 应用入口点
  - [requirements.txt](mdc:backend/requirements.txt) - Python依赖

## 开发最佳实践
- 基本要求:
  - 遵循FastAPI最佳实践
  - 使用依赖注入模式
  - 统一处理异常
- 认证与授权:
  - 遵循OAuth2标准
  - 密码进行安全加密存储

## API设计原则
- 权限控制:
  - 采用基于角色的访问控制 (RBAC)
  - 实现路由级别和资源级别权限检查
- 请求头规范:
  - `Authorization`: Bearer <jwt_token>
  - `Content-Type`: application/json
  - `Accept`: application/json

## 统一错误处理
- HTTP状态码:
  - 200: 成功
  - 201: 创建成功
  - 400: 请求参数错误
  - 401: 未认证
  - 403: 权限不足
  - 404: 资源不存在
  - 422: 数据验证失败
  - 500: 服务器内部错误
- 内部错误代码:
  - VALIDATION_ERROR: 数据验证错误
  - AUTHENTICATION_ERROR: 认证错误
  - AUTHORIZATION_ERROR: 授权错误
  - RESOURCE_NOT_FOUND: 资源不存在
  - BUSINESS_ERROR: 业务逻辑错误
  - SYSTEM_ERROR: 系统错误

## 数据库设计
- SQL脚本 ([backend/sql/](mdc:backend/sql)):
  - [00_base.sql](mdc:backend/sql/db/00_base.sql) - 基础结构
  - [01_auth.sql](mdc:backend/sql/db/01_auth.sql) - 认证表
  - [02_rbac.sql](mdc:backend/sql/db/02_rbac.sql) - RBAC表
  - [03_notification.sql](mdc:backend/sql/db/03_notification.sql) - 通知表
  - [04_crypto.sql](mdc:backend/sql/db/04_crypto.sql) - 加密货币表
  - [05_kline.sql](mdc:backend/sql/db/05_kline.sql) - K线数据表
  - [06_apikey.sql](mdc:backend/sql/db/06_apikey.sql) - API密钥表
- 视图与优化:
  - [07_create_kline_materialized_view.sql](mdc:backend/sql/db/07_create_kline_materialized_view.sql) - K线物化视图
  - [08_create_kline_indicator_history_view.sql](mdc:backend/sql/db/08_create_kline_indicator_history_view.sql) - 历史指标视图
  - [09_create_v_kline_indicator_real_view.sql](mdc:backend/sql/db/09_create_v_kline_indicator_real_view.sql) - 实时指标视图
  - [99_performance_tuning.sql](mdc:backend/sql/99_performance_tuning.sql) - 性能优化

## 核心数据模型 ([backend/app/models/](mdc:backend/app/models))
- 认证模块
  - [model_user.py](mdc:backend/app/models/auth/model_user.py) - 用户模型
  - [model_login.py](mdc:backend/app/models/auth/model_login.py) - 登录记录
  - [model_password.py](mdc:backend/app/models/auth/model_password.py) - 密码管理
  - [model_token.py](mdc:backend/app/models/auth/model_token.py) - Token管理
- RBAC权限模块
  - [model_role.py](mdc:backend/app/models/rbac/model_role.py) - 角色模型
  - [model_permission.py](mdc:backend/app/models/rbac/model_permission.py) - 权限模型
  - [model_user_role.py](mdc:backend/app/models/rbac/model_user_role.py) - 用户角色关联
  - [model_role_permission.py](mdc:backend/app/models/rbac/model_role_permission.py) - 角色权限关联
- 加密货币模块
  - [model_symbol.py](mdc:backend/app/models/crypto/model_symbol.py) - 交易对模型
  - [model_ticker.py](mdc:backend/app/models/crypto/model_ticker.py) - 市场行情
  - [model_kline.py](mdc:backend/app/models/crypto/model_kline.py) - K线数据
  - [model_apikey.py](mdc:backend/app/models/crypto/model_apikey.py) - API密钥管理
- 通知模块
  - [model_notification.py](mdc:backend/app/models/notification/model_notification.py) - 通知模型
  - [model_channel.py](mdc:backend/app/models/notification/model_channel.py) - 通知渠道
  - [model_template.py](mdc:backend/app/models/notification/model_template.py) - 通知模板
  - [model_preference.py](mdc:backend/app/models/notification/model_preference.py) - 用户偏好

## 数据库连接和会话管理
- 连接管理 ([backend/app/db/](mdc:backend/app/db))
  - [db_session.py](mdc:backend/app/db/db_session.py) - 数据库会话管理
  - [db_session_manager.py](mdc:backend/app/db/db_session_manager.py) - 会话管理器
  - [db_orm_base.py](mdc:backend/app/db/db_orm_base.py) - ORM基础类
  - [db_orm_base_model.py](mdc:backend/app/db/db_orm_base_model.py) - 基础模型类

