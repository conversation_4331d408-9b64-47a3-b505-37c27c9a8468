import asyncio
import functools
from contextlib import asynccontextmanager, contextmanager
from typing import Any, As<PERSON><PERSON><PERSON>ator, Callable, Generator, TypeVar

from loguru import logger
from sqlalchemy import text
from sqlalchemy.engine import Engine, create_engine
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import Session, sessionmaker

T = TypeVar('T')
P = TypeVar('P', bound=Callable[..., Any])


class DBSessionManager:
    """Database session manager for synchronous and asynchronous sessions."""

    def __init__(self, db_host: str, db_port: int, db_user: str, db_password: str, db_name: str, db_type: str = "pg", *args, **kwargs):
        self.db_host = db_host
        self.db_port = db_port
        self.db_user = db_user
        self.db_password = db_password
        self.db_name = db_name
        self.db_type = db_type
        self._sync_engine: Engine | None = None
        self._async_engine: AsyncEngine | None = None

    def _get_db_driver(self, is_async: bool = False) -> str:
        """Determine the database driver based on database type and async mode."""
        drivers = {
            "pg": "postgresql+psycopg_async" if is_async else "postgresql+psycopg",
            "mysql": "mysql+aiomysql" if is_async else "mysql+pymysql",
        }
        if self.db_type not in drivers:
            raise ValueError(f"Unsupported database type: {self.db_type}")
        return drivers[self.db_type]

    def get_database_url(self, is_async: bool) -> str:
        """Construct the database connection URL."""
        driver = self._get_db_driver(is_async=is_async)
        return f"{driver}://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"

    def _create_engine(self, is_async: bool) -> Engine | AsyncEngine:
        """Create and return a database engine instance."""
        engine_creator = create_async_engine if is_async else create_engine
        url = self.get_database_url(is_async=is_async)
        return engine_creator(
            url,
            echo=False,
            pool_size=10,
            max_overflow=20
        )

    def get_sync_engine(self) -> Engine:
        """Get the synchronous engine instance, creating it if it doesn't exist."""
        if not self._sync_engine:
            self._sync_engine = self._create_engine(False)
        return self._sync_engine

    def get_async_engine(self) -> AsyncEngine:
        """Get the asynchronous engine instance, creating it if it doesn't exist."""
        if not self._async_engine:
            self._async_engine = self._create_engine(True)
        return self._async_engine

    @contextmanager
    def session(self, autoflush: bool = True) -> Generator[Session, None, None]:
        """Synchronous session context manager."""
        session_factory = sessionmaker(
            bind=self.get_sync_engine(),
            expire_on_commit=False,
            autoflush=autoflush
        )
        session = session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Session error: {e}")
            raise
        finally:
            session.close()

    @asynccontextmanager
    async def async_session(self, autoflush: bool = True) -> AsyncGenerator[AsyncSession, None]:
        """Asynchronous session context manager."""
        session_factory = async_sessionmaker(
            bind=self.get_async_engine(),
            class_=AsyncSession,
            expire_on_commit=False,
            autoflush=autoflush
        )
        async with session_factory() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                logger.error(f"Session error: {e}")
                raise

    def __call__(self, func=None, **kwargs):
        """Decorator to provide a database session to a function."""
        # 支持 autoflush 和 auto_flush 两种参数名
        autoflush = True
        if "autoflush" in kwargs:
            autoflush = kwargs["autoflush"]
        elif "auto_flush" in kwargs:
            autoflush = kwargs["auto_flush"]

        def decorator(fn):  # 将func改为fn
            if asyncio.iscoroutinefunction(fn):
                @functools.wraps(fn)
                async def async_wrapper(*args, **fn_kwargs):  # 将kwargs改为fn_kwargs
                    if 'session' in fn_kwargs and fn_kwargs['session'] is not None:
                        return await fn(*args, **fn_kwargs)
                    async with self.async_session(autoflush=autoflush) as session:
                        fn_kwargs["session"] = session
                        return await fn(*args, **fn_kwargs)

                return async_wrapper
            else:
                @functools.wraps(fn)
                def sync_wrapper(*args, **fn_kwargs):  # 将kwargs改为fn_kwargs
                    if 'session' in fn_kwargs and fn_kwargs['session'] is not None:
                        return fn(*args, **fn_kwargs)
                    with self.session(autoflush=autoflush) as session:
                        fn_kwargs["session"] = session
                        return fn(*args, **fn_kwargs)

                return sync_wrapper

        if func is None:
            return decorator
        return decorator(func)

    async def check_connection(self) -> bool:
        """Check if the database connection is available."""
        try:
            async with self.async_session() as session:
                await session.execute(text("SELECT 1"))
            return True
        except Exception as e:
            logger.error(f"Database connection check failed: {e}")
            return False

    async def close(self) -> None:
        """Close all database connections."""
        if self._async_engine:
            await self._async_engine.dispose()
            self._async_engine = None
        if self._sync_engine:
            self._sync_engine.dispose()
            self._sync_engine = None
        logger.info(f"Database connection closed: {self}")

    def get_db(self) -> Callable[[], Generator[Session, None, None]]:
        """Dependency for FastAPI to inject a synchronous database session."""

        def _get_db() -> Generator[Session, None, None]:
            with self.session() as session:
                yield session

        return _get_db

    def get_async_db(self) -> Callable[[], AsyncGenerator[AsyncSession, None]]:
        """Dependency for FastAPI to inject an asynchronous database session."""

        async def _get_async_db() -> AsyncGenerator[AsyncSession, None]:
            async with self.async_session() as session:
                yield session

        return _get_async_db
