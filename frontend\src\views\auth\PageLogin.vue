<template>
  <div class="min-h-screen flex flex-col bg-gradient-to-br from-gray-50 to-gray-100 dark:from-dark dark:to-dark-secondary transition-colors duration-300 relative overflow-hidden">
    <div class="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <!-- 装饰性背景元素 -->
      <div class="absolute top-0 left-0 w-full h-64 bg-primary opacity-5 dark:opacity-10 dark:bg-gradient-to-r dark:from-primary dark:to-primary-light transform -skew-y-6"></div>
      <div class="absolute bottom-0 right-0 w-full h-64 bg-accent opacity-5 dark:opacity-10 dark:bg-gradient-to-r dark:from-accent dark:to-primary transform skew-y-6"></div>
      <div class="max-w-md w-full space-y-8 relative z-10">
        <div class="text-center animate-fade-in">
          <h1 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
            {{ $t('auth.login.title') }}
          </h1>
          <div class="text-sm text-gray-600 dark:text-gray-300 mt-2">
            {{ $t('auth.login.actions.noAccount') }}
            <router-link :to="{ name: 'Register' }" class="text-primary hover:text-accent dark:text-primary-light dark:hover:text-primary transition">
              {{ $t('auth.login.actions.register') }}
            </router-link>
          </div>
        </div>
        <form
          class="mt-8 space-y-6 bg-white dark:bg-dark-secondary p-8 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 animate-fade-in animation-delay-100"
          @submit.prevent="handleLogin"
        >
          <div v-if="loginError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline">{{ loginError }}</span>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" for="email">{{ $t('auth.login.fields.email') }}</label>
              <input
                id="email"
                v-model="email"
                :placeholder="$t('auth.login.fields.email')"
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm dark:bg-dark-alt transition-colors duration-300"
                name="email"
                required
                type="email"
                autocomplete="email"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" for="password">{{ $t('auth.login.fields.password') }}</label>
              <input
                id="password"
                v-model="password"
                :placeholder="$t('auth.login.fields.password')"
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm dark:bg-dark-alt transition-colors duration-300"
                name="password"
                required
                type="password"
                autocomplete="current-password"
              />
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember-me"
                v-model="rememberMe"
                class="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-700 rounded"
                name="remember-me"
                type="checkbox"
              />
              <label class="ml-2 block text-sm text-gray-900 dark:text-gray-300" for="remember-me">
                {{ $t('auth.login.actions.rememberMe') }}
              </label>
            </div>

            <div class="text-sm">
              <a class="font-medium text-primary hover:text-accent dark:text-primary-light dark:hover:text-primary transition-colors" href="#">
                {{ $t('auth.login.actions.forgotPassword') }}
              </a>
            </div>
          </div>

          <div>
            <button
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-accent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:bg-primary-light dark:hover:bg-primary transition-all duration-300 shadow-md hover:shadow-lg hover:scale-105"
              type="submit"
              :disabled="isLoading"
            >
              <span v-if="isLoading" class="mr-2">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </span>
              {{ $t('auth.login.actions.signIn') }}
            </button>
          </div>
        </form>
        <div class="h-20"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';

const router = useRouter();
const userStore = useUserStore();

const email = ref('');
const password = ref('');
const rememberMe = ref(false);
const isLoading = ref(false);
const loginError = ref('');

const handleLogin = async () => {
  if (isLoading.value) return;

  try {
    isLoading.value = true;
    loginError.value = '';

    const success = await userStore.login({
      username: email.value,
      password: password.value,
    });

    if (success) {
      await router.push({ name: 'UserProfile' });
    } else {
      loginError.value = '登录失败，请检查邮箱和密码';
    }
  } catch (error) {
    console.error('登录错误:', error);
    loginError.value = error instanceof Error ? error.message : '登录时发生错误，请稍后再试';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.animate-fade-in {
  opacity: 0;
  transform: translateY(10px);
  animation: fadeIn 0.6s ease-out forwards;
}

.animation-delay-100 {
  animation-delay: 0.1s;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
