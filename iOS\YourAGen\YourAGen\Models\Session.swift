import Foundation

struct Session: Codable, Identifiable {
    let id: String
    let sessionId: String
    let title: String
    let agentId: String?
    let agentName: String?
    let messageCount: Int
    let lastMessage: String?
    let createdAt: TimeInterval
    let updatedAt: TimeInterval
    
    enum CodingKeys: String, CodingKey {
        case id
        case sessionId = "session_id"
        case title
        case agentId = "agent_id"
        case agentName = "agent_name"
        case messageCount = "message_count"
        case lastMessage = "last_message"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // 处理id字段：优先使用id，如果没有则使用session_id
        if let id = try? container.decode(String.self, forKey: .id) {
            self.id = id
        } else if let sessionId = try? container.decode(String.self, forKey: .sessionId) {
            self.id = sessionId
        } else {
            throw DecodingError.keyNotFound(CodingKeys.id, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Neither 'id' nor 'session_id' found"))
        }
        
        // sessionId字段
        self.sessionId = try container.decode(String.self, forKey: .sessionId)
        
        // title字段：优先使用title，如果没有则使用默认值
        self.title = (try? container.decode(String.self, forKey: .title)) ?? "新对话"
        
        // 可选字段，提供默认值
        self.agentId = try? container.decode(String.self, forKey: .agentId)
        self.agentName = try? container.decode(String.self, forKey: .agentName)
        self.messageCount = (try? container.decode(Int.self, forKey: .messageCount)) ?? 0
        self.lastMessage = try? container.decode(String.self, forKey: .lastMessage)
        
        // 时间戳处理：支持字符串和数字格式
        if let createdAtString = try? container.decode(String.self, forKey: .createdAt),
           let createdAtDouble = Double(createdAtString) {
            self.createdAt = createdAtDouble
        } else if let createdAtDouble = try? container.decode(Double.self, forKey: .createdAt) {
            self.createdAt = createdAtDouble
        } else if let createdAtInt = try? container.decode(Int.self, forKey: .createdAt) {
            self.createdAt = Double(createdAtInt)
        } else {
            self.createdAt = Date().timeIntervalSince1970
        }
        
        // updatedAt字段：如果没有则使用createdAt
        if let updatedAtString = try? container.decode(String.self, forKey: .updatedAt),
           let updatedAtDouble = Double(updatedAtString) {
            self.updatedAt = updatedAtDouble
        } else if let updatedAtDouble = try? container.decode(Double.self, forKey: .updatedAt) {
            self.updatedAt = updatedAtDouble
        } else if let updatedAtInt = try? container.decode(Int.self, forKey: .updatedAt) {
            self.updatedAt = Double(updatedAtInt)
        } else {
            self.updatedAt = self.createdAt
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(sessionId, forKey: .sessionId)
        try container.encode(title, forKey: .title)
        try container.encodeIfPresent(agentId, forKey: .agentId)
        try container.encodeIfPresent(agentName, forKey: .agentName)
        try container.encode(messageCount, forKey: .messageCount)
        try container.encodeIfPresent(lastMessage, forKey: .lastMessage)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
    }
    
    // 便利初始化器
    init(sessionId: String, title: String, agentId: String? = nil, agentName: String? = nil, messageCount: Int = 0, lastMessage: String? = nil, createdAt: TimeInterval? = nil, updatedAt: TimeInterval? = nil) {
        self.id = sessionId
        self.sessionId = sessionId
        self.title = title
        self.agentId = agentId
        self.agentName = agentName
        self.messageCount = messageCount
        self.lastMessage = lastMessage
        let now = Date().timeIntervalSince1970
        self.createdAt = createdAt ?? now
        self.updatedAt = updatedAt ?? self.createdAt
    }
}

// 会话响应数据结构
struct SessionResponse: Codable {
    let success: Bool
    let data: SessionData
    let message: String?
    let code: Int?
}

struct SessionData: Codable {
    let items: [Session]
    let total: Int
    let page: Int
    let size: Int
    let pages: Int
}

// 会话详情响应
struct SessionDetailResponse: Codable {
    let success: Bool
    let data: [Message]
    let message: String?
    let code: Int?
} 