import { AnalyticsOutline, LanguageOutline } from '@vicons/ionicons5';

export interface AgentData {
  id: number;
  key: string;
  icon: any;
  avatarBg: string;
  rating: number;
  users: number;
  conversations: number;
}

export const agentsData: AgentData[] = [
  {
    id: 1,
    key: 'cryptoAnalyst',
    icon: AnalyticsOutline,
    avatarBg: 'bg-orange-500',
    rating: 4.7,
    users: 850,
    conversations: 12500,
  },
  {
    id: 2,
    key: 'multilingualTranslator',
    icon: LanguageOutline,
    avatarBg: 'bg-indigo-500',
    rating: 4.9,
    users: 3000,
    conversations: 45000,
  },
];
