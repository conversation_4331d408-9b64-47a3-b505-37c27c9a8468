<template>
  <div class="token-usage-container">
    <!-- 简化版本 - 只显示总token数 -->
    <div v-if="!showDetails" class="token-simple">
      <div class="flex items-center justify-center gap-1">
        <n-icon size="10" class="text-gray-500 dark:text-gray-400 flex-shrink-0" style="line-height: 1;">
          <StatsChartOutline />
        </n-icon>
        <span class="text-[10px] font-medium leading-none" :class="getTokenUsageColor(tokenStats.total_tokens)">
          {{ formatTokenCount(tokenStats.total_tokens) }}
        </span>
        <span class="text-[10px] text-gray-700 dark:text-gray-500 leading-none">tokens</span>
      </div>
    </div>

    <!-- 详细版本 - 显示输入/输出token分解 -->
    <div v-else class="token-detailed">
      <div class="flex items-center justify-center gap-1.5">
        <n-icon size="10" class="text-gray-500 dark:text-gray-400 flex-shrink-0" style="line-height: 1;">
          <StatsChartOutline />
        </n-icon>
        <div class="flex items-center gap-2 text-[10px] leading-none">
          <!-- 输入tokens -->
          <div class="flex items-center gap-0.5">
            <span class="text-gray-700 dark:text-gray-500">输入:</span>
            <span class="font-medium text-blue-400">
              {{ formatTokenCount(tokenStats.prompt_tokens) }}
            </span>
          </div>

          <!-- 输出tokens -->
          <div class="flex items-center gap-0.5">
            <span class="text-gray-700 dark:text-gray-500">输出:</span>
            <span class="font-medium text-green-400">
              {{ formatTokenCount(tokenStats.completion_tokens) }}
            </span>
          </div>

          <!-- 总计 -->
          <div class="flex items-center gap-0.5">
            <span class="text-gray-700 dark:text-gray-500">总计:</span>
            <span class="font-medium" :class="getTokenUsageColor(tokenStats.total_tokens)">
              {{ formatTokenCount(tokenStats.total_tokens) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Tooltip显示详细信息 -->
    <n-tooltip v-if="!showDetails && tokenStats.total_tokens > 0" trigger="hover">
      <template #trigger>
        <div class="cursor-help w-full h-full absolute inset-0"></div>
      </template>
      <div class="text-[10px] space-y-1">
        <div>输入 tokens: {{ tokenStats.prompt_tokens.toLocaleString() }}</div>
        <div>输出 tokens: {{ tokenStats.completion_tokens.toLocaleString() }}</div>
        <div class="border-t border-gray-600 pt-1">总计: {{ tokenStats.total_tokens.toLocaleString() }}</div>
      </div>
    </n-tooltip>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { NIcon, NTooltip } from 'naive-ui';
import { StatsChartOutline } from '@vicons/ionicons5';
import type { PlaygroundChatMessage } from '@/types/playground';
import { calculateMessageTokens, calculateTotalTokens, formatTokenCount, getTokenUsageColor, type TokenStats } from '@/utils/tokenUtils';

interface Props {
  // 单条消息的token统计
  message?: PlaygroundChatMessage;
  // 多条消息的token统计
  messages?: PlaygroundChatMessage[];
  // 是否显示详细信息
  showDetails?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showDetails: false,
});

// 计算token统计
const tokenStats = computed((): TokenStats => {
  if (props.message) {
    return calculateMessageTokens(props.message);
  } else if (props.messages) {
    return calculateTotalTokens(props.messages);
  }
  return {
    prompt_tokens: 0,
    completion_tokens: 0,
    total_tokens: 0,
  };
});
</script>

<style scoped>
.token-usage-container {
  @apply relative;
}

.token-simple {
  @apply flex items-center;
}

.token-detailed {
  @apply flex items-center;
}
</style>
