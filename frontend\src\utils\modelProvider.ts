// 模型提供商映射到图标类型
const providerIconMap: Record<string, string> = {
  openai: 'open-ai',
  anthropic: 'anthropic',
  google: 'gemini',
  mistral: 'mistral',
  ollama: 'ollama',
  azure: 'azure',
  cohere: 'cohere',
  aws: 'aws',
  deepseek: 'deepseek',
  fireworks: 'fireworks',
  groq: 'groq',
  xai: 'xai',
};

// 根据提供商名称获取对应的图标类型
export const getProviderIcon = (provider: string): string | null => {
  // 转换为小写以实现不区分大小写的匹配
  const providerLower = provider.toLowerCase();

  // 直接匹配
  if (providerLower in providerIconMap) {
    return providerIconMap[providerLower];
  }

  // 部分匹配
  for (const [key, value] of Object.entries(providerIconMap)) {
    if (providerLower.includes(key)) {
      return value;
    }
  }

  // 没有匹配则返回null
  return null;
};

// 获取提供商颜色
export const getProviderColor = (provider: string): string => {
  const colors: Record<string, string> = {
    openai: '#10B981',
    anthropic: '#F59E0B',
    google: '#3B82F6',
    mistral: '#EF4444',
    ollama: '#8B5CF6',
    azure: '#0078D4',
    cohere: '#FF6B6B',
    aws: '#FF9900',
    deepseek: '#6366F1',
    fireworks: '#F97316',
    groq: '#84CC16',
    xai: '#6B7280',
  };

  const providerLower = provider.toLowerCase();
  return colors[providerLower] || colors['default'] || '#6B7280';
};

// 获取提供商显示名称
export const getProviderDisplayName = (provider: string): string => {
  const names: Record<string, string> = {
    openai: 'OpenAI',
    anthropic: 'Anthropic',
    google: 'Google',
    mistral: 'Mistral AI',
    ollama: 'Ollama',
    azure: 'Azure OpenAI',
    cohere: 'Cohere',
    aws: 'AWS Bedrock',
    deepseek: 'DeepSeek',
    fireworks: 'Fireworks AI',
    groq: 'Groq',
    xai: 'xAI',
  };

  const providerLower = provider.toLowerCase();
  return names[providerLower] || provider;
};
