import inspect
import json
from logging import getLogger
from typing import List, Optional, Tuple, Any, Dict

from agno.agent import Agent
from agno.app.playground.utils import (
    process_audio,
    process_document,
    process_image,
    process_video,
)
from agno.media import Audio, Image, Video
from agno.media import File as FileMedia
from agno.team import Team
from agno.workflow import Workflow
from fastapi import HTTPException, UploadFile
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field

from ai.registry import agents_map

logger = getLogger(__name__)

# 文件类型处理器映射
FILE_TYPE_PROCESSORS = {
    "image": (["image/png", "image/jpeg", "image/jpg", "image/webp"], process_image),
    "audio": (["audio/wav", "audio/mp3", "audio/mpeg"], process_audio),
    "video": (
        [
            "video/x-flv",
            "video/quicktime",
            "video/mpeg",
            "video/mpegs",
            "video/mpgs",
            "video/mpg",
            "video/mp4",
            "video/webm",
            "video/wmv",
            "video/3gpp",
        ],
        process_video,
    ),
    "document": (
        [
            "application/pdf",
            "text/csv",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "text/plain",
            "application/json",
        ],
        process_document,
    ),
}


class EntityInfo(BaseModel):
    """统一的实体信息模型"""

    entity_id: str = Field(..., description="实体ID")
    name: str = Field(..., description="实体名称")
    description: str = Field(..., description="实体描述")
    entity_type: str = Field(..., description="实体类型")


def create_json_response(data: Dict) -> str:
    """创建JSON响应字符串"""
    return json.dumps(data) + "\n"


######################################################
## 流式响应处理
######################################################


def create_streaming_response(generator):
    """创建流式响应，优化HTTP/2兼容性"""
    return StreamingResponse(
        generator,
        media_type="text/event-stream",
        headers={
            "Content-Type": "text/event-stream; charset=utf-8",
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
            "Pragma": "no-cache",
            "Expires": "0",
            "Access-Control-Allow-Origin": "*",
        },
    )


async def generic_chat_response_streamer(
        entity,
        message: str,
        images: Optional[List[Image]] = None,
        audio: Optional[List[Audio]] = None,
        videos: Optional[List[Video]] = None,
        files: Optional[List[FileMedia]] = None,
):
    """通用的聊天响应流处理器"""
    run_response = await entity.arun(
        message,
        session_id=entity.session_id,
        user_id=entity.user_id,
        images=images,
        audio=audio,
        videos=videos,
        files=files,
        stream=True,
        stream_intermediate_steps=True,
    )
    async for run_response_chunk in run_response:
        yield run_response_chunk.to_json() + "\n"


######################################################
## 文件处理
######################################################


async def process_uploaded_files(
        files: Optional[List[UploadFile]],
) -> Tuple[List[Image], List[Audio], List[Video], List[FileMedia]]:
    """处理上传的文件"""
    if not files:
        return [], [], [], []

    results = {"image": [], "audio": [], "video": [], "document": []}

    for file in files:
        logger.info(f"处理文件: {file.content_type}")
        processed = False

        for file_type, (content_types, processor) in FILE_TYPE_PROCESSORS.items():
            if file.content_type in content_types:
                try:
                    result = processor(file)
                    if result is not None:
                        results[file_type].append(result)
                    processed = True
                    break
                except Exception as e:
                    logger.error(f"处理{file_type} {file.filename} 失败: {e}")
                    continue

        if not processed:
            raise HTTPException(status_code=400, detail="不支持的文件类型")

    return results["image"], results["audio"], results["video"], results["document"]


def get_entity_instance(entity_factory):
    temp_instance: Agent | Team | Workflow = entity_factory(debug_mode=True)
    # 判断实体类型
    if hasattr(temp_instance, "team_id"):
        entity_type = "Team"
    elif hasattr(temp_instance, "workflow_id"):
        entity_type = "Workflow"
    else:
        entity_type = "Agent"
    return temp_instance, entity_type


def get_entity_by_id(
        entity_id: str, entity_type: Optional[str] = None
) -> Tuple[Any, str]:
    if entity_id not in agents_map:
        raise HTTPException(status_code=404, detail="Entity not found")

    entity_factory = agents_map[entity_id]
    temp_instance, found_type = get_entity_instance(entity_factory)

    if entity_type and entity_type != found_type:
        raise HTTPException(status_code=400, detail=f"Entity type mismatch: expected {entity_type}, got {found_type}")

    return temp_instance, found_type


def create_entity_instance(
        entity_id: str,
        user_id,
        session_id: Optional[str] = None,
        debug_mode: bool = True,
):
    entity_factory = agents_map[entity_id]
    sig = inspect.signature(entity_factory)

    param_values = {
        "user_id": user_id,
        "session_id": session_id,
        "debug_mode": debug_mode,
    }
    kwargs = {k: v for k, v in param_values.items() if k in sig.parameters}
    new_instance = entity_factory(**kwargs)

    for attr_name, attr_value in param_values.items():
        if hasattr(new_instance, attr_name) and attr_name not in kwargs:
            setattr(new_instance, attr_name, attr_value)

    return new_instance


async def run_entity_with_files(
        entity,
        message: str,
        monitor: bool,
        stream: bool,
        files: Optional[List[UploadFile]] = None,
):
    entity.monitoring = monitor
    base64_images, base64_audios, base64_videos, document_files = (
        await process_uploaded_files(files)
    )
    if stream:
        return create_streaming_response(
            generic_chat_response_streamer(
                entity,
                message,
                images=base64_images,
                audio=base64_audios,
                videos=base64_videos,
                files=document_files,
            )
        )
    else:
        run_response = await entity.arun(
            message,
            images=base64_images,
            audio=base64_audios,
            videos=base64_videos,
            files=document_files,
            stream=False,
        )
        return run_response.to_dict()
