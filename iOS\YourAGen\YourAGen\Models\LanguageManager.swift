import SwiftUI
import Foundation

class LanguageManager: ObservableObject {
    @Published var currentLanguage: String {
        didSet {
            UserDefaults.standard.set(currentLanguage, forKey: "currentLanguage")
        }
    }
    
    private let translations: [String: [String: String]] = [
        "zh": [
            "playground": "游乐场",
            "agents": "智能体",
            "settings": "设置",
            "profile": "个人资料",
            "theme": "主题",
            "language": "语言",
            "logout": "退出登录",
            "login": "登录",
            "register": "注册",
            "send": "发送",
            "typing": "正在输入...",
            "online": "在线",
            "offline": "离线",
            "new_chat": "新对话",
            "chat_history": "最近对话",
            "clear_history": "清空记录",
            "copy": "复制",
            "delete": "删除",
            "cancel": "取消",
            "confirm": "确认",
            "error": "错误",
            "success": "成功",
            "loading": "加载中...",
            "retry": "重试",
            "welcome": "欢迎使用 Your AI Agent",
            "select_agent": "选择智能体",
            "start_conversation": "开始对话",
            "message_placeholder": "输入消息...",
            "stream_error": "流式响应错误",
            "network_error": "网络错误",
            "auth_error": "认证失败",
            
            // 通用
            "common.ok": "确定",
            "common.cancel": "取消",
            "common.save": "保存",
            "common.delete": "删除",
            "common.edit": "编辑",
            "common.done": "完成",
            "common.loading": "加载中...",
            "common.error": "错误",
            "common.success": "成功",
            "common.retry": "重试",
            
            // 认证相关
            "auth.login.title": "登录",
            "auth.login.subtitle": "欢迎回来，请登录您的账户",
            "auth.login.fields.email": "邮箱",
            "auth.login.fields.password": "密码",
            "auth.login.actions.signIn": "登录",
            "auth.login.actions.rememberMe": "记住我",
            "auth.login.actions.forgotPassword": "忘记密码？",
            "auth.login.actions.noAccount": "还没有账户？",
            "auth.login.actions.register": "立即注册",
            "auth.login.error.title": "登录失败",
            "auth.login.error.emptyFields": "请填写邮箱和密码",
            "auth.login.error.invalidCredentials": "邮箱或密码错误",
            
            "auth.register.title": "注册",
            "auth.register.subtitle": "创建您的新账户",
            "auth.register.fields.name": "姓名",
            "auth.register.fields.email": "邮箱",
            "auth.register.fields.password": "密码",
            "auth.register.fields.confirmPassword": "确认密码",
            "auth.register.actions.signUp": "注册",
            "auth.register.actions.acceptTerms": "我同意",
            "auth.register.actions.terms": "服务条款",
            "auth.register.actions.haveAccount": "已有账户？",
            "auth.register.actions.login": "立即登录",
            "auth.register.error.title": "注册失败",
            "auth.register.error.emptyFields": "请填写所有必填字段",
            "auth.register.error.passwordMismatch": "两次输入的密码不一致",
            "auth.register.error.termsNotAccepted": "请同意服务条款",
            "auth.register.error.registrationFailed": "注册失败，请稍后再试"
        ],
        "en": [
            "playground": "Playground",
            "agents": "Agents",
            "settings": "Settings",
            "profile": "Profile",
            "theme": "Theme",
            "language": "Language",
            "logout": "Logout",
            "login": "Login",
            "register": "Register",
            "send": "Send",
            "typing": "Typing...",
            "online": "Online",
            "offline": "Offline",
            "new_chat": "New Chat",
            "chat_history": "Chat History",
            "clear_history": "Clear History",
            "copy": "Copy",
            "delete": "Delete",
            "cancel": "Cancel",
            "confirm": "Confirm",
            "error": "Error",
            "success": "Success",
            "loading": "Loading...",
            "retry": "Retry",
            "welcome": "Welcome to Your AI Agent",
            "select_agent": "Select Agent",
            "start_conversation": "Start Conversation",
            "message_placeholder": "Type a message...",
            "stream_error": "Stream Response Error",
            "network_error": "Network Error",
            "auth_error": "Authentication Failed",
            
            // Common
            "common.ok": "OK",
            "common.cancel": "Cancel",
            "common.save": "Save",
            "common.delete": "Delete",
            "common.edit": "Edit",
            "common.done": "Done",
            "common.loading": "Loading...",
            "common.error": "Error",
            "common.success": "Success",
            "common.retry": "Retry",
            
            // Authentication
            "auth.login.title": "Sign In",
            "auth.login.subtitle": "Welcome back, please sign in to your account",
            "auth.login.fields.email": "Email",
            "auth.login.fields.password": "Password",
            "auth.login.actions.signIn": "Sign In",
            "auth.login.actions.rememberMe": "Remember me",
            "auth.login.actions.forgotPassword": "Forgot password?",
            "auth.login.actions.noAccount": "Don't have an account?",
            "auth.login.actions.register": "Sign up now",
            "auth.login.error.title": "Login Failed",
            "auth.login.error.emptyFields": "Please fill in email and password",
            "auth.login.error.invalidCredentials": "Invalid email or password",
            
            "auth.register.title": "Sign Up",
            "auth.register.subtitle": "Create your new account",
            "auth.register.fields.name": "Name",
            "auth.register.fields.email": "Email",
            "auth.register.fields.password": "Password",
            "auth.register.fields.confirmPassword": "Confirm Password",
            "auth.register.actions.signUp": "Sign Up",
            "auth.register.actions.acceptTerms": "I agree to the",
            "auth.register.actions.terms": "Terms of Service",
            "auth.register.actions.haveAccount": "Already have an account?",
            "auth.register.actions.login": "Sign in now",
            "auth.register.error.title": "Registration Failed",
            "auth.register.error.emptyFields": "Please fill in all required fields",
            "auth.register.error.passwordMismatch": "Passwords do not match",
            "auth.register.error.termsNotAccepted": "Please accept the terms of service",
            "auth.register.error.registrationFailed": "Registration failed, please try again later"
        ]
    ]
    
    init() {
        self.currentLanguage = UserDefaults.standard.string(forKey: "currentLanguage") ?? "zh"
    }
    
    func localized(_ key: String) -> String {
        return translations[currentLanguage]?[key] ?? key
    }
    
    func localizedString(_ key: String) -> String {
        return translations[currentLanguage]?[key] ?? key
    }
    
    func setLanguage(_ language: String) {
        currentLanguage = language
    }
    
    var availableLanguages: [String: String] {
        return [
            "zh": "中文",
            "en": "English"
        ]
    }
} 