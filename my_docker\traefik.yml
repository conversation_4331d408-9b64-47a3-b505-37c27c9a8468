api:
  dashboard: true
  insecure: true

entryPoints:
  web:
    address: ":80"

  websecure:
    address: ":443"
    http3: {}
    http:
      tls:
        certResolver: letsencryptResolver

certificatesResolvers:
  letsencryptResolver:
    acme:
      email: <EMAIL>
      storage: /etc/traefik/certs/acme.json
      dnsChallenge:
        provider: cloudflare
        # Traefik 3.x 中 DNS 提供商配置方式有所变化
        resolvers:
          - "*******:53"
          - "*******:53"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: proxy-network  # 明确指定网络
  file:
    filename: /etc/traefik/traefik_dynamic.yml
    watch: true  # 启用文件监视，配置变更时自动重新加载

log:
  level: INFO

accessLog: {}

metrics:
  prometheus: {}  # Traefik 3.x 增强了指标收集功能