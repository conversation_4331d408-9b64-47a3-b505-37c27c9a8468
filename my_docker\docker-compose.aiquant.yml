services:
  web:
    image: docker.aiquant.io/aiquant-backend:latest
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s  # 重启延迟
        max_attempts: 3  # 最大重试次数
        window: 120s  # 重试窗口
    environment:
      - TZ=Asia/Shanghai  # 设置时区
      - ENVIRONMENT=development  # 示例环境变量
      - DATABASE_URL=${DATABASE_URL}  # 从 .env 文件中读取环境变量
      - PYTHONPATH=/app
    volumes:
      - aiquant_data:/app  # 将当前目录挂载到容器内的 /app
    command: python main.py  # 启动容器时运行的命令
    ports:
      - "8888:8888"  # 映射端口
  ticker:
    image: docker.aiquant.io/aiquant-backend:latest
    deploy:
      restart_policy:
        condition: on-failure
        delay: 5s  # 重启延迟
        max_attempts: 3  # 最大重试次数
        window: 120s  # 重试窗口
    environment:
      - TZ=Asia/Shanghai  # 设置时区
      - ENVIRONMENT=development  # 示例环境变量
      - DATABASE_URL=${DATABASE_URL}  # 从 .env 文件中读取环境变量
      - PYTHONPATH=/app
    volumes:
      - aiquant_data:/app  # 将当前目录挂载到容器内的 /app
    command: python app/market/starter_market.py

volumes:
  aiquant_data:
