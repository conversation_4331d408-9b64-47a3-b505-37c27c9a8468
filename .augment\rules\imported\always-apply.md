---
type: "always_apply"
---

# Your AI Agent 通用开发工作流

## 项目简介

- 类型: 全功能人工智能代理系统。
- 核心: 以AI Agent开发为核心。
- 应用场景: 支持多种AI能力的集成和扩展

## 核心原则

- 对话语言:
    - 必须使用中文进行回复。
    - Always response in Chinese
- 代码修改原则:
    - 原子性：同一个文件尽量一次性完成修改,减少工具调用。
    - 上下文感知: 修改前需理解现有代码结构和逻辑，学习并保持代码原有的编码风格。
    - 禁止硬编码: 避免在代码中使用硬编码的字符串或数字。
    - 最佳实践：遵循各平台的最佳实践
    - 极简设计：无冗余,拒绝重复内容
    - 易维护：逻辑集中，结构清晰

- 服务管理:
    - 禁止主动启动/停止服务 (如yarn dev, uvicorn，docker等)。
    - 如需调试请调用MCP mcp-feedback-enhanced工具请求用户手动操作。

- 沟通反馈规则:
    - 在每个任务步骤中都必须调用mcp-feedback-enhanced工具以获取用户反馈,并根据反馈调整
    - 什么时候结束任务对话? 当且仅当用户明确指示“结束”时才停止。

## 平台特定规则

- 后端开发特定规则:
    - 遵循FastAPI最佳实践。
    - 使用集中的异常处理。
    - 遵守既定的项目结构 (backend/app/...)。
    - 权限控制采用RBAC。

- 前端开发特定规则：
    - 优先使用现有UI组件 (Naive UI) 和Tailwind CSS。禁止自定义CSS,除非绝对必要。
    - 包管理工具强制使用yarn

- iOS开发特定规则:
    - 使用Swift 6
    - 每次修改后必须在iPhone 16模拟器上编译,并修复所有编译错误
    - 使用开源组件，避免重复造轮子

## MCP工具使用规则

- 命名以doc开头的都是文档集成工具: 用于查询不熟悉或有版本差异的API。
- 系统可用的工具都是有用的，应该积极调用工具来提升自身能力

## 技术栈 (Tech Stack)

- 后端: Python 3.12+, FastAPI, Agno, PostgreSQL, SQLModel, Pydantic, JWT。
- 前端: Vue 3, Pinia, Naive UI, Tailwind CSS, i18n
- iOS移动端: Swift 6, Xcode 16.4, iOS 18.5。
- 部署运维: Docker Compose, Traefik

## 核心配置文件

- [docker-compose.yml](mdc:docker-compose.yml) - Docker服务编排
- [backend/main.py](mdc:backend/main.py) - 后端应用入口
- [frontend/package.json](mdc:frontend/package.json) - 前端依赖管理
- [frontend/vite.config.ts](mdc:frontend/vite.config.ts) - 前端构建配置

## 关键目录

- [backend/app/api/v1/](mdc:backend/app/api/v1) - 后端API路由
- [backend/app/models/](mdc:backend/app/models) - 数据模型定义
- [frontend/src/components/](mdc:frontend/src/components) - Vue组件
- [frontend/src/views/](mdc:frontend/src/views) - 页面视图
- [iOS/YourAGen/YourAGen/](mdc:iOS/YourAGen/YourAGen) - iOS应用代码
