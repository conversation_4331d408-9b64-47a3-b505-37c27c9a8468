from fastapi import Depends

from app.core.core_config import settings
from app.db.db_session_manager import DBSessionManager

db_manager = DBSessionManager(**settings.model_dump())
get_db = Depends(db_manager.get_db())
get_async_db = Depends(db_manager.get_async_db())

if __name__ == '__main__':
    import asyncio
    from sqlalchemy import text


    async def main():
        async with db_manager.async_session() as session:
            result = await session.execute(text("SELECT 1"))
            print(result.fetchone())


    asyncio.run(main())
