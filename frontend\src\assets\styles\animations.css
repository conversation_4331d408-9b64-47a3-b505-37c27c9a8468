/* 全局字体优化 */
* {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    sans-serif,
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variant-numeric: tabular-nums;
}

/* 针对中文字体优化 */
html[lang='zh-CN'] *,
html[lang='zh'] * {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'PingFang SC',
    'Hiragino Sans GB',
    'Microsoft YaHei',
    '微软雅黑',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    sans-serif;
}

/* 通用过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease-in-out;
}

.slide-enter-from {
  opacity: 0;
  transform: translateY(15px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateY(-15px);
}

.scale-enter-active,
.scale-leave-active {
  transition: all 0.3s ease-in-out;
}

.scale-enter-from {
  opacity: 0;
  transform: scale(0.8);
}

.scale-leave-to {
  opacity: 0;
  transform: scale(1.2);
}

/* 加载动画 */
.loading-dots {
  @apply flex gap-1;
}

.loading-dot {
  @apply w-1.5 h-1.5 rounded-full animate-bounce;
}

.loading-dot:nth-child(1) {
  animation-delay: 0ms;
}

.loading-dot:nth-child(2) {
  animation-delay: 150ms;
}

.loading-dot:nth-child(3) {
  animation-delay: 300ms;
}

/* 状态指示器 */
.status-indicator {
  @apply flex items-center gap-2;
}

.status-dot {
  @apply w-2 h-2 rounded-full animate-pulse;
}

.status-dot.online {
  @apply bg-green-500;
}

.status-dot.thinking {
  @apply bg-gray-400;
}

.status-dot.error {
  @apply bg-red-500;
}
