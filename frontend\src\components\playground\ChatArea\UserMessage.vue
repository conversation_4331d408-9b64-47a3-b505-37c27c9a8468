<template>
  <MessageBubble :message="message" message-type="user" @edit="editMessage" />
</template>

<script lang="ts" setup>
import { useMessage } from 'naive-ui';
import type { PlaygroundChatMessage } from '@/types/playground';
import MessageBubble from './MessageBubble.vue';

interface Props {
  message: PlaygroundChatMessage;
}

const props = defineProps<Props>();
const messageApi = useMessage();

const editMessage = () => {
  messageApi.info('编辑功能开发中...');
};
</script>
