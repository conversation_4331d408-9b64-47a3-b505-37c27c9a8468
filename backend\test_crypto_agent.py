#!/usr/bin/env python3
"""
测试 Crypto Prediction Agent 功能的脚本
"""
import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.dev.crypto_prediction_agent import get_crypto_prediction_agent


async def test_crypto_agent():
    """测试 Crypto Prediction Agent"""
    print("🚀 开始测试 Crypto Prediction Agent...")

    try:
        # 创建 crypto agent 实例
        agent = get_crypto_prediction_agent(
            model_id="gpt-4.1",
            user_id="test_user",
            session_id="test_session",
            debug_mode=True
        )

        print(f"✅ Crypto Prediction Agent 创建成功")
        print(f"   名称: {agent.name}")
        print(f"   ID: {agent.agent_id}")
        print(f"   工具: {[tool.__class__.__name__ for tool in agent.tools]}")

        # 测试不同类型的查询
        test_queries = [
            "简单介绍一下你的功能",
            # "分析比特币当前的市场状况",
            # "以太坊的投资前景如何？",
            # "什么是加密货币的技术分析？"
        ]

        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 测试查询 {i}: {query}")
            try:
                # 注意：这里不实际运行 agent，因为需要数据库连接和 API 密钥
                # response = await agent.arun(query, stream=False)
                # print(f"   响应: {response.content[:200]}...")
                print("   （跳过实际运行，因为需要数据库连接）")
            except Exception as e:
                print(f"   ❌ 查询失败: {str(e)}")

    except Exception as e:
        print(f"❌ Crypto Prediction Agent 测试失败: {str(e)}")

    print("\n✨ 测试完成！")


if __name__ == "__main__":
    asyncio.run(test_crypto_agent())
