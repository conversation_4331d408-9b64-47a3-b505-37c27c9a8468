import SwiftUI

struct AgentSelectorView: View {
    let agents: [Agent]
    let selectedAgent: Agent?
    let onAgentSelected: (Agent) -> Void
    
    @EnvironmentObject var languageManager: LanguageManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach(agents) { agent in
                        AgentCard(
                            agent: agent,
                            isSelected: selectedAgent?.id == agent.id,
                            onTap: {
                                onAgentSelected(agent)
                            }
                        )
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 20)
            }
            .navigationTitle(languageManager.localized("select_agent"))
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(languageManager.localized("cancel")) {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct AgentCard: View {
    let agent: Agent
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 12) {
                // 头部信息
                HStack(spacing: 12) {
                    // Agent图标
                    Image(systemName: agent.icon)
                        .font(.system(size: 24))
                        .foregroundColor(.white)
                        .frame(width: 48, height: 48)
                        .background(
                            Circle()
                                .fill(isSelected ? Color.blue : Color.green)
                        )
                    
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text(agent.name)
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                            
                            Spacer()
                            
                            if isSelected {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.blue)
                                    .font(.system(size: 20))
                            }
                        }
                        
                        Text(agent.description)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                }
                
                // 模型信息
                HStack(spacing: 8) {
                    Image(systemName: "cpu")
                        .font(.system(size: 12))
                        .foregroundColor(.blue)
                    Text("模型: \(agent.model.model)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Image(systemName: "building.2")
                        .font(.system(size: 12))
                        .foregroundColor(.green)
                    Text(agent.model.provider.capitalized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                // 存储支持指示器
                HStack {
                    if agent.storage == true {
                        HStack(spacing: 4) {
                            Image(systemName: "externaldrive")
                                .font(.system(size: 12))
                                .foregroundColor(.blue)
                            Text("支持存储")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    Spacer()
                    
                    Text("Agent ID: \(agent.agentId)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                    )
                    .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    AgentSelectorView(
        agents: [],
        selectedAgent: nil,
        onAgentSelected: { _ in }
    )
    .environmentObject(LanguageManager())
} 