"""市场交易对模型

对应数据库中的 crypto.symbol 表，遵循SQL文件定义
"""

from datetime import datetime
from enum import Enum
from typing import List

import sqlalchemy as sa
from sqlalchemy import PrimaryKeyConstraint
from sqlmodel import Field, Relationship

from app.db.db_orm_base import SchemaBase
from app.db.db_orm_base_model import MixTime

# 前向引用
Ticker = "Ticker"
Kline = "Kline"


class ExchangeEnum(str, Enum):
    binance = "binance"
    okex = "okex"
    huobi = "huobi"
    bitfinex = "bitfinex"
    ftx = "ftx"
    mexc = "mexc"


class MarketTypeEnum(str, Enum):
    spot = "spot"
    coin_future = 'coin_future'
    usdt_future = 'usdt_future'


# 数据库市场交易对模型
class Symbol(SchemaBase, MixTime, table=True):
    """市场交易对数据库模型，对应crypto.symbol表"""
    __tablename__ = "symbol"
    __table_args__ = (
        PrimaryKeyConstraint('exchange', 'market_type', 'symbol'),
        {"schema": "crypto"}
    )
    id: int | None = Field(title='ID', sa_column=sa.Column(sa.SmallInteger, sa.Identity(start=1000), unique=True, nullable=False))
    exchange: ExchangeEnum = Field(..., title="交易所")
    market_type: MarketTypeEnum = Field(..., title="市场类型")
    symbol: str = Field(..., title="交易对符号")
    base_coin: str = Field(..., title="基础币种")
    quote_coin: str = Field(..., title="计价币种")
    margin_coin: str = Field(..., title="保证金币种")
    trade_status: str = Field("TRADING", title="交易状态")
    contract_type: str = Field("", title="合约类型")
    contract_val: float = Field(1.0, title="合约价值")
    list_time: datetime | None = Field(title="上线时间", sa_column_kwargs={"server_default": "'2021-12-31 16:00:00'"})
    delivery_time: datetime | None = Field(title="交割时间", sa_column_kwargs={"server_default": "'2099-12-31 16:00:00'"})
    amount_precision: int = Field(100, title="数量精度")
    price_precision: int = Field(100, title="价格精度")
    min_amount: float = Field(0.0, title="最小交易数量")
    min_cost: float = Field(10.0, title="最小交易成本")
    sub_market: bool = Field(True, title="是否为子市场")
    tag: str | None = Field(None, title="交易对标签")
    kline_pending_sync: bool | None = Field(title="k线等待同步")
    is_deleted: bool = Field(False, title="是否已删除")

    # 关系
    ticker: Ticker = Relationship(
        back_populates="symbol",
        sa_relationship_kwargs={
            "primaryjoin": "Symbol.id == foreign(Ticker.symbol_id)",
        }
    )

    klines: List[Kline] = Relationship(
        back_populates="symbol",
        sa_relationship_kwargs={
            "primaryjoin": "Symbol.id == foreign(Kline.symbol_id)"
        }
    )
