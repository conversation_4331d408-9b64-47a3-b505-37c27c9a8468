from typing import List

from fastapi import APIRouter, Depends, HTTPException, status, Query

from app.core.core_auth_deps import get_current_active_user
from app.models.auth import User
from app.models.notification.model_notification import NotificationResponse
from app.models.notification.model_preference import UserPreferenceUpdate as NotificationPreferenceUpdate, UserPreferenceResponse as NotificationPreferenceResponse

router = APIRouter(prefix="/notifications", tags=["通知"])


@router.get("/", response_model=List[NotificationResponse])
async def get_notifications(
        skip: int = Query(0, ge=0),
        limit: int = Query(20, ge=1, le=100),
        unread_only: bool = Query(False),
        current_user: User = Depends(get_current_active_user)
):
    """获取用户的通知列表"""
    notifications = await notification_service.get_user_notifications(
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        unread_only=unread_only
    )

    return [
        NotificationResponse(
            id=notification.id,
            notification_type=notification.notification_type,
            title=notification.title,
            content=notification.content,
            data=notification.data,
            link=notification.link,
            is_read=notification.is_read,
            read_at=notification.read_at,
            created_at=notification.created_at
        ) for notification in notifications
    ]


@router.post("/{notification_id}/read")
async def mark_as_read(
        notification_id: int,
        current_user: User = Depends(get_current_active_user)
):
    """将通知标记为已读"""
    success = await notification_service.mark_notification_as_read(
        notification_id=notification_id,
        user_id=current_user.id
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="通知不存在或已读"
        )

    return {"detail": "通知已标记为已读"}


@router.put("/preferences/{notification_type}/{channel_id}", response_model=NotificationPreferenceResponse)
async def update_notification_preference(
        notification_type: str,
        channel_id: int,
        preference: NotificationPreferenceUpdate,
        current_user: User = Depends(get_current_active_user)
):
    """更新用户通知偏好设置"""
    updated_preference = await notification_service.set_user_preference(
        user_id=current_user.id,
        notification_type=notification_type,
        channel_id=channel_id,
        is_enabled=preference.is_enabled
    )

    return NotificationPreferenceResponse(
        user_id=str(updated_preference.user_id),
        notification_type=updated_preference.notification_type,
        channel_id=updated_preference.channel_id,
        is_enabled=updated_preference.is_enabled
    )
