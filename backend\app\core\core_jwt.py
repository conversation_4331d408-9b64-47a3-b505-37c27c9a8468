from datetime import timedelta
from uuid import UUID

import pendulum
from jose import jwt, JWTError
from pydantic import ValidationError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.core_config import settings
from app.core.core_exceptions import AuthenticationException
from app.models import User
from app.models.auth import TokenPayload, Token


def create_access_token(subject: str, expires_delta: timedelta | None = None) -> str:
    """
    创建访问令牌
    
    Args:
        subject: 令牌主题（通常是用户ID）
        expires_delta: 过期时间增量
        
    Returns:
        JWT令牌字符串
    """
    if expires_delta:
        expire = pendulum.now("UTC").add(seconds=expires_delta.total_seconds())
    else:
        expire = pendulum.now("UTC").add(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode = {"exp": expire.int_timestamp, "sub": str(subject), "type": "access"}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(subject: str, expires_delta: timedelta | None = None) -> str:
    """
    创建刷新令牌
    
    Args:
        subject: 令牌主题（通常是用户ID）
        expires_delta: 过期时间增量
        
    Returns:
        JWT令牌字符串
    """
    if expires_delta:
        expire = pendulum.now("UTC").add(seconds=expires_delta.total_seconds())
    else:
        expire = pendulum.now("UTC").add(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)

    to_encode = {"exp": expire.int_timestamp, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str, token_type: str = "access") -> TokenPayload:
    """
    验证JWT令牌
    
    Args:
        token: JWT令牌字符串
        token_type: 令牌类型（access或refresh）
        
    Returns:
        解码后的令牌载荷
        
    Raises:
        AuthenticationException: 令牌无效或已过期
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        token_data = TokenPayload(**payload)

        # 检查令牌是否已过期
        if token_data.exp < pendulum.now("UTC").int_timestamp:
            raise AuthenticationException("令牌已过期")

        # 验证令牌类型
        if token_data.type != token_type:
            raise AuthenticationException("令牌类型不匹配")

        return token_data
    except (JWTError, ValidationError):
        raise AuthenticationException("无效的令牌")


async def create_tokens(user_id: UUID, access_token_expires: timedelta | None = None, refresh_token_expires: timedelta | None = None, ) -> Token:
    """
    创建访问令牌和刷新令牌

    Args:
        user_id: 用户ID
        access_token_expires: 访问令牌过期时间
        refresh_token_expires: 刷新令牌过期时间

    Returns:
        包含访问令牌和刷新令牌的对象
    """
    # 创建访问令牌和刷新令牌
    access_token = create_access_token(subject=str(user_id), expires_delta=access_token_expires)

    refresh_token = create_refresh_token(subject=str(user_id), expires_delta=refresh_token_expires)

    return Token(access_token=access_token, refresh_token=refresh_token, token_type="bearer")


async def refresh_tokens(db: AsyncSession, refresh_token: str, ) -> Token:
    """
    刷新令牌

    Args:
        db: 数据库会话
        refresh_token: 刷新令牌

    Returns:
        包含新的访问令牌和刷新令牌的对象

    Raises:
        AuthenticationException: 令牌无效或已过期
    """
    try:
        # 验证刷新令牌
        token_data = verify_token(refresh_token, "refresh")

        # 查询用户
        user = await User.get(session=db, id=token_data.sub)
        if not user or not user.is_active:
            raise AuthenticationException("无效的令牌")

        # 创建新的令牌
        return await create_tokens(user.id)

    except Exception as e:
        raise AuthenticationException(str(e))
