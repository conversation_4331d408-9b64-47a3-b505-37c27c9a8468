from enum import Enum

from sqlmodel import Field, SQLModel


# 通知状态枚举
class NotificationStatus(str, Enum):
    """通知状态枚举"""
    PENDING = "pending"  # 待发送
    SENDING = "sending"  # 发送中
    SENT = "sent"  # 已发送
    DELIVERED = "delivered"  # 已送达
    READ = "read"  # 已读
    FAILED = "failed"  # 失败


# 通知类型枚举
class NotificationType(str, Enum):
    """通知类型枚举"""
    SYSTEM = "system"  # 系统通知
    ALERT = "alert"  # 警报通知
    INFO = "info"  # 信息通知
    EVENT = "event"  # 事件通知
    REMINDER = "reminder"  # 提醒通知
    MARKETING = "marketing"  # 营销通知


# 通知优先级枚举
class NotificationPriority(str, Enum):
    """通知优先级枚举"""
    LOW = "low"  # 低优先级
    NORMAL = "normal"  # 正常优先级
    HIGH = "high"  # 高优先级
    URGENT = "urgent"  # 紧急优先级


# 通知渠道枚举
class NotificationChannel(str, Enum):
    """通知渠道枚举"""
    EMAIL = "email"  # 电子邮件
    SMS = "sms"  # 短信
    PUSH = "push"  # 推送通知
    WEBAPP = "webapp"  # 网页应用内通知
    MOBILE = "mobile"  # 移动应用内通知
    WEBHOOK = "webhook"  # Webhook


# 通知基础模型
class NotificationBase(SQLModel):
    """通知基础信息模型
    
    包含通知的基本信息
    """
    subject: str = Field(..., min_length=1, max_length=255, title="通知主题")
    content: str = Field(..., title="通知内容")
    notification_type: NotificationType = Field(..., title="通知类型")
    priority: NotificationPriority = Field(default=NotificationPriority.NORMAL, title="优先级")


# 通知模板基础模型
class NotificationTemplateBase(SQLModel):
    """通知模板基础信息模型
    
    包含通知模板的基本信息
    """
    name: str = Field(..., min_length=1, max_length=100, title="模板名称")
    code: str = Field(..., min_length=1, max_length=50, title="模板代码")
    subject_template: str = Field(..., title="主题模板")
    content_template: str = Field(..., title="内容模板")
    notification_type: NotificationType = Field(..., title="通知类型")


# 通知渠道基础模型
class NotificationChannelBase(SQLModel):
    """通知渠道基础信息模型
    
    包含通知渠道的基本信息
    """
    name: str = Field(..., min_length=1, max_length=100, title="渠道名称")
    channel_type: NotificationChannel = Field(..., title="渠道类型")
    is_active: bool = Field(default=True, title="是否启用")


# 用户通知偏好基础模型
class UserPreferenceBase(SQLModel):
    """用户通知偏好基础信息模型
    
    包含用户通知偏好的基本信息
    """
    notification_type: NotificationType = Field(..., title="通知类型")
    channel_type: NotificationChannel = Field(..., title="渠道类型")
    is_enabled: bool = Field(default=True, title="是否启用")
