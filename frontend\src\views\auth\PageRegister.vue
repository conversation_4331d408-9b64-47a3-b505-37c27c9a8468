<template>
  <div class="min-h-screen flex flex-col bg-gradient-to-br from-gray-50 to-gray-100 dark:from-dark dark:to-dark-secondary transition-colors duration-300 relative overflow-hidden">
    <div class="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <!-- 装饰性背景元素 -->
      <div class="absolute top-0 right-0 w-full h-64 bg-primary opacity-5 dark:opacity-10 dark:bg-gradient-to-r dark:from-primary dark:to-primary-light transform skew-y-6"></div>
      <div class="absolute bottom-0 left-0 w-full h-64 bg-accent opacity-5 dark:opacity-10 dark:bg-gradient-to-r dark:from-accent dark:to-primary transform -skew-y-6"></div>

      <div class="max-w-md w-full space-y-8 relative z-10">
        <div class="text-center animate-fade-in">
          <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
            {{ $t('auth.register.title') }}
          </h2>
          <div class="text-sm text-gray-600 dark:text-gray-300 mt-2">
            {{ $t('auth.register.actions.haveAccount') }}
            <router-link :to="{ name: 'Login' }" class="text-primary hover:text-accent dark:text-primary-light dark:hover:text-primary transition">
              {{ $t('auth.register.actions.login') }}
            </router-link>
          </div>
        </div>
        <form
          class="mt-8 space-y-6 bg-white dark:bg-dark-secondary p-8 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 animate-fade-in animation-delay-100"
          @submit.prevent="handleRegister"
        >
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" for="name">{{ $t('auth.register.fields.name') }}</label>
              <input
                id="name"
                v-model="name"
                :placeholder="$t('auth.register.fields.name')"
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm dark:bg-dark-alt transition-colors duration-300"
                name="name"
                required
                type="text"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" for="email">{{ $t('auth.register.fields.email') }}</label>
              <input
                id="email"
                v-model="email"
                :placeholder="$t('auth.register.fields.email')"
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm dark:bg-dark-alt transition-colors duration-300"
                name="email"
                required
                type="email"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" for="password">{{ $t('auth.register.fields.password') }}</label>
              <input
                id="password"
                v-model="password"
                :placeholder="$t('auth.register.fields.password')"
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm dark:bg-dark-alt transition-colors duration-300"
                name="password"
                required
                type="password"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" for="confirm-password">{{ $t('auth.register.fields.confirmPassword') }}</label>
              <input
                id="confirm-password"
                v-model="confirmPassword"
                :placeholder="$t('auth.register.fields.confirmPassword')"
                class="appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-700 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white rounded-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm dark:bg-dark-alt transition-colors duration-300"
                name="confirm-password"
                required
                type="password"
              />
            </div>
          </div>

          <div class="flex items-center">
            <input
              id="terms"
              v-model="acceptTerms"
              class="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-700 rounded"
              name="terms"
              required
              type="checkbox"
            />
            <label class="ml-2 block text-sm text-gray-900 dark:text-gray-300" for="terms">
              {{ $t('auth.register.actions.acceptTerms') }}
              <a class="font-medium text-primary hover:text-accent dark:text-primary-light dark:hover:text-primary transition-colors" href="#">
                {{ $t('auth.register.actions.terms') }}
              </a>
            </label>
          </div>

          <div>
            <button
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-accent focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary dark:bg-primary-light dark:hover:bg-primary transition-all duration-300 shadow-md hover:shadow-lg hover:scale-105"
              type="submit"
            >
              {{ $t('auth.register.actions.signUp') }}
            </button>
          </div>
        </form>
        <div class="h-20"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const name = ref('');
const email = ref('');
const password = ref('');
const confirmPassword = ref('');
const acceptTerms = ref(false);

const handleRegister = async () => {
  if (password.value !== confirmPassword.value) {
    // TODO: 显示错误提示
    return;
  }

  // TODO: 实现注册逻辑
  console.log('Register attempt:', {
    name: name.value,
    email: email.value,
    password: password.value,
  });
  // 注册成功后跳转到首页
  await router.push('/');
};
</script>

<style scoped>
.animate-fade-in {
  opacity: 0;
  transform: translateY(10px);
  animation: fadeIn 0.6s ease-out forwards;
}

.animation-delay-100 {
  animation-delay: 0.1s;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
