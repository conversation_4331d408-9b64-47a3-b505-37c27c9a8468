user  nginx;
worker_processes  $NGINX_WORKER_PROCESSES;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    keepalive_timeout  65;
    gzip off;

    server {
        listen ${FRONTEND_PORT};

        # 前端应用
        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        # 流式接口专用配置 - 解决HTTP/2协议错误
        location ~* ^/api/v1/agents/[^/]+/runs {
            proxy_pass http://${BACKEND_HOST}:${BACKEND_PORT};
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

            # 强制使用HTTP/1.1避免HTTP/2协议问题
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_set_header Upgrade "";

            # 流式响应关键设置
            proxy_buffering off;
            proxy_cache off;
            proxy_read_timeout 90s;          # 小于Cloudflare的100秒
            proxy_connect_timeout 60s;
            proxy_send_timeout 90s;

            # SSE专用headers
            proxy_set_header Accept text/event-stream;
            proxy_set_header Cache-Control no-cache;
            
            # 防止缓冲和协议转换问题
            add_header X-Accel-Buffering no;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma no-cache;
            add_header Expires 0;
            add_header Connection keep-alive;
            
            # CORS headers for HTTP/2 compatibility
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, Accept";
            add_header Access-Control-Expose-Headers "Content-Type";
            
            # 确保正确的Content-Type
            proxy_hide_header Content-Encoding;
            proxy_hide_header Content-Length;
            proxy_hide_header Transfer-Encoding;
        }

        # 普通API接口
        location /api {
            proxy_pass http://${BACKEND_HOST}:${BACKEND_PORT};
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

            proxy_read_timeout 60s;
            proxy_connect_timeout 30s;
            proxy_http_version 1.1;
            proxy_set_header Connection "";
        }
    }

    include /etc/nginx/conf.d/*.conf;
}