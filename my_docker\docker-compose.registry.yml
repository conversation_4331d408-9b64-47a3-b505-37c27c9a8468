services:
  registry-server:
    image: registry:2.8.2
    restart: always
    ports:
      - "5001:5000"  # 映射主机端口5001到容器端口5000
    environment:
      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Origin: '["*"]'
      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Methods: '["HEAD", "GET", "OPTIONS", "DELETE"]'
      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Credentials: '["true"]'
      REGISTRY_HTTP_HEADERS_Access-Control-Allow-Headers: '["Authorization", "Accept", "Cache-Control"]'
      REGISTRY_HTTP_HEADERS_Access-Control-Expose-Headers: '["Docker-Content-Digest"]'
      REGISTRY_STORAGE_DELETE_ENABLED: "true"
      REGISTRY_AUTH: "htpasswd"
      REGISTRY_AUTH_HTPASSWD_REALM: "Registry Realm"
      REGISTRY_AUTH_HTPASSWD_PATH: "/auth/htpasswd"
      TZ: "Asia/Shanghai"
    volumes:
      - registry-data:/var/lib/registry
      - auth-volume:/auth  # 本地认证文件路径
    networks:
      - registry-network  # 使用自定义网络
    container_name: registry-server

  registry-ui:
    image: joxit/docker-registry-ui:main
    restart: always
    ports:
      - "5002:80"  # 映射主机端口5002到容器端口80
    environment:
      SINGLE_REGISTRY: "true"
      REGISTRY_TITLE: "Docker Registry UI"
      DELETE_IMAGES: "true"
      SHOW_CONTENT_DIGEST: "true"
      NGINX_PROXY_PASS_URL: "http://registry-server:5000"  # 连接到 registry-server
      SHOW_CATALOG_NB_TAGS: "true"
      CATALOG_MIN_BRANCHES: "1"
      CATALOG_MAX_BRANCHES: "1"
      TAGLIST_PAGE_SIZE: "100"
      REGISTRY_SECURED: "true"
      CATALOG_ELEMENTS_LIMIT: "1000"
      HTTP_SECRET: "Gi3RIfKQOSKUmDkfRKmcqOk4jj7N9oLmAEdgUuvIGg8="  # 添加 HTTP secret
      TZ: "Asia/Shanghai"
    networks:
      - registry-network  # 使用自定义网络
    container_name: registry-ui

volumes:
  registry-data:
  auth-volume:  # 确保在这里定义卷

networks:
  registry-network:

# 设置密码
# docker run --rm -it -v registry_auth-volume:/auth httpd:2.4-alpine sh -c "apk add --no-cache apache2-utils && htpasswd -Bc /auth/htpasswd admin"