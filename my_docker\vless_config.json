{"log": {"loglevel": "warning"}, "inbounds": [{"port": 8073, "protocol": "vless", "settings": {"clients": [{"id": "b831381d-6324-4d53-ad4f-8cda48b30811", "level": 0}], "decryption": "none", "fallbacks": [{"dest": 80, "xver": 1}]}, "streamSettings": {"network": "ws", "security": "none", "wsSettings": {"path": "/ws", "Host": "v2.aiquant.io"}}, "sniffing": {"enabled": true, "destOverride": ["http", "tls"]}}], "outbounds": [{"protocol": "freedom", "tag": "direct"}, {"protocol": "blackhole", "tag": "blocked"}], "routing": {"rules": [{"type": "field", "ip": ["geoip:private"], "outboundTag": "blocked"}]}}