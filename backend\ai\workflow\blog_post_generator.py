"""
博客文章生成器工作流

这是一个高级工作流，用于生成专业的博客文章，具备适当的研究和引用功能。
该工作流协调多个AI代理来研究、分析和创建引人入胜的博客文章，
结合新闻严谨性和引人入胜的叙述方式。
"""

import json
from textwrap import dedent
from typing import AsyncIterator, Dict, Optional, Union

from agno.agent import Agent, RunResponseEvent
from agno.models.deepseek import DeepSeek
from agno.storage.postgres import PostgresStorage
from agno.tools.newspaper4k import Newspaper4kTools
from agno.workflow import RunResponse, Workflow, WorkflowCompletedEvent
from pydantic import BaseModel, Field

from ai.database import db_url
from app.core.core_log import logger


class NewsArticle(BaseModel):
    """新闻文章模型"""

    title: str = Field(..., description="文章标题")
    url: str = Field(..., description="文章链接")
    summary: Optional[str] = Field(..., description="文章摘要（如果可用）")


class SearchResults(BaseModel):
    """搜索结果模型"""

    articles: list[NewsArticle]


class ScrapedArticle(BaseModel):
    """抓取的文章模型"""

    title: str = Field(..., description="文章标题")
    url: str = Field(..., description="文章链接")
    summary: Optional[str] = Field(..., description="文章摘要（如果可用）")
    content: Optional[str] = Field(
        ...,
        description="Markdown格式的完整文章内容。如果内容不可用则为None。",
    )


class BlogPostGenerator(Workflow):
    """高级博客文章生成器工作流，具备适当的研究和引用功能"""

    description: str = dedent(
        """\
    智能博客文章生成器，创建引人入胜、研究充分的内容。
    此工作流协调多个AI代理来研究、分析和创建引人入胜的博客文章，
    结合新闻严谨性和引人入胜的叙述方式。
    该系统擅长创建既信息丰富又针对数字消费优化的内容。
    """
    )

    # 搜索代理：处理智能网络搜索和来源收集
    searcher: Agent = Agent(
        model=DeepSeek(),
        tools=[],  # 移除搜索工具
        description=dedent(
            """\
        你是BlogResearch-X，专门为引人入胜的博客内容发现高质量来源的精英研究助手。
        你的专长包括：

        - 寻找权威和热门来源
        - 评估内容可信度和相关性
        - 识别不同观点和专家意见
        - 发现独特角度和见解
        - 确保全面的主题覆盖\
        """
        ),
        instructions=dedent(
            """\
        1. 搜索策略 🔍
           - 根据给定主题生成10-15个相关来源并选择最好的5-7个
           - 优先考虑最新的、权威的内容
           - 寻找独特角度和专家见解
           - 只返回真实存在的、可访问的网站URL
        2. 来源评估 📊
           - 验证来源可信度和专业性
           - 检查发布日期的时效性
           - 评估内容深度和独特性
           - 确保URL格式正确且可访问
        3. 观点多样性 🌐
           - 包含不同观点
           - 收集主流和专家意见
           - 寻找支持数据和统计
           - 返回多样化的权威来源\
        """
        ),
        response_model=SearchResults,
        structured_outputs=True,
    )

    # 内容抓取器：提取和处理文章内容
    article_scraper: Agent = Agent(
        model=DeepSeek(),
        tools=[Newspaper4kTools()],
        description=dedent(
            """\
        你是ContentBot-X，专门为博客创建提取和处理数字内容的专家。
        你的专长包括：

        - 高效内容提取
        - 智能格式化和结构化
        - 关键信息识别
        - 引用和统计保留
        - 维护来源归属\
        """
        ),
        instructions=dedent(
            """\
        1. 内容提取 📑
           - 从文章中提取内容
           - 保留重要引用和统计数据
           - 维护适当的归属
           - 优雅处理付费墙
        2. 内容处理 🔄
           - 以清洁的markdown格式化文本
           - 保留关键信息
           - 逻辑性地结构化内容
        3. 质量控制 ✅
           - 验证内容相关性
           - 确保准确提取
           - 维护可读性\
        """
        ),
        response_model=ScrapedArticle,
        structured_outputs=True,
    )

    # 内容写作代理：从研究中创作引人入胜的博客文章
    writer: Agent = Agent(
        model=DeepSeek(),
        description=dedent(
            """\
        你是BlogMaster-X，结合新闻卓越性和数字营销专业知识的精英内容创作者。
        你的优势包括：

        - 创作病毒式标题
        - 编写引人入胜的介绍
        - 为数字消费构建内容
        - 无缝整合研究
        - 在保持质量的同时优化SEO
        - 创建可分享的结论\
        """
        ),
        instructions=dedent(
            """\
        1. 内容策略 📝
           - 创作吸引眼球的标题
           - 编写引人入胜的介绍
           - 为参与度构建内容
           - 包含相关子标题
        2. 写作卓越性 ✍️
           - 平衡专业性和可及性
           - 使用清晰、引人入胜的语言
           - 包含相关示例
           - 自然地融入统计数据
        3. 来源整合 🔍
           - 正确引用来源
           - 包含专家引用
           - 保持事实准确性
        4. 数字优化 💻
           - 为扫描性构建结构
           - 包含可分享的要点
           - 针对SEO优化
           - 添加引人入胜的子标题\
        """
        ),
        expected_output=dedent(
            """\
        # {病毒式标题}

        ## 介绍
        {引人入胜的钩子和背景}

        ## {引人入胜的第一部分}
        {关键见解和分析}
        {专家引用和统计}

        ## {引人入胜的第二部分}
        {深入探索}
        {现实世界示例}

        ## {实用的第三部分}
        {可行见解}
        {专家建议}

        ## 关键要点
        - {可分享见解1}
        - {实用要点2}
        - {值得注意的发现3}

        ## 来源
        {带链接的正确归属来源}\
        """
        ),
        markdown=True,
    )

    async def arun(  # type: ignore
            self,
            topic: str,
            use_search_cache: bool = True,
            use_scrape_cache: bool = True,
            use_cached_report: bool = True,
            **kwargs,
    ) -> AsyncIterator[Union[WorkflowCompletedEvent, RunResponseEvent]]:
        """
        运行博客文章生成工作流

        Args:
            topic: 博客文章主题
            use_search_cache: 是否使用搜索缓存
            use_scrape_cache: 是否使用抓取缓存
            use_cached_report: 是否使用缓存的报告
        """
        logger.info(f"生成关于以下主题的博客文章：{topic}")

        if self.run_id is None:
            raise ValueError("运行ID未设置")

        # 如果use_cache为True，使用缓存的博客文章
        if use_cached_report:
            cached_blog_post = self.get_cached_blog_post(topic)
            if cached_blog_post:
                yield WorkflowCompletedEvent(
                    run_id=self.run_id, content=cached_blog_post
                )
                return

        # 在网络上搜索关于该主题的文章
        search_results: Optional[SearchResults] = await self.get_search_results(
            topic, use_search_cache
        )
        # 如果没有找到关于该主题的搜索结果，结束工作流
        if search_results is None or len(search_results.articles) == 0:
            yield WorkflowCompletedEvent(
                run_id=self.run_id,
                content=f"抱歉，找不到关于主题的任何文章：{topic}",
            )
            return

        # 抓取搜索结果
        scraped_articles: Dict[str, ScrapedArticle] = await self.scrape_articles(
            topic, search_results, use_scrape_cache
        )

        # 为作者准备输入
        writer_input = {
            "topic": topic,
            "articles": [v.model_dump() for v in scraped_articles.values()],
        }

        # 运行作者并产出响应
        writer_response = await self.writer.arun(
            json.dumps(writer_input, indent=4),
            stream=True,
            stream_intermediate_steps=True
        )
        async for response in writer_response:
            yield response

        # 将博客文章保存在缓存中
        if self.writer.run_response:
            self.add_blog_post_to_cache(topic, str(self.writer.run_response.content))

    def get_cached_blog_post(self, topic: str) -> Optional[str]:
        """获取缓存的博客文章"""
        logger.info("检查是否存在缓存的博客文章")
        return self.session_state.get("blog_posts", {}).get(topic)

    def add_blog_post_to_cache(self, topic: str, blog_post: str):
        """将博客文章添加到缓存"""
        logger.info(f"保存主题的博客文章：{topic}")
        self.session_state.setdefault("blog_posts", {})
        self.session_state["blog_posts"][topic] = blog_post

    def get_cached_search_results(self, topic: str) -> Optional[SearchResults]:
        """获取缓存的搜索结果"""
        logger.info("检查是否存在缓存的搜索结果")
        search_results = self.session_state.get("search_results", {}).get(topic)
        return (
            SearchResults.model_validate(search_results)
            if search_results and isinstance(search_results, dict)
            else search_results
        )

    def add_search_results_to_cache(self, topic: str, search_results: SearchResults):
        """将搜索结果添加到缓存"""
        logger.info(f"保存主题的搜索结果：{topic}")
        self.session_state.setdefault("search_results", {})
        self.session_state["search_results"][topic] = search_results

    def get_cached_scraped_articles(self, topic: str) -> Optional[Dict[str, ScrapedArticle]]:
        """获取缓存的抓取文章"""
        logger.info("检查是否存在缓存的抓取文章")
        scraped_articles = self.session_state.get("scraped_articles", {}).get(topic)
        return scraped_articles if scraped_articles and isinstance(scraped_articles, dict) else None

    def add_scraped_articles_to_cache(
            self, topic: str, scraped_articles: Dict[str, ScrapedArticle]
    ):
        """将抓取的文章添加到缓存"""
        logger.info(f"保存主题的抓取文章：{topic}")
        self.session_state.setdefault("scraped_articles", {})
        self.session_state["scraped_articles"][topic] = scraped_articles

    async def get_search_results(
            self, topic: str, use_search_cache: bool, num_attempts: int = 3
    ) -> Optional[SearchResults]:
        """获取搜索结果"""
        # 如果use_search_cache为True，从会话状态获取缓存的搜索结果
        if use_search_cache:
            try:
                search_results_from_cache = self.get_cached_search_results(topic)
                if search_results_from_cache is not None:
                    search_results = SearchResults.model_validate(
                        search_results_from_cache
                    )
                    logger.info(f"在缓存中找到{len(search_results.articles)}篇文章。")
                    return search_results
            except Exception as e:
                logger.warning(f"无法从缓存读取搜索结果：{e}")

        # 如果没有缓存的搜索结果，使用搜索器查找最新文章
        for attempt in range(num_attempts):
            try:
                searcher_response: RunResponse = await self.searcher.arun(topic)
                if (
                        searcher_response is not None
                        and searcher_response.content is not None
                        and isinstance(searcher_response.content, SearchResults)
                ):
                    article_count = len(searcher_response.content.articles)
                    logger.info(f"在第{attempt + 1}次尝试中找到{article_count}篇文章")
                    # 缓存搜索结果
                    self.add_search_results_to_cache(topic, searcher_response.content)
                    return searcher_response.content
                else:
                    logger.warning(
                        f"第{attempt + 1}/{num_attempts}次尝试失败：无效的响应类型"
                    )
            except Exception as e:
                logger.warning(f"第{attempt + 1}/{num_attempts}次尝试失败：{str(e)}")

        logger.error(f"在{num_attempts}次尝试后获取搜索结果失败")
        return None

    async def scrape_articles(
            self, topic: str, search_results: SearchResults, use_scrape_cache: bool
    ) -> Dict[str, ScrapedArticle]:
        """抓取文章内容"""
        scraped_articles: Dict[str, ScrapedArticle] = {}

        # 如果use_scrape_cache为True，从会话状态获取缓存的抓取文章
        if use_scrape_cache:
            try:
                scraped_articles_from_cache = self.get_cached_scraped_articles(topic)
                if scraped_articles_from_cache is not None:
                    scraped_articles = scraped_articles_from_cache
                    logger.info(f"在缓存中找到{len(scraped_articles)}篇抓取的文章。")
                    return scraped_articles
            except Exception as e:
                logger.warning(f"无法从缓存读取抓取的文章：{e}")

        # 抓取不在缓存中的文章
        for article in search_results.articles:
            if article.url in scraped_articles:
                logger.info(f"在缓存中找到抓取的文章：{article.url}")
                continue

            try:
                article_scraper_response: RunResponse = await self.article_scraper.arun(
                    article.url
                )
                if (
                        article_scraper_response is not None
                        and article_scraper_response.content is not None
                        and isinstance(article_scraper_response.content, ScrapedArticle)
                ):
                    scraped_articles[article_scraper_response.content.url] = (
                        article_scraper_response.content
                    )
                    logger.info(f"成功抓取文章：{article_scraper_response.content.url}")
                else:
                    logger.warning(f"抓取文章失败，响应无效：{article.url}")
            except Exception as e:
                logger.error(f"抓取文章时发生异常：{article.url}, 错误：{e}")

        # 将抓取的文章保存在会话状态中
        self.add_scraped_articles_to_cache(topic, scraped_articles)
        return scraped_articles


async def write_blog_post(
        self, topic: str, scraped_articles: Dict[str, ScrapedArticle]
) -> AsyncIterator[RunResponseEvent]:
    """写博客文章"""
    logger.info("编写博客文章")
    # 为作者准备输入
    writer_input = {
        "topic": topic,
        "articles": [v.model_dump() for v in scraped_articles.values()],
    }
    # 运行作者并产出响应
    writer_response = await self.writer.arun(
        json.dumps(writer_input, indent=4),
        stream=True,
        stream_intermediate_steps=True
    )
    async for response in writer_response:
        yield response
    # 将博客文章保存在缓存中
    self.add_blog_post_to_cache(topic, self.writer.run_response.content)


def get_blog_post_generator(debug_mode: bool = False) -> BlogPostGenerator:
    """
    获取博客文章生成器实例

    Args:
        debug_mode: 是否开启调试模式

    Returns:
        BlogPostGenerator: 博客文章生成器实例
    """
    return BlogPostGenerator(
        workflow_id="generate-blog-post-on",
        name="文案生成器",
        storage=PostgresStorage(
            table_name="blog_post_generator_workflows",
            db_url=db_url,
            mode="workflow",
            auto_upgrade_schema=True,
        ),
        debug_mode=debug_mode,
    )
