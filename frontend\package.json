{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write \"src/**/*.{vue,js,ts,jsx,tsx,css,scss,json}\""}, "dependencies": {"@eslint/config-array": "^0.20.0", "@eslint/object-schema": "^2.1.6", "@mdit/plugin-alert": "^0.22.1", "@mdit/plugin-container": "^0.22.1", "@mdit/plugin-footnote": "^0.22.2", "@mdit/plugin-katex": "^0.23.1", "@types/dompurify": "^3.2.0", "@types/markdown-it": "^14.1.2", "@vicons/ionicons5": "^0.13.0", "@vueuse/head": "^2.0.0", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "core-js": "^3.8.3", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "flat": "^6.0.1", "highlight.js": "^11.11.1", "katex": "^0.16.22", "markdown-it": "^14.1.0", "markdown-it-highlightjs": "^4.2.0", "marked": "^15.0.11", "naive-ui": "^2.41.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "postcss": "^8.5.3", "tailwindcss": "3", "vue": "^3.2.13", "vue-i18n": "9", "vue-router": "4"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@babel/plugin-transform-class-properties": "^7.25.9", "@stagewise/toolbar-vue": "^0.1.2", "@types/node": "^22.14.1", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@vitejs/plugin-vue": "^5.2.3", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-typescript": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-typescript": "^14.5.0", "consolidate": "^1.0.4", "eslint": "8.56.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^9.23.0", "prettier": "^3.5.3", "typescript": "^5.8.3", "vite": "^6.3.4", "vite-plugin-eslint": "^1.8.1"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "resolutions": {"rimraf": "^4.0.0", "glob": "^9.0.0", "inflight": "npm:lru-cache@^7.14.1", "memfs": "^4.0.0"}}