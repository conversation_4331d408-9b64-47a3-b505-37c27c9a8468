tls:
  options:
    default:
      minVersion: VersionTLS12
      sniStrict: true
      cipherSuites:
        - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305
        - TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305
      alpnProtocols:
        - h3  # HTTP/3
        - h2  # HTTP/2
        - http/1.1

# Traefik 3.x 中间件增强配置
http:
  middlewares:
    default-headers:
      headers:
        frameDeny: true
        browserXssFilter: true
        contentTypeNosniff: true
        forceSTSHeader: true
        stsIncludeSubdomains: true
        stsPreload: true
        stsSeconds: 31536000
        
    # 流式响应专用中间件
    stream-headers:
      headers:
        customRequestHeaders:
          X-Accel-Buffering: "no"  # 禁用nginx缓冲
        customResponseHeaders:
          Cache-Control: "no-cache, no-store, must-revalidate"
          Connection: "keep-alive"
          X-Accel-Buffering: "no"
          Pragma: "no-cache"
          Expires: "0"
          Content-Encoding: "identity"  # 禁用压缩
          
    # HTTP/2优化中间件
    http2-optimize:
      headers:
        customResponseHeaders:
          # HTTP/2推送策略
          Link: "</api/v1/agents>; rel=preload; as=fetch"