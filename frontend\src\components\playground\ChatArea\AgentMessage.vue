<template>
  <MessageBubble :message="message" message-type="agent" @regenerate="regenerateResponse" />
</template>

<script lang="ts" setup>
import { useMessage } from 'naive-ui';
import type { PlaygroundChatMessage } from '@/types/playground';
import { usePlaygroundStore } from '@/stores/playground';
import { useStreamHandler } from '@/composables/useStreamHandler';
import MessageBubble from './MessageBubble.vue';

interface Props {
  message: PlaygroundChatMessage;
}

const props = defineProps<Props>();
const playgroundStore = usePlaygroundStore();
const { handleStreamResponse } = useStreamHandler();
const messageApi = useMessage();

const regenerateResponse = async () => {
  const messages = playgroundStore.messages;
  const currentIndex = messages.findIndex((m: PlaygroundChatMessage) => m === props.message);

  if (currentIndex > 0) {
    const userMessage = messages[currentIndex - 1];
    if (userMessage.role === 'user' && userMessage.content) {
      try {
        playgroundStore.setMessages(messages.filter((m: PlaygroundChatMessage) => m !== props.message));
        await handleStreamResponse(userMessage.content);
      } catch (error) {
        console.error('重新生成失败:', error);
        messageApi.error('重新生成失败，请稍后再试');
      }
    }
  }
};
</script>
