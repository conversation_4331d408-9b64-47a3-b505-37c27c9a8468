from typing import List, ForwardRef
from uuid import UUID

from sqlalchemy import Column, Boolean, UniqueConstraint
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixUUID, MixDeleted, MixTime
from app.models.rbac.model_rbac_base import RoleBase

# 避免循环导入
Permission = ForwardRef("Permission")
User = ForwardRef("User")
UserRole = ForwardRef("UserRole")
RolePermission = ForwardRef("RolePermission")


# 角色创建模型
class RoleCreate(RoleBase):
    """角色创建模型
    
    用于创建新角色
    """
    permissions: List[str] | None = Field(default=None, title="权限代码列表")


# 角色更新模型
class RoleUpdate(SQLModel):
    """角色更新模型
    
    用于更新角色信息
    """
    name: str | None = Field(None, min_length=2, max_length=50, title="角色名称")
    description: str | None = Field(None, title="角色描述")
    is_active: bool | None = Field(None, title="是否启用")
    permissions: List[str] | None = Field(default=None, title="权限代码列表")


# 角色数据库模型
class Role(RoleBase, MixUUID, MixDeleted, MixTime, table=True):
    """角色表
    
    系统中的角色定义
    """
    __tablename__ = "roles"
    __table_args__ = (
        UniqueConstraint("code", name="uq_role_code"),
        {"schema": "rbac"}
    )

    # 扩展字段
    is_system: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="是否系统角色")
    is_active: bool = Field(default=True, sa_column=Column(Boolean, nullable=False, default=True), title="是否启用")

    # 关系
    users: List["UserRole"] = Relationship(back_populates="role")
    permissions: List["RolePermission"] = Relationship(back_populates="role")


# 角色响应模型
class RoleResponse(RoleBase):
    """角色响应模型
    
    用于API响应返回角色信息
    """
    id: UUID
    is_system: bool
    is_active: bool

    class Config:
        from_attributes = True


# 角色详情响应模型
class RoleDetailResponse(RoleResponse):
    """角色详情响应模型
    
    用于API响应返回角色详细信息，包含权限列表
    """
    permissions: List[str] = Field(default=[], title="权限代码列表")


# 角色列表响应模型
class RoleListResponse(SQLModel):
    """角色列表响应模型"""
    total_count: int
    data: List[RoleResponse]
