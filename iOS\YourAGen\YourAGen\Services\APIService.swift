import Foundation
import Combine

class APIService: ObservableObject {
    static let shared = APIService()
    
    private let baseURL: String
    private let session: URLSession
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        // 从配置文件或环境变量读取API地址
        self.baseURL = "https://youragen.com" // 开发环境地址
        
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 300
        self.session = URLSession(configuration: config)
    }
    
    // MARK: - 通用请求方法
    
    private func createRequest(for endpoint: String, method: HTTPMethod = .GET, body: Data? = nil, token: String? = nil) -> URLRequest? {
        guard let url = URL(string: baseURL + endpoint) else { return nil }
        
        var request = URLRequest(url: url)
        request.httpMethod = method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        if let token = token {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        if let body = body {
            request.httpBody = body
        }
        
        return request
    }
    
    private func performRequest<T: Codable>(_ request: URLRequest, responseType: T.Type) -> AnyPublisher<T, Error> {
        return session.dataTaskPublisher(for: request)
            .map(\.data)
            .decode(type: T.self, decoder: JSONDecoder())
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    // MARK: - 认证相关API
    
    func login(email: String, password: String) -> AnyPublisher<LoginResponse, Error> {
        let loginData = LoginRequest(email: email, password: password)
        guard let body = try? JSONEncoder().encode(loginData),
              let request = createRequest(for: "/api/v1/auth/login", method: .POST, body: body) else {
            return Fail(error: APIError.invalidRequest).eraseToAnyPublisher()
        }
        
        return performRequest(request, responseType: LoginResponse.self)
    }
    
    func register(email: String, password: String, username: String?) -> AnyPublisher<RegisterResponse, Error> {
        let registerData = RegisterRequest(email: email, password: password, username: username)
        guard let body = try? JSONEncoder().encode(registerData),
              let request = createRequest(for: "/api/v1/auth/register", method: .POST, body: body) else {
            return Fail(error: APIError.invalidRequest).eraseToAnyPublisher()
        }
        
        return performRequest(request, responseType: RegisterResponse.self)
    }
    
    func getCurrentUser(token: String) async throws -> UserResponse {
        let endpoint = "/api/v1/users/me"
        guard let url = URL(string: baseURL + endpoint) else {
            throw APIError.invalidRequest
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidRequest
        }
        
        guard httpResponse.statusCode == 200 else {
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let errorMessage = errorData["message"] as? String {
                throw APIError.networkError(errorMessage)
            }
            throw APIError.networkError("获取用户信息失败")
        }
        
        let userResponse = try JSONDecoder().decode(UserResponse.self, from: data)
        return userResponse
    }
    
    // MARK: - Agent相关API
    
    func getAgents(token: String) async throws -> [Agent] {
        let endpoint = "/api/v1/playground/agents"
        guard let url = URL(string: baseURL + endpoint) else {
            throw APIError.invalidRequest
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // 只有当token不为空时才添加Authorization头
        if !token.isEmpty {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidRequest
        }
        
        guard httpResponse.statusCode == 200 else {
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let errorMessage = errorData["message"] as? String {
                throw APIError.networkError(errorMessage)
            }
            throw APIError.networkError("获取智能体列表失败")
        }
        
        let agents = try JSONDecoder().decode([Agent].self, from: data)
        return agents
    }
    
    // MARK: - 会话相关API
    
    func getSessions(agentId: String, token: String) async throws -> [Session] {
        let endpoint = "/api/v1/playground/agents/\(agentId)/sessions"
        guard let url = URL(string: baseURL + endpoint) else {
            throw APIError.invalidRequest
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidRequest
        }
        
        guard httpResponse.statusCode == 200 else {
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let errorMessage = errorData["message"] as? String {
                throw APIError.networkError(errorMessage)
            }
            throw APIError.networkError("获取会话列表失败")
        }
        
        // 添加调试信息
        if let jsonString = String(data: data, encoding: .utf8) {
            print("🔍 API响应数据: \(jsonString)")
        }
        
        // 尝试不同的解码方式
        do {
            // 首先尝试直接解码为Session数组
            let sessions = try JSONDecoder().decode([Session].self, from: data)
            print("✅ 成功解码为Session数组，共 \(sessions.count) 个会话")
            return sessions
        } catch {
            print("❌ 直接解码Session数组失败: \(error)")
            
            // 尝试解码为包装的响应格式
            do {
                let sessionResponse = try JSONDecoder().decode(SessionResponse.self, from: data)
                print("✅ 成功解码为SessionResponse格式")
                return sessionResponse.data.items
            } catch {
                print("❌ 解码SessionResponse格式失败: \(error)")
                
                // 尝试解码为通用JSON对象来查看结构
                if let jsonObject = try? JSONSerialization.jsonObject(with: data) {
                    print("🔍 JSON对象结构: \(jsonObject)")
                }
                
                throw APIError.decodingError
            }
        }
    }
    
    func getSessionMessages(sessionId: String, agentId: String, token: String) async throws -> [Message] {
        let endpoint = "/api/v1/playground/agents/\(agentId)/sessions/\(sessionId)"
        guard let url = URL(string: baseURL + endpoint) else {
            throw APIError.invalidRequest
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidRequest
        }
        
        guard httpResponse.statusCode == 200 else {
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let errorMessage = errorData["message"] as? String {
                throw APIError.networkError(errorMessage)
            }
            throw APIError.networkError("获取会话消息失败")
        }
        
        // 解析会话数据并转换为消息列表
        let sessionData = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        let runs = sessionData?["runs"] as? [[String: Any]] ?? []
        
        var messages: [Message] = []
        
        for run in runs {
            // 添加用户消息
            if let messageData = run["message"] as? [String: Any],
               let content = messageData["content"] as? String {
                let userMessage = Message(
                    role: .user,
                    content: content
                )
                messages.append(userMessage)
            }
            
            // 添加AI响应
            if let responseData = run["response"] as? [String: Any],
               let content = responseData["content"] as? String {
                let agentMessage = Message(
                    role: .agent,
                    content: content
                )
                messages.append(agentMessage)
            }
        }
        
        return messages
    }
    
    // MARK: - 认证API
    
    func login(username: String, password: String) async throws -> LoginResponse {
        let endpoint = "/api/v1/auth/login"
        guard let url = URL(string: baseURL + endpoint) else {
            throw APIError.invalidRequest
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        // 使用FormData格式
        let boundary = UUID().uuidString
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        var body = Data()
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"username\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(username)\r\n".data(using: .utf8)!)
        
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"password\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(password)\r\n".data(using: .utf8)!)
        
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        request.httpBody = body
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidRequest
        }
        
        guard httpResponse.statusCode == 200 else {
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let errorMessage = errorData["message"] as? String {
                throw APIError.networkError(errorMessage)
            }
            throw APIError.networkError("登录失败")
        }
        
        let loginResponse = try JSONDecoder().decode(LoginResponse.self, from: data)
        return loginResponse
    }
    
    func register(name: String, email: String, password: String) async throws -> RegisterResponse {
        let endpoint = "/api/v1/auth/register"
        guard let url = URL(string: baseURL + endpoint) else {
            throw APIError.invalidRequest
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        
        // 使用FormData格式
        let boundary = UUID().uuidString
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        var body = Data()
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"name\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(name)\r\n".data(using: .utf8)!)
        
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"email\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(email)\r\n".data(using: .utf8)!)
        
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"password\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(password)\r\n".data(using: .utf8)!)
        
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        request.httpBody = body
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidRequest
        }
        
        guard httpResponse.statusCode == 201 else {
            if let errorData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let errorMessage = errorData["message"] as? String {
                throw APIError.networkError(errorMessage)
            }
            throw APIError.networkError("注册失败")
        }
        
        let registerResponse = try JSONDecoder().decode(RegisterResponse.self, from: data)
        return registerResponse
    }
    
    // MARK: - 流式聊天API
    
    func sendMessage(
        message: String,
        agentModel: String,
        sessionId: String?,
        token: String,
        onChunk: @escaping (StreamResponse) -> Void,
        onComplete: @escaping () -> Void,
        onError: @escaping (Error) -> Void
    ) {
        let endpoint = "/api/v1/playground/agents/\(agentModel)/runs"
        guard let url = URL(string: baseURL + endpoint) else {
            onError(APIError.invalidRequest)
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        request.setValue("text/event-stream", forHTTPHeaderField: "Accept")
        request.setValue("keep-alive", forHTTPHeaderField: "Connection")
        request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")
        
        // 构建FormData
        let boundary = UUID().uuidString
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        
        var body = Data()
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"message\"\r\n\r\n".data(using: .utf8)!)
        body.append("\(message)\r\n".data(using: .utf8)!)
        
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"stream\"\r\n\r\n".data(using: .utf8)!)
        body.append("true\r\n".data(using: .utf8)!)
        
        if let sessionId = sessionId {
            body.append("--\(boundary)\r\n".data(using: .utf8)!)
            body.append("Content-Disposition: form-data; name=\"session_id\"\r\n\r\n".data(using: .utf8)!)
            body.append("\(sessionId)\r\n".data(using: .utf8)!)
        }
        
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)
        request.httpBody = body
        
        // 创建流式数据处理器
        let streamHandler = StreamDataHandler(
            onChunk: onChunk,
            onComplete: onComplete,
            onError: onError
        )
        
        // 创建自定义URLSession用于流式处理
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 0 // 不设置超时
        config.timeoutIntervalForResource = 0
        let session = URLSession(configuration: config, delegate: streamHandler, delegateQueue: nil)
        
        let task = session.dataTask(with: request)
        streamHandler.task = task
        task.resume()
    }
    
    // MARK: - 流式数据处理器
    private class StreamDataHandler: NSObject, URLSessionDataDelegate {
        private var buffer = ""
        private let onChunk: (StreamResponse) -> Void
        private let onComplete: () -> Void
        private let onError: (Error) -> Void
        weak var task: URLSessionDataTask?
        
        init(
            onChunk: @escaping (StreamResponse) -> Void,
            onComplete: @escaping () -> Void,
            onError: @escaping (Error) -> Void
        ) {
            self.onChunk = onChunk
            self.onComplete = onComplete
            self.onError = onError
        }
        
        func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {
            guard let httpResponse = response as? HTTPURLResponse else {
                DispatchQueue.main.async {
                    self.onError(APIError.invalidRequest)
                }
                completionHandler(.cancel)
                return
            }
            
            guard httpResponse.statusCode == 200 else {
                DispatchQueue.main.async {
                    self.onError(APIError.networkError("HTTP \(httpResponse.statusCode)"))
                }
                completionHandler(.cancel)
                return
            }
            
            completionHandler(.allow)
        }
        
        func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
            guard let dataString = String(data: data, encoding: .utf8) else {
                DispatchQueue.main.async {
                    self.onError(APIError.decodingError)
                }
                return
            }
            
            // 将新数据添加到缓冲区
            buffer += dataString
            print("🔄 接收到数据: \(dataString)")
            
            // 处理缓冲区中的所有完整JSON对象
            while let jsonData = extractCompleteJSON(from: &buffer) {
                if let streamResponse = parseStreamResponse(jsonData) {
                    DispatchQueue.main.async {
                        self.onChunk(streamResponse)
                    }
                    
                    // 如果是完成事件，调用完成回调
                    if streamResponse.event == .runCompleted {
                        DispatchQueue.main.async {
                            self.onComplete()
                        }
                        task?.cancel()
                        return
                    }
                }
            }
        }
        
        func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didCompleteWithError error: Error?) {
            if let error = error {
                DispatchQueue.main.async {
                    self.onError(error)
                }
            } else {
                // 如果没有错误但连接结束了，也调用完成回调
                DispatchQueue.main.async {
                    self.onComplete()
                }
            }
        }
        
        private func extractCompleteJSON(from buffer: inout String) -> Data? {
            // 查找完整的JSON对象
            var braceCount = 0
            var startIndex: String.Index?
            var endIndex: String.Index?
            
            for (index, char) in buffer.enumerated() {
                let stringIndex = buffer.index(buffer.startIndex, offsetBy: index)
                
                if char == "{" {
                    if braceCount == 0 {
                        startIndex = stringIndex
                    }
                    braceCount += 1
                } else if char == "}" {
                    braceCount -= 1
                    if braceCount == 0 && startIndex != nil {
                        endIndex = buffer.index(after: stringIndex)
                        break
                    }
                }
            }
            
            guard let start = startIndex, let end = endIndex else {
                return nil
            }
            
            let jsonString = String(buffer[start..<end])
            buffer.removeSubrange(buffer.startIndex..<end)
            
            return jsonString.data(using: .utf8)
        }
        
        private func parseStreamResponse(_ data: Data) -> StreamResponse? {
            do {
                let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
                
                // 添加调试信息
                print("📥 收到流式响应: \(json ?? [:])")
                
                let content = json?["content"] as? String ?? ""
                let eventString = json?["event"] as? String ?? "response"
                let sessionId = json?["session_id"] as? String
                
                // 处理不同的事件类型格式
                let event: StreamEventType?
                switch eventString.lowercased() {
                case "runstarted", "run_started":
                    event = .runStarted
                case "runresponse", "run_response":
                    event = .runResponse
                case "runerror", "run_error":
                    event = .runError
                case "runcompleted", "run_completed", "completed":
                    event = .runCompleted
                case "toolcallstarted", "tool_call_started":
                    event = .toolCallStarted
                case "toolcallcompleted", "tool_call_completed":
                    event = .toolCallCompleted
                case "reasoningstarted", "reasoning_started":
                    event = .reasoningStarted
                case "reasoningstep", "reasoning_step":
                    event = .reasoningStep
                case "reasoningcompleted", "reasoning_completed":
                    event = .reasoningCompleted
                case "memoryupdated", "memory_updated":
                    event = .memoryUpdated
                case "workflowstarted", "workflow_started":
                    event = .workflowStarted
                case "workflowcompleted", "workflow_completed":
                    event = .workflowCompleted
                default:
                    // 忽略未识别的事件类型，避免显示调试信息
                    print("⚠️ 忽略未识别的事件类型: \(eventString)")
                    return nil
                }
                
                // 如果事件类型无效，直接返回nil
                guard let validEvent = event else {
                    return nil
                }
                
                print("📝 解析事件: \(eventString) -> \(validEvent), 内容: \(content)")
                
                return StreamResponse(
                    event: validEvent,
                    sessionId: sessionId,
                    content: content.isEmpty ? nil : content,
                    tools: nil,
                    images: nil,
                    audio: nil,
                    videos: nil,
                    responseAudio: nil,
                    messages: nil,
                    createdAt: Date().timeIntervalSince1970
                )
            } catch {
                print("❌ JSON解析失败: \(error)")
                // 如果JSON解析失败，尝试作为纯文本处理
                if let content = String(data: data, encoding: .utf8), !content.isEmpty {
                    print("📄 作为纯文本处理: \(content)")
                    return StreamResponse(
                        content: content,
                        isComplete: false,
                        error: nil
                    )
                }
                return nil
            }
        }
    }
}

// MARK: - 数据结构

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
}

enum APIError: Error, LocalizedError {
    case invalidRequest
    case noData
    case decodingError
    case networkError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidRequest:
            return "无效的请求"
        case .noData:
            return "没有数据"
        case .decodingError:
            return "数据解析失败"
        case .networkError(let message):
            return "网络错误: \(message)"
        }
    }
}

// 请求数据结构
struct LoginRequest: Codable {
    let email: String
    let password: String
}

struct RegisterRequest: Codable {
    let email: String
    let password: String
    let username: String?
}

// 响应数据结构 - 直接返回数据，不包装在success结构中
struct LoginResponse: Codable {
    let access_token: String
    let refresh_token: String
    let token_type: String
}

struct RegisterResponse: Codable {
    let access_token: String
    let refresh_token: String
    let token_type: String
}

// UserResponse直接返回User数据，不包装
typealias UserResponse = User

struct APIAgentResponse: Codable {
    let success: Bool
    let data: [Agent]
    let message: String?
    let code: Int?
}

struct APISessionResponse: Codable {
    let success: Bool
    let data: APISessionData
    let message: String?
    let code: Int?
}

struct APISessionData: Codable {
    let items: [Session]
    let total: Int
    let page: Int
    let size: Int
    let pages: Int
}

struct APISessionDetailResponse: Codable {
    let success: Bool
    let data: [Message]
    let message: String?
    let code: Int?
} 