# 通用
.git
.gitignore
.github
.gitlab-ci.yml
README.md
LICENSE
CHANGELOG.md
*.md
.DS_Store
.vscode
.idea

# 构建文件
Dockerfile
.dockerignore
docker-compose.yml
docker-compose.*.yml

# 后端 (Python)
**/__pycache__/
**/*.py[cod]
**/*.so
**/.Python
**/env/
**/venv/
**/.venv/
**/build/
**/dist/
**/*.egg-info/

# 前端 (Node.js)
**/node_modules/
**/npm-debug.log
**/yarn-debug.log
**/yarn-error.log
**/coverage/

# iOS / Swift
**/xcuserdata/
**/*.xcscmblueprint
**/*.xccheckout
**/DerivedData/
**/*.moved-aside
**/*.pbxuser
**/*.mode1v3
**/*.mode2v3
**/*.perspectivev3
**/*.ipa
**/*.dSYM.zip
**/*.dSYM
**/.build/
**/Pods/
**/Carthage/

# 日志文件
**/*.log
**/logs/
**/tmp/

# 环境文件 (注意：这些文件需要在构建时传入，不应包括在镜像中)
**/.env
.env.example
.env.local
