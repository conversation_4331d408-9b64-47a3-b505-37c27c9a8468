import Foundation
import Combine

@MainActor
class PlaygroundStore: ObservableObject {
    // MARK: - Published Properties
    @Published var agents: [Agent] = []
    @Published var selectedAgent: Agent?
    @Published var messages: [Message] = []
    @Published var sessions: [Session] = []
    @Published var currentSessionId: String?
    @Published var isLoading = false
    @Published var isStreaming = false
    @Published var errorMessage: String?
    @Published var streamingErrorMessage: String?
    @Published var lastMessage: Message?
    @Published var feedbackMessage: String?
    
    // MARK: - Private Properties
    private let apiService = APIService()
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Computed Properties
    var hasMessages: Bool {
        !messages.isEmpty
    }
    
    var isReady: Bool {
        !agents.isEmpty && selectedAgent != nil
    }
    
    // MARK: - Agent Management
    func loadAgents(token: String) {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let loadedAgents = try await apiService.getAgents(token: token)
                await MainActor.run {
                    self.agents = loadedAgents
                    self.isLoading = false
                    
                    // 如果没有选中的智能体，选择第一个并加载其会话
                    if self.selectedAgent == nil && !loadedAgents.isEmpty {
                        self.selectedAgent = loadedAgents.first
                        // 自动加载默认选择智能体的会话数据
                        if let firstAgent = loadedAgents.first {
                            self.loadSessions(agentId: firstAgent.agentId, token: token)
                        }
                    }
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "加载智能体失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    func selectAgent(_ agent: Agent, token: String) {
        selectedAgent = agent
        // 切换智能体时清空当前会话和消息
        currentSessionId = nil
        messages = []
        // 加载新智能体的会话列表
        loadSessions(agentId: agent.agentId, token: token)
    }
    
    // MARK: - Session Management
    func loadSessions(agentId: String, token: String) {
        Task {
            do {
                let loadedSessions = try await apiService.getSessions(agentId: agentId, token: token)
                await MainActor.run {
                    self.sessions = loadedSessions
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "加载会话失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    func selectSession(_ session: Session, token: String) {
        // 检查是否已经是当前会话，显示反馈消息
        if currentSessionId == session.sessionId {
            feedbackMessage = "当前对话已选中"
            // 3秒后自动清除反馈消息
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                self.feedbackMessage = nil
            }
            return
        }
        
        // 检查是否正在加载，避免重复点击
        if isLoading {
            return
        }
        
        // 使用当前选中的智能体ID，如果会话没有agentId的话
        let agentId = session.agentId ?? selectedAgent?.agentId
        
        guard let agentId = agentId else { 
            errorMessage = "会话缺少智能体信息，且当前未选择智能体"
            return 
        }
        
        // 先设置当前会话ID，避免UI状态闪烁
        currentSessionId = session.sessionId
        
        // 加载会话消息
        loadSessionMessages(sessionId: session.sessionId, agentId: agentId, token: token)
    }
    
    func loadSessionMessages(sessionId: String, agentId: String, token: String) {
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let sessionMessages = try await apiService.getSessionMessages(sessionId: sessionId, agentId: agentId, token: token)
                await MainActor.run {
                    self.messages = sessionMessages
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "加载会话消息失败: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    // MARK: - Message Management
    func addMessage(_ message: Message) {
        messages.append(message)
        lastMessage = message
    }
    
    func updateLastMessage(_ updater: (inout Message) -> Void) {
        guard !messages.isEmpty else { return }
        var lastMessage = messages[messages.count - 1]
        updater(&lastMessage)
        messages[messages.count - 1] = lastMessage
        self.lastMessage = lastMessage
    }
    
    func clearMessages() {
        messages = []
        lastMessage = nil
        currentSessionId = nil
    }
    
    // MARK: - Stream Response Processing
    func processStreamChunk(_ chunk: StreamResponse) {
        switch chunk.event {
        case .runStarted:
            if let sessionId = chunk.sessionId {
                currentSessionId = sessionId
                
                // 创建新会话数据
                let sessionData = Session(
                    sessionId: sessionId,
                    title: messages.last?.content ?? "新对话",
                    agentId: selectedAgent?.agentId,
                    agentName: selectedAgent?.name,
                    messageCount: 0,
                    lastMessage: nil,
                    createdAt: chunk.createdAt ?? Date().timeIntervalSince1970,
                    updatedAt: chunk.createdAt ?? Date().timeIntervalSince1970
                )
                
                // 检查会话是否已存在
                if !sessions.contains(where: { $0.sessionId == sessionId }) {
                    sessions.insert(sessionData, at: 0)
                }
            }
            
            // 确保有agent消息存在
            if messages.isEmpty || messages.last?.role != .agent {
                let agentMessage = Message(
                    role: .agent,
                    content: "",
                    createdAt: Date().timeIntervalSince1970,
                    isStreaming: true
                )
                addMessage(agentMessage)
            }
            
            // RunStarted事件不处理content内容，避免显示调试信息
            
        case .runResponse:
            // 确保有agent消息存在
            if messages.isEmpty || messages.last?.role != .agent {
                let agentMessage = Message(
                    role: .agent,
                    content: "",
                    createdAt: Date().timeIntervalSince1970,
                    isStreaming: true
                )
                addMessage(agentMessage)
            }
            
            updateLastMessage { message in
                if let content = chunk.content {
                    message.content = (message.content.isEmpty ? "" : message.content) + content
                }
                
                // 更新其他属性
                if let tools = chunk.tools {
                    message.toolCalls = tools
                }
                if let images = chunk.images {
                    message.images = images
                }
                if let audio = chunk.audio {
                    message.audio = audio
                }
                if let videos = chunk.videos {
                    message.videos = videos
                }
                if let responseAudio = chunk.responseAudio {
                    message.responseAudio = responseAudio
                }
                
                message.createdAt = chunk.createdAt ?? message.createdAt
            }
            
        case .runError:
            updateLastMessage { message in
                message.streamingError = true
            }
            streamingErrorMessage = chunk.content ?? "运行出错"
            
        case .runCompleted:
            // 确保最后一条消息不再是streaming状态
            updateLastMessage { message in
                message.isStreaming = false
            }
            
            // 处理最终的metrics信息
            if let messages = chunk.messages {
                let assistantMessage = messages.first { $0.role == .agent }
                if let metrics = assistantMessage?.metrics {
                    updateLastMessage { message in
                        message.metrics = metrics
                    }
                }
            }
            
            // RunCompleted事件不处理content内容，避免显示调试信息
            
            isStreaming = false
            
        case .toolCallStarted, .toolCallCompleted:
            if let tools = chunk.tools {
                updateLastMessage { message in
                    message.toolCalls = tools
                }
            }
            
        case .reasoningStarted:
            // 推理开始，不处理content内容
            print("🧠 推理开始")
            
        case .reasoningStep:
            // 推理步骤，不处理content内容
            print("🧠 推理步骤")
            
        case .reasoningCompleted:
            // 推理完成，不处理content内容
            print("🧠 推理完成")
            
        case .memoryUpdated:
            // 内存更新，不处理content内容，避免显示调试信息
            print("💾 内存已更新")
            
        case .workflowStarted:
            // 工作流开始，不处理content内容
            print("🔄 工作流开始")
            
        case .workflowCompleted:
            // 工作流完成，不处理content内容
            print("✅ 工作流完成")
        }
    }
    
    // MARK: - Error Handling
    func handleStreamError(_ error: Error, errorMessage: String = "未知错误") {
        print("Stream error: \(error)")
        updateLastMessage { message in
            message.streamingError = true
        }
        streamingErrorMessage = error.localizedDescription
    }
    
    func cleanupErrorMessages() {
        guard messages.count >= 2 else { return }
        
        let lastMessage = messages[messages.count - 1]
        let secondLastMessage = messages[messages.count - 2]
        
        if lastMessage.role == .agent && lastMessage.streamingError && secondLastMessage.role == .user {
            messages.removeLast(2)
        }
    }
    
    // MARK: - Send Message
    func sendMessage(_ content: String, token: String) {
        guard let selectedAgent = selectedAgent else {
            errorMessage = "请先选择智能体"
            return
        }
        
        isStreaming = true
        streamingErrorMessage = nil
        
        // 清理错误消息
        cleanupErrorMessages()
        
        // 添加用户消息
        let userMessage = Message(
            role: .user,
            content: content,
            createdAt: Date().timeIntervalSince1970
        )
        addMessage(userMessage)
        
        // 发送流式请求
        apiService.sendMessage(
            message: content,
            agentModel: selectedAgent.agentId,
            sessionId: currentSessionId,
            token: token,
            onChunk: { [weak self] chunk in
                Task { @MainActor in
                    self?.processStreamChunk(chunk)
                }
            },
            onComplete: { [weak self] in
                Task { @MainActor in
                    self?.isStreaming = false
                }
            },
            onError: { [weak self] error in
                Task { @MainActor in
                    self?.handleStreamError(error)
                    self?.isStreaming = false
                }
            }
        )
    }
    
    // MARK: - New Chat
    func startNewChat() {
        currentSessionId = nil
        
        // 延迟清空消息，让UI有时间适应
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.05) {
            self.messages = []
            self.lastMessage = nil
        }
    }
} 