<template>
  <div class="space-y-2 mx-1 min-w-0">
    <!-- 自定义下拉选择器 -->
    <div class="relative min-w-0">
      <!-- 选择框 -->
      <div
        class="w-full px-2 py-1.5 rounded-lg cursor-pointer transition-all duration-300 backdrop-blur-sm group select-none relative overflow-hidden min-w-0 bg-gray-100 dark:bg-[#1a1a1a] hover:bg-gray-200 dark:hover:bg-[#2a2a2a]"
        @click="toggleDropdown"
        @mousedown.prevent.stop
        @selectstart.prevent
        @dragstart.prevent
        style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
      >
        <div v-if="selectedAgentInfo" class="flex items-center gap-2 pr-6 select-none relative z-10 min-w-0">
          <div class="flex h-5 w-5 items-center justify-center rounded-full transition-all duration-200 relative overflow-hidden shrink-0 bg-gray-200 dark:bg-[#303030]">
            <n-icon color="#6B7280" size="12" class="relative z-10 select-none">
              <RocketOutline />
            </n-icon>
          </div>
          <div class="flex-1 min-w-0">
            <span
              class="text-xs font-semibold text-gray-900 dark:text-white truncate select-none"
              style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
            >
              {{ selectedAgentInfo.label }}
            </span>
          </div>
        </div>
        <div
          v-else
          class="text-xs font-medium pr-6 select-none relative z-10 truncate text-gray-600 dark:text-[#e0e0e0]"
          style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
        >
          选择一个智能体
        </div>
        <!-- 箭头图标 -->
        <div class="absolute right-2 top-1/2 transform -translate-y-1/2 transition-all duration-300 group-hover:scale-110 text-gray-600 dark:text-gray-300">
          <n-icon size="12" :class="[isDropdownOpen ? 'rotate-180' : 'rotate-0', 'transition-transform duration-300']">
            <ChevronDownOutline />
          </n-icon>
        </div>
      </div>

      <!-- 下拉选项 -->
      <Transition name="dropdown" mode="out-in">
        <div
          v-if="isDropdownOpen"
          class="absolute top-full left-0 right-0 z-50 mt-1 rounded-lg backdrop-blur-sm shadow-lg overflow-hidden select-none bg-white dark:bg-[#1a1a1a]"
          style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
        >
          <div
            v-for="option in agentOptions"
            :key="option.value"
            class="flex items-center gap-2 px-2 py-2 cursor-pointer transition-all duration-200 first:rounded-t-lg last:rounded-b-lg group select-none relative overflow-hidden min-w-0 hover:bg-gray-200 dark:hover:bg-[#2a2a2a]"
            @click="selectOption(option)"
            @mousedown.prevent.stop
            @selectstart.prevent
            @dragstart.prevent
            style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
          >
            <div class="flex h-5 w-5 items-center justify-center rounded-full transition-all duration-200 relative overflow-hidden z-10 shrink-0 bg-gray-200 dark:bg-[#303030]">
              <n-icon color="#6B7280" size="12" class="relative z-10 select-none">
                <RocketOutline />
              </n-icon>
            </div>
            <div class="flex-1 min-w-0 relative z-10">
              <div
                class="font-bold text-gray-900 dark:text-white text-xs truncate group-hover:text-gray-800 dark:group-hover:text-gray-100 transition-colors duration-200 select-none"
                style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
              >
                {{ option.label }}
              </div>
              <div
                class="text-xs mt-0.5 font-medium group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors duration-200 select-none truncate text-gray-600 dark:text-[#e0e0e0]"
                style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
              >
                智能助手
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { NIcon } from 'naive-ui';
import { ChevronDownOutline, RocketOutline } from '@vicons/ionicons5';
import { usePlaygroundStore } from '@/stores/playground';
import { useStreamHandler } from '@/composables/useStreamHandler';
import { useRouter } from 'vue-router';

const playgroundStore = usePlaygroundStore();
const { fetchSessions } = useStreamHandler();
const router = useRouter();

const isDropdownOpen = ref(false);

const selectedAgentId = computed({
  get: () => playgroundStore.selectedAgent,
  set: (value: string) => playgroundStore.setSelectedAgent(value),
});

const agentOptions = computed(() => {
  const options = playgroundStore.agents.map(agent => ({
    label: agent.label,
    value: agent.value,
    description: agent.description,
  }));
  return options;
});

const selectedAgentInfo = computed(() => {
  return agentOptions.value.find(agent => agent.value === selectedAgentId.value);
});

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
};

const selectOption = async (option: any) => {
  // 检查是否切换到了不同的智能体
  const isChangingAgent = selectedAgentId.value !== option.value;
  
  selectedAgentId.value = option.value;
  isDropdownOpen.value = false;

  // 只有在切换智能体时才清空状态和URL
  if (isChangingAgent) {
    // 清空当前消息和会话状态
    playgroundStore.clearMessages();
    playgroundStore.setCurrentSessionId(null);
    
    // 更新URL，只保留agent-id参数，清除session-id
    try {
      await router.push({ 
        name: 'Playground',
        query: { 'agent-id': option.value }
      });
    } catch (error) {
      console.log('Router navigation handled:', error);
    }

    // 重新加载该智能体的会话数据
    if (option.value) {
      try {
        await fetchSessions();
      } catch (error) {
        console.error('切换智能体后重新加载会话失败:', error);
      }
    }
  }
};

// 点击外部关闭下拉框
const handleClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.relative')) {
    isDropdownOpen.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
/* 自定义滚动条样式 */
.max-h-48::-webkit-scrollbar {
  width: 6px;
}
.max-h-48::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}
.max-h-48::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgb(75 85 99), rgb(55 65 81));
  border-radius: 3px;
  border: 1px solid rgb(55 65 81);
}

.max-h-48::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgb(107 114 128), rgb(75 85 99));
}

/* Firefox 滚动条样式 */
.max-h-48 {
  scrollbar-width: thin;
  scrollbar-color: rgb(75 85 99) transparent;
}
</style>
