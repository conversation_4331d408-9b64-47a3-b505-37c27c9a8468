<template>
  <div
    class="relative flex h-[56px] w-[170px] cursor-default flex-col justify-between overflow-hidden rounded-md bg-gray-50 dark:bg-dark-secondary hover:bg-gray-100 dark:hover:bg-dark p-2 transition-all duration-200"
  >
    <p class="text-xs font-medium text-gray-900 dark:text-gray-100">{{ reference.name }}</p>
    <p class="truncate text-xs text-gray-700 dark:text-gray-400">{{ reference.content }}</p>
  </div>
</template>

<script lang="ts" setup>
import type { Reference } from '@/types/playground';

interface Props {
  reference: Reference;
}

defineProps<Props>();
</script>
