from binance.client import Client

# 初始化现货客户端，不传入API密钥只能访问公共数据
client = Client()
client.API_URL = 'https://data-api.binance.vision/api'

# 获取现货数据
ticker = client.get_ticker(symbol="BTCUSDT")
print("现货数据:")
print(ticker)

# 获取现货K线数据
klines = client.get_klines(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_1MINUTE, limit=1)
print("现货K线数据:")
print(klines)

# 创建用于U本位合约的客户端
futures_client = Client()
# 设置为 data-api.binance.vision 来访问U本位合约数据
futures_client.API_URL = 'https://data-api.binance.vision/futures/um'

try:
    # 获取U本位合约K线数据
    futures_klines = futures_client.futures_klines(symbol="BTCUSDT", interval="1m", limit=1)
    print("\nU本位合约K线数据:")
    print(futures_klines)

    # 获取U本位合约24小时价格统计
    futures_ticker = futures_client.futures_ticker(symbol="BTCUSDT")
    print("U本位合约24小时价格统计:")
    print(futures_ticker)

    # 获取U本位合约标记价格
    futures_price = futures_client.futures_mark_price(symbol="BTCUSDT")
    print("U本位合约标记价格:")
    print(futures_price)

    # 获取U本位合约资金费率
    funding_rate = futures_client.futures_funding_rate(symbol="BTCUSDT", limit=1)
    print("U本位合约资金费率:")
    print(funding_rate)
except Exception as e:
    print(f"获取U本位合约数据时出错: {e}")
