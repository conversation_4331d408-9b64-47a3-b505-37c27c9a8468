"""交易所API密钥模型

对应数据库中的 exchange_apikey 表，用于管理用户的交易所API凭证
"""
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Field

from app.db.db_orm_base import SchemaBase
from app.db.db_orm_base_model import MixTime
from app.models.crypto.model_symbol import ExchangeEnum


# 数据库交易所API密钥模型
class ApiKey(SchemaBase, MixTime, table=True):
    """交易所API密钥数据库模型，对应exchange_apikey表"""
    __tablename__ = "exchange_apikey"

    api_id: str = Field(..., primary_key=True, title="API标识符")
    user_id: str | None = Field(None, title="关联的用户ID")
    exchange: ExchangeEnum = Field(..., title="交易所名称")
    api_key: str = Field(..., title="交易所提供的API Key")
    secret_key: str = Field(..., title="交易所提供的Secret Key")
    passphrase: str | None = Field(None, title="部分交易所需要的密码短语")
    remark: str | None = Field(None, title="备注说明")
    params: dict | None = Field(default={}, sa_type=JSONB, title="额外参数")
    is_deleted: bool = Field(False, title="是否已删除")
