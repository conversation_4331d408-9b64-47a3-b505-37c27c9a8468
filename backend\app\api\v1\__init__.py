"""
API v1

包含系统所有v1版本的API接口
"""
from fastapi import APIRouter, Depends

from app.api.v1.endpoint_agents import router as agents_router
from app.api.v1.endpoint_auth import router as auth_router
from app.api.v1.endpoint_health import router as health_router
from app.api.v1.endpoint_notification import router as notification_router
from app.api.v1.endpoint_rbac import router as rbac_router
from app.api.v1.endpoint_users import router as users_router
from app.core.core_auth_deps import get_current_active_user

# 创建v1主路由
router = APIRouter(prefix="/v1")

# 注册模块路由
router.include_router(auth_router)
router.include_router(health_router)

# 需要认证的路由
router.include_router(users_router, dependencies=[Depends(get_current_active_user)])
router.include_router(notification_router, dependencies=[Depends(get_current_active_user)])
router.include_router(rbac_router, dependencies=[Depends(get_current_active_user)])

# AI实体统一路由（Agent、Team、Workflow）
router.include_router(agents_router, dependencies=[Depends(get_current_active_user)])

__all__ = ["router"]
