"""Agent注册中心"""
from logging import getLogger
from typing import Dict, Callable

from ai.agents.crypto_prediction_agent import get_crypto_prediction_agent
from ai.teams.multi_language import get_multi_language_team
from ai.workflow.blog_post_generator import get_blog_post_generator

logger = getLogger(__name__)

# Agent注册表 
agents_map: Dict[str, Callable] = {}


def register_agent(factory_func: Callable) -> bool:
    # 创建临时实例获取agent_id
    temp_agent = factory_func(debug_mode=False)

    # 检查实例类型而不是类名，支持继承
    if hasattr(temp_agent, 'agent_id'):
        agent_id = temp_agent.agent_id
    elif hasattr(temp_agent, 'team_id'):
        agent_id = temp_agent.team_id
    elif hasattr(temp_agent, 'workflow_id'):
        agent_id = temp_agent.workflow_id
    else:
        logger.error(f"注册失败：{factory_func.__name__} 缺少 agent_id、team_id 或 workflow_id 属性")
        return False

    # 检查是否已经注册
    if agent_id in agents_map:
        logger.warning(f"Agent '{agent_id}' 已存在，跳过重复注册")
        return True

    # 注册agent
    agents_map[agent_id] = factory_func
    logger.info(f"成功注册Agent: {agent_id}")
    return True


# 注册所有agent
register_agent(get_crypto_prediction_agent)

# 注册新的团队和工作流
register_agent(get_multi_language_team)
register_agent(get_blog_post_generator)
