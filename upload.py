import os



class Deployment:
    def __init__(self, host, user):
        self.host = host
        self.user = user

    def upload(self, test=False):
        cmd = f"rsync -avHe ssh --exclude=.git --exclude-from=.gitignore " \
              f". {self.user}@{self.host}:/root/your-ai-agent"
        if test:
            cmd = f"{cmd}_test"
        print('命令：', cmd)
        assert os.system(cmd) == 0


if __name__ == '__main__':
    d = Deployment('************', 'root')
    d.upload()
