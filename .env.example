# 数据库配置
DB_HOST=127.0.0.1
DB_NAME=ai_agent
DB_USER=ai
DB_PASSWORD=123456

# 数据库端口
DB_PORT=5432

# API配置
BACKEND_WORKER_NUM=1
BACKEND_PORT=20001
FRONTEND_PORT=20000

# Nginx 配置
NGINX_WORKER_PROCESSES=1

API_SECRET_KEY=xLmG7kJt3QwVrZ2pFnY8dBsE4cH9SaT6XyUvPN5DqA1bCR
API_TOKEN_EXPIRE_MINUTES=60

FERNET_KEY=8RRdgvcNRzx3s9QTLp86E0Ot1-RIyFUWPpIKjCalxlI=

# 日志配置
LOG_LEVEL=INFO
DEBUG=True

# 系统配置
TZ=Asia/Shanghai

#HTTP_PROXY=127.0.0.1:1080
#HTTPS_PROXY=127.0.0.1:1080
