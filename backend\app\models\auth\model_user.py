from datetime import datetime
from typing import List, Dict, Any

from pydantic import EmailStr, field_validator
from sqlalchemy import (Boolean, Column, DateTime, String, Text, SmallInteger, Identity)
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Field, Relationship

from app.db.db_orm_base import SchemaBase
from app.db.db_orm_base_model import MixUUID, MixDeleted, MixCreateTime, MixUpdateTime

# 前向引用
UserAuthProvider = Any
PasswordHistory = Any
UserStatusHistory = Any
VerificationToken = Any
UserRole = Any
Notification = Any
UserPreference = Any


# 按功能重构混入类
class MixUserBasicInfo:
    """用户基本信息混入类"""
    username: str | None = Field(..., min_length=3, max_length=50, title="用户名")
    email: EmailStr = Field(None, title="电子邮件")
    phone_number: str | None = Field(None, title="电话号码")
    display_name: str | None = Field(None, title="显示名称")
    full_name: str | None = Field(None, title="全名")


class MixUserProfile:
    """用户个人资料混入类"""
    avatar_url: str | None = Field(None, sa_column=Column(String(255)), title="头像URL")
    bio: str | None = Field(None, sa_column=Column(Text), title="个人简介")
    timezone: str | None = Field("UTC", sa_column=Column(String(50), default="UTC"), title="时区")
    locale: str | None = Field("zh-CN", sa_column=Column(String(10), default="zh-CN"), title="语言地区")


class MixUserStatus:
    """用户状态混入类"""
    is_active: bool = Field(default=True, sa_column=Column(Boolean, nullable=False, default=True), title="是否激活")
    is_locked: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="是否锁定")
    account_status: str = Field(default="pending", sa_column=Column(String(20), nullable=False, default="pending"), title="账户状态")
    status_reason: str | None = Field(None, sa_column=Column(Text), title="状态原因")


# 用户响应模型
class UserResponse(SchemaBase, MixUserBasicInfo, MixUserProfile, MixUUID, MixCreateTime, MixUserStatus):
    """用户响应模型"""
    pass


# 用户创建模型
class UserCreate(SchemaBase, MixUserBasicInfo):
    """用户创建模型"""
    password: str = Field(..., min_length=8, title="密码")
    confirm_password: str = Field(..., min_length=8, title="确认密码")

    @classmethod
    @field_validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('密码不匹配')
        return v


# 用户更新模型
class UserUpdate(SchemaBase, MixUserProfile):
    """用户更新模型"""
    phone_number: str | None = None
    display_name: str | None = None
    full_name: str | None = None


# 用户密码更新模型
class UserPasswordUpdate(SchemaBase):
    """用户密码更新模型"""
    current_password: str = Field(..., title="当前密码")
    new_password: str = Field(..., min_length=8, title="新密码")
    confirm_password: str = Field(..., min_length=8, title="确认密码")

    @classmethod
    @field_validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('密码不匹配')
        return v


# 认证相关混入类
class MixUserAuth:
    """用户认证相关字段混入类"""
    email_verified: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="邮箱已验证")
    password_hash: str | None = Field(None, sa_column=Column(String(255)), title="密码哈希")
    password_salt: str | None = Field(None, sa_column=Column(String(64)), title="密码盐")
    password_last_changed: datetime | None = Field(None, sa_column=Column(DateTime), title="密码最后修改时间")
    phone_verified: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="电话已验证")
    mfa_enabled: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="启用多因素认证")
    mfa_secret: str | None = Field(None, sa_column=Column(String(255)), title="多因素认证密钥")
    recovery_codes: Dict | None = Field(None, sa_column=Column(JSONB), title="恢复码")


# 系统信息混入类
class MixUserSystem:
    """系统相关用户字段混入类"""
    user_id: int | None = Field(sa_column=Column(SmallInteger, Identity(start=1000), unique=True, nullable=False), title="用户数字ID")
    is_system: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="是否系统用户")
    is_anonymous: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="是否匿名用户")
    last_login_at: datetime | None = Field(None, sa_column=Column(DateTime), title="最后登录时间")
    login_attempts: int = Field(default=0, sa_column=Column(SmallInteger, nullable=False, default=0), title="登录尝试次数")
    last_failed_login_at: datetime | None = Field(None, sa_column=Column(DateTime), title="最后失败登录时间")
    invite_code: str | None = Field(None, sa_column=Column(String), title="邀请码")
    preferences: Dict | None = Field(default={}, sa_column=Column(JSONB, default={}), title="偏好设置")
    remark: str | None = Field(None, sa_column=Column(String), title="备注")


# 数据库用户模型
class User(UserResponse, MixDeleted, MixUpdateTime, MixUserAuth, MixUserSystem, table=True):
    """用户数据库模型"""
    __tablename__ = "users"
    __table_args__ = ({"schema": "auth"})

    # 关系
    auth_providers: List["UserAuthProvider"] = Relationship(back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"})
    password_history: List["PasswordHistory"] = Relationship(back_populates="user")
    status_history: List["UserStatusHistory"] = Relationship(back_populates="user", sa_relationship_kwargs={"primaryjoin": "User.id == UserStatusHistory.user_id"})
    verification_tokens: List["VerificationToken"] = Relationship(back_populates="user")
    roles: List["UserRole"] = Relationship(back_populates="user")
    notifications: List["Notification"] = Relationship(back_populates="user")
    notification_preferences: List["UserPreference"] = Relationship(back_populates="user")


if __name__ == '__main__':
    import pendulum
    import asyncio


    async def t():
        count = await User.count_by(where=User.create_time > pendulum.now().date())
        print(count)


    asyncio.run(t())
