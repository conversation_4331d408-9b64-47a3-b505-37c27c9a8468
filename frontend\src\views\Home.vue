<template>
  <div class="min-h-screen transition-colors duration-300">
    <!-- 主区域 -->
    <main>
      <!-- 英雄区域 -->
      <section class="py-16 md:py-24 bg-white dark:bg-dark-secondary text-text-light dark:text-white transition-all duration-500">
        <div class="container">
          <div class="max-w-3xl mx-auto text-center">
            <h1 class="text-4xl md:text-6xl mb-6 animate-fade-in font-bold text-primary dark:text-white" v-html="$t('hero.title').replace('\n', '<br/>')"></h1>
            <p class="text-xl md:text-2xl mb-8 text-gray-600 dark:text-gray-200 animate-fade-in animation-delay-100">
              {{ $t('hero.subtitle') }}
            </p>

            <div class="flex flex-col md:flex-row gap-4 justify-center mb-12 animate-fade-in animation-delay-200">
              <router-link
                :to="{ name: 'Playground' }"
                class="btn btn-primary transition-all hover:scale-105 hover:shadow-lg shadow-md dark:btn-outline dark:border-light dark:text-dark dark:bg-light"
                >{{ $t('hero.primaryBtn') }}</router-link
              >
              <router-link
                :to="{ name: 'About' }"
                class="btn btn-outline transition-all hover:scale-105 hover:shadow-lg border-primary text-primary dark:btn-primary dark:border-light dark:bg-dark dark:text-white"
                >{{ $t('hero.secondaryBtn') }}</router-link
              >
            </div>

            <div class="text-sm animate-fade-in animation-delay-300">
              <p class="text-gray-600 dark:text-gray-200 mb-2">{{ $t('hero.feature1') }}</p>
              <div class="flex flex-wrap items-center justify-center gap-3 text-gray-600 dark:text-gray-200">
                <span>{{ $t('hero.feature2') }}</span>
                <a href="#" class="text-primary hover:text-accent dark:text-white dark:hover:text-primary-light transition-colors">{{ $t('hero.feature3') }}</a>
                <span>•</span>
                <a href="#" class="text-primary hover:text-accent dark:text-white dark:hover:text-primary-light transition-colors">{{ $t('hero.feature4') }}</a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 特性区域 -->
      <section class="py-16 md:py-24 bg-gray-50 dark:bg-dark transition-colors duration-300">
        <div class="container">
          <h2 class="text-3xl md:text-4xl text-center mb-16 dark:text-white">
            {{ $t('features.title') }}
          </h2>

          <!-- 特性卡片组 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div
              class="feature-card p-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-dark-secondary shadow-sm hover:shadow-md transition-all duration-300 relative overflow-hidden group"
            >
              <div class="flex items-start">
                <div class="mr-4 text-primary dark:text-white">
                  <n-icon size="28">
                    <ChatboxOutline />
                  </n-icon>
                </div>
                <div>
                  <h3 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">
                    {{ $t('features.card1.title') }}
                  </h3>
                  <p class="text-gray-600 dark:text-gray-300">
                    {{ $t('features.card1.description') }}
                  </p>
                </div>
              </div>
            </div>
            <div
              class="feature-card p-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-dark-secondary shadow-sm hover:shadow-md transition-all duration-300 relative overflow-hidden group"
            >
              <div class="flex items-start">
                <div class="mr-4 text-primary dark:text-white">
                  <n-icon size="28">
                    <SchoolOutline />
                  </n-icon>
                </div>
                <div>
                  <h3 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">
                    {{ $t('features.card2.title') }}
                  </h3>
                  <p class="text-gray-600 dark:text-gray-300">
                    {{ $t('features.card2.description') }}
                  </p>
                </div>
              </div>
            </div>
            <div
              class="feature-card p-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-dark-secondary shadow-sm hover:shadow-md transition-all duration-300 relative overflow-hidden group"
            >
              <div class="flex items-start">
                <div class="mr-4 text-primary dark:text-white">
                  <n-icon size="28">
                    <GridOutline />
                  </n-icon>
                </div>
                <div>
                  <h3 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">
                    {{ $t('features.card3.title') }}
                  </h3>
                  <p class="text-gray-600 dark:text-gray-300">
                    {{ $t('features.card3.description') }}
                  </p>
                </div>
              </div>
            </div>
            <div
              class="feature-card p-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-dark-secondary shadow-sm hover:shadow-md transition-all duration-300 relative overflow-hidden group"
            >
              <div class="flex items-start">
                <div class="mr-4 text-primary dark:text-white">
                  <n-icon size="28">
                    <PulseOutline />
                  </n-icon>
                </div>
                <div>
                  <h3 class="text-xl font-semibold mb-3 text-gray-800 dark:text-white">
                    {{ $t('features.card4.title') }}
                  </h3>
                  <p class="text-gray-600 dark:text-gray-300">
                    {{ $t('features.card4.description') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- FAQ区域 -->
      <section class="py-16 bg-white dark:bg-dark-alt transition-colors duration-300">
        <div class="container">
          <h2 class="text-3xl md:text-4xl text-center mb-12 text-gray-800 dark:text-white">
            {{ $t('faq.title') }}
          </h2>

          <div class="max-w-3xl mx-auto space-y-6">
            <div class="card p-6 bg-white dark:bg-dark-secondary rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-lg transition-all duration-300">
              <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-white">
                {{ $t('faq.question1.question') }}
              </h3>
              <p class="text-gray-600 dark:text-gray-300">{{ $t('faq.question1.answer') }}</p>
            </div>

            <div class="card p-6 bg-white dark:bg-dark-secondary rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-lg transition-all duration-300">
              <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-white">
                {{ $t('faq.question2.question') }}
              </h3>
              <p class="text-gray-600 dark:text-gray-300">{{ $t('faq.question2.answer') }}</p>
            </div>

            <div class="card p-6 bg-white dark:bg-dark-secondary rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-lg transition-all duration-300">
              <h3 class="text-xl font-semibold mb-2 text-gray-800 dark:text-white">
                {{ $t('faq.question3.question') }}
              </h3>
              <p class="text-gray-600 dark:text-gray-300">{{ $t('faq.question3.answer') }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- CTA区域 -->
      <section class="py-16 bg-gray-50 dark:bg-primary text-text-light dark:text-white text-center transition-colors duration-300 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-primary dark:to-accent opacity-90"></div>
        <div class="container relative z-10">
          <h2 class="text-3xl md:text-4xl mb-6 font-bold text-primary dark:text-white">
            {{ $t('cta.title') }}
          </h2>
          <p class="text-xl mb-8 max-w-2xl mx-auto text-gray-600 dark:text-white">
            {{ $t('cta.subtitle') }}
          </p>
          <a
            href="#"
            class="btn bg-primary text-white hover:bg-accent dark:bg-white dark:text-primary dark:hover:bg-gray-100 shadow-md hover:shadow-lg transition-all hover:scale-105"
            >{{ $t('cta.button') }}</a
          >
        </div>
      </section>
    </main>
  </div>
</template>

<script lang="ts" setup>
import { onBeforeMount, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useHead } from '@vueuse/head';
import { ChatboxOutline, SchoolOutline, GridOutline, PulseOutline } from '@vicons/ionicons5';
import { NIcon } from 'naive-ui';

const { locale } = useI18n();

// 为页面添加SEO元数据
useHead({
  title: 'YourAGen - Your First AI Agent Platform',
  meta: [
    {
      name: 'description',
      content:
        'YourAGen leverages cutting-edge AI technology to create personalized agents that understand your needs, learn your preferences, and help you accomplish tasks efficiently.',
    },
    {
      name: 'keywords',
      content: 'quantitative trading, AI trading, market forecasting, trading algorithms, financial technology',
    },
  ],
});

onBeforeMount(() => {
  // 确保页面加载时使用英文
  if (locale.value !== 'en') {
    locale.value = 'en';
  }

  // 设置文档语言
  document.documentElement.setAttribute('lang', locale.value);
});

onMounted(() => {
  // 添加滚动动画
  const observer = new IntersectionObserver(
    entries => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
          observer.unobserve(entry.target);
        }
      });
    },
    { threshold: 0.1 }
  );

  document.querySelectorAll('.animate-on-scroll').forEach(element => {
    observer.observe(element);
  });
});
</script>

<style scoped>
.animate-fade-in {
  opacity: 0;
  transform: translateY(10px);
  animation: fadeIn 0.6s ease-out forwards;
}

.animation-delay-100 {
  animation-delay: 0.1s;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-300 {
  animation-delay: 0.3s;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-on-scroll {
  opacity: 0;
  transform: translateY(20px);
  transition:
    opacity 0.6s ease-out,
    transform 0.6s ease-out;
}

.animate-on-scroll.visible {
  opacity: 1;
  transform: translateY(0);
}
</style>
