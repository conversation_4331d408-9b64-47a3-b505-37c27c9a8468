{"log": {"loglevel": "warning"}, "inbounds": [{"port": 8072, "protocol": "vmess", "settings": {"clients": [{"id": "36cc15dd-dda5-4cf4-9dc8-7d01737f252e", "alterId": 0}], "fallbacks": [{"dest": 80, "xver": 1}]}, "streamSettings": {"network": "ws", "wsSettings": {"path": "/ws"}}, "sniffing": {"enabled": true, "destOverride": ["http", "tls", "quic"]}}], "outbounds": [{"protocol": "freedom", "settings": {}}, {"protocol": "blackhole", "tag": "blocked"}], "routing": {"rules": [{"type": "field", "ip": ["geoip:private"], "outboundTag": "blocked"}]}}