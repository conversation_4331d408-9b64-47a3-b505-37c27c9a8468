from datetime import datetime
from enum import Enum
from typing import Dict, ForwardRef
from uuid import UUID

from sqlalchemy import Column, DateTime, Enum as SQLEnum, ForeignKey, String, Boolean
from sqlalchemy.dialects.postgresql import JSONB, UUID as PGUUID
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixTime
from app.models.auth.model_auth_base import AuthProviderBase

# 避免循环导入
User = ForwardRef("User")


# 认证提供者类型
class AuthProviderType(str, Enum):
    """认证提供者类型"""
    LOCAL = "local"  # 本地用户名密码
    GOOGLE = "google"
    GITHUB = "github"
    FACEBOOK = "facebook"
    TWITTER = "twitter"
    APPLE = "apple"
    MICROSOFT = "microsoft"
    LDAP = "ldap"
    SAML = "saml"
    OAUTH2 = "oauth2"
    OPENID = "openid"


# 外部认证提供者模型
class ExternalAuthProvider(SQLModel):
    """外部认证提供者配置模型"""
    provider_type: AuthProviderType
    client_id: str
    client_secret: str
    redirect_uri: str
    scopes: str
    additional_params: Dict | None = None
    is_enabled: bool = True


# 认证提供者注册模型
class AuthProviderRegister(AuthProviderBase):
    """认证提供者注册模型
    
    用于注册新的认证提供者
    """
    user_id: UUID
    provider_type: AuthProviderType
    provider_data: Dict | None = None
    access_token: str | None = None
    refresh_token: str | None = None


# 认证提供者数据库模型
class UserAuthProvider(AuthProviderBase, MixTime, table=True):
    """用户认证提供者表
    
    存储用户的外部认证信息
    """
    __tablename__ = "user_auth_providers"
    __table_args__ = {"schema": "auth"}

    # 主键
    id: int | None = Field(default=None, primary_key=True, title="ID")

    # 关联字段
    user_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("auth.users.id"), nullable=False), title="用户ID")

    # 认证相关字段
    provider_type: AuthProviderType = Field(sa_column=Column(SQLEnum(AuthProviderType), nullable=False), title="提供者类型")
    provider_data: Dict | None = Field(sa_column=Column(JSONB), title="提供者数据")
    display_name: str | None = Field(sa_column=Column(String(100)), title="显示名称")
    access_token: str | None = Field(sa_column=Column(String), title="访问令牌")
    refresh_token: str | None = Field(sa_column=Column(String), title="刷新令牌")
    expires_at: datetime | None = Field(sa_column=Column(DateTime), title="过期时间")
    is_verified: bool = Field(sa_column=Column(Boolean, nullable=False, default=False), title="是否已验证")
    is_primary: bool = Field(sa_column=Column(Boolean, nullable=False, default=False), title="是否主要提供者")

    # 关系
    user: "User" = Relationship(back_populates="auth_providers")


# OAuth2授权请求模型
class OAuth2AuthRequest(SQLModel):
    """OAuth2授权请求模型"""
    provider: AuthProviderType
    state: str | None = None
    redirect_uri: str | None = None


# OAuth2回调响应模型
class OAuth2CallbackResponse(SQLModel):
    """OAuth2回调响应模型"""
    access_token: str
    refresh_token: str | None = None
    expires_in: int
    user_info: Dict
    provider: AuthProviderType
