-- 为kline.kline表设置更合理的压缩策略
ALTER TABLE kline.kline
    SET (
        timescaledb.compress,
        timescaledb.compress_segmentby = 'symbol_id',
        timescaledb.compress_orderby = 'timestamp DESC'
        );

-- 移除现有压缩策略（如果存在）并重新设置
SELECT remove_compression_policy('kline.kline', if_exists => true);
SELECT add_compression_policy('kline.kline', INTERVAL '3 days');

-- 优化压缩设置
SELECT set_chunk_time_interval('kline.kline', INTERVAL '1 day');

-- 设置保留策略，保留较老的数据
SELECT remove_retention_policy('kline.kline', if_exists => true);
SELECT add_retention_policy('kline.kline', INTERVAL '90 days');

-- 设置连续聚合视图的刷新策略为手动模式，减少后台工作者的内存占用
SELECT alter_job(job_id, scheduled => false)
FROM timescaledb_information.jobs
WHERE proc_name LIKE 'policy_refresh_continuous_aggregate%';

-- 设置自动清理参数
ALTER SYSTEM SET autovacuum_vacuum_scale_factor = 0.01;
ALTER SYSTEM SET autovacuum_analyze_scale_factor = 0.01;
ALTER SYSTEM SET autovacuum_vacuum_cost_limit = 1000;

-- 手动触发vacuum
VACUUM ANALYZE kline.kline;

-- 手动优化内存泄漏问题
ALTER DATABASE ${DB_NAME} SET timescaledb.telemetry_level to no_functions; 