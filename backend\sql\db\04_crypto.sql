------------------------------------------
-- 加密货币市场数据表
-- 此模块处理交易所、交易对和市场行情相关数据
------------------------------------------

-- 创建加密货币模式
CREATE SCHEMA IF NOT EXISTS crypto;

-- 市场和交易所枚举类型
CREATE TYPE crypto.exchange_enum AS ENUM ('binance', 'okx', 'huobi', 'gate', 'bybit', 'mexc', 'kucoin', 'bitget', 'coinbase', 'bitfinex');
CREATE TYPE crypto.market_enum AS ENUM ('spot', 'coin_future', 'usdt_future');

-- 市场交易对表
-- 存储所有支持的交易所交易对及其配置信息
CREATE TABLE crypto.symbol
(
    id                 SMALLINT GENERATED BY DEFAULT AS IDENTITY (START WITH 1000) UNIQUE, -- 交易对内部ID
    exchange           crypto.exchange_enum NOT NULL,                                      -- 交易所
    market_type        crypto.market_enum   NOT NULL,                                      -- 市场类型(现货/合约)
    symbol             VARCHAR              NOT NULL,                                      -- 交易对符号（如BTC/USDT）
    base_coin          VARCHAR              NOT NULL,                                      -- 基础币种（如BTC）
    quote_coin         VARCHAR              NOT NULL,                                      -- 计价币种（如USDT）
    margin_coin        VARCHAR              NOT NULL,                                      -- 保证金币种
    trade_status       VARCHAR              NOT NULL DEFAULT 'TRADING',                    -- 交易状态
    contract_type      VARCHAR              NOT NULL,                                      -- 合约类型
    contract_val       DOUBLE PRECISION     NOT NULL DEFAULT 1,                            -- 合约价值
    amount_precision   SMALLINT             NOT NULL DEFAULT 100,                          -- 数量精度
    price_precision    SMALLINT             NOT NULL DEFAULT 100,                          -- 价格精度
    min_amount         DOUBLE PRECISION     NOT NULL DEFAULT 0,                            -- 最小交易数量
    min_cost           DOUBLE PRECISION     NOT NULL DEFAULT 10,                           -- 最小交易成本
    list_time          TIMESTAMPTZ          NOT NULL DEFAULT '2021-12-31 16:00:00+00',     -- 上线时间
    delivery_time      TIMESTAMPTZ          NOT NULL DEFAULT '2099-12-31 16:00:00+00',     -- 交割时间
    sub_market         BOOLEAN                       DEFAULT TRUE,                         -- 是否为子市场
    tag                VARCHAR,                                                            -- 交易对标签
    kline_pending_sync BOOLEAN                       DEFAULT FALSE,                        -- k线是否同步
    is_deleted            BOOLEAN                       DEFAULT FALSE,                        -- 是否已删除
    create_time        TIMESTAMPTZ          NOT NULL DEFAULT CURRENT_TIMESTAMP,            -- 创建时间
    update_time        TIMESTAMPTZ          NOT NULL DEFAULT CURRENT_TIMESTAMP,            -- 更新时间
    PRIMARY KEY (exchange, market_type, symbol)
) WITH (fillfactor = 100);

-- 市场交易对索引
CREATE INDEX idx_symbol_id ON crypto.symbol (id) WHERE is_deleted = FALSE;
CREATE INDEX idx_symbol_exchange ON crypto.symbol (exchange, market_type) WHERE is_deleted = FALSE;
CREATE INDEX idx_symbol_base_coin ON crypto.symbol (base_coin) WHERE is_deleted = FALSE;
CREATE INDEX idx_symbol_quote_coin ON crypto.symbol (quote_coin) WHERE is_deleted = FALSE;
CREATE INDEX idx_symbol_status ON crypto.symbol (trade_status) WHERE is_deleted = FALSE;

-- 市场行情表
-- 存储每个交易对的最新价格和行情数据
CREATE TABLE crypto.ticker
(
    symbol_id   SMALLINT PRIMARY KEY NOT NULL,                           -- 关联的交易对ID
    open        DOUBLE PRECISION     NOT NULL,                           -- 开盘价
    high        DOUBLE PRECISION     NOT NULL,                           -- 最高价
    low         DOUBLE PRECISION     NOT NULL,                           -- 最低价
    close       DOUBLE PRECISION     NOT NULL,                           -- 最新成交价
    volume      DOUBLE PRECISION     NOT NULL,                           -- 成交量
    count       INTEGER                       DEFAULT 0,                 -- 成交笔数
    create_time TIMESTAMPTZ          NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time TIMESTAMPTZ          NOT NULL DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

DROP VIEW IF EXISTS crypto.v_symbol CASCADE;
CREATE VIEW crypto.v_symbol AS
SELECT s.*,
       t.close,
       ((t.close - t.open) / NULLIF(t.open, 0))::NUMERIC(20, 4) as change,
       t.volume,
       NOW() > s.delivery_time                                  AS deliveried,
       RANK() OVER (
           PARTITION BY s.market_type,s.base_coin
           ORDER BY CASE
                        WHEN s.quote_coin = 'USDT' THEN 1
                        WHEN s.quote_coin = 'BUSD' THEN 2
                        WHEN s.quote_coin = 'BNB' THEN 3
                        WHEN s.quote_coin = 'BTC' THEN 4
                        WHEN s.quote_coin = 'ETH' THEN 5
                        ELSE 9999 END )
                                                                AS quote_priority_rank,
       RANK() OVER (
           PARTITION BY s.market_type,s.base_coin
           ORDER BY t.volume DESC)                              AS volume_rank,
       (t.volume * t2.close / 10000)::INTEGER                   AS volume_usdt,
       s2.id                                                    AS usdt_symbol_id
FROM crypto.symbol s
         LEFT JOIN crypto.ticker t on s.id = t.symbol_id
         LEFT JOIN crypto.symbol s2 on CONCAT(s.base_coin, 'USDT') = s2.symbol AND s2.market_type = 'spot'
         LEFT JOIN crypto.ticker t2 on s2.id = t2.symbol_id
WHERE s.is_deleted = FALSE
  AND s.trade_status = 'TRADING'
  AND s.contract_type != 'DELIVERY'
  AND s.base_coin != 'USDT'
  AND s.symbol NOT IN ('USDCUSDT', 'FDUSDUSDT', 'FDUSDUSDC', 'WBTCBTC', 'TUSDUSDT', 'USDPUSDT', 'XUSDUSDT')
  AND (t.volume * t2.close / 10000) > 1000;


DROP VIEW IF EXISTS crypto.v_symbol_volume_rank CASCADE;
CREATE VIEW crypto.v_symbol_volume_rank AS
SELECT *
FROM crypto.v_symbol
WHERE quote_priority_rank = 1;

DROP VIEW IF EXISTS crypto.v_symbol_spot_volume_rank CASCADE;
CREATE VIEW crypto.v_symbol_spot_volume_rank AS
SELECT *
FROM crypto.v_symbol
WHERE market_type = 'spot'
  AND quote_priority_rank = 1;

DROP VIEW IF EXISTS crypto.v_symbol_usdt_future_volume_rank CASCADE;
CREATE VIEW crypto.v_symbol_usdt_future_volume_rank AS
SELECT *
FROM crypto.v_symbol
WHERE market_type = 'usdt_future'
  AND quote_priority_rank = 1;

DROP VIEW IF EXISTS crypto.v_symbol_coin_future_volume_rank CASCADE;
CREATE VIEW crypto.v_symbol_coin_future_volume_rank AS
SELECT *
FROM crypto.v_symbol
WHERE market_type = 'coin_future'
  AND quote_priority_rank = 1;


-- 自动更新时间戳触发器
CREATE TRIGGER update_symbol_timestamp
    BEFORE UPDATE
    ON crypto.symbol
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();

CREATE TRIGGER update_ticker_timestamp
    BEFORE UPDATE
    ON crypto.ticker
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();
