from celery import Celery

from app.core.core_config import settings

# 创建Celery实例
celery_app = Celery(
    "worker",
    broker=f"redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_celery_db}",
    backend=f"redis://{settings.redis_host}:{settings.redis_port}/{settings.redis_celery_result_db}",
)

# 配置Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=False,
    worker_concurrency=4,
    task_acks_late=True,
    task_time_limit=60 * 5,  # 5分钟
    broker_connection_retry_on_startup=True,
)

# 自动加载任务模块
celery_app.autodiscover_tasks(["app.services.tasks"])


def get_task_info(task_id):
    """获取任务信息"""
    return celery_app.AsyncResult(task_id)
