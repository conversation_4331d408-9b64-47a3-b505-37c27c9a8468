import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var themeSettings: ThemeSettings
    @EnvironmentObject var languageManager: LanguageManager
    @EnvironmentObject var userData: UserData
    
    @State private var showingLogoutAlert = false
    
    var body: some View {
        NavigationView {
            List {
                // 用户信息部分
                if userData.isLoggedIn {
                    userSection
                }
                
                // 外观设置
                appearanceSection
                
                // 语言设置
                languageSection
                
                // 关于部分
                aboutSection
                
                // 登出部分
                if userData.isLoggedIn {
                    logoutSection
                }
            }
            .navigationTitle(languageManager.localized("settings"))
            .navigationBarTitleDisplayMode(.large)
        }
        .alert("确认登出", isPresented: $showingLogoutAlert) {
            Button("取消", role: .cancel) { }
            But<PERSON>("登出", role: .destructive) {
                userData.logout()
            }
        } message: {
            Text("确定要登出当前账户吗？")
        }
    }
    
    private var userSection: some View {
        Section {
            HStack(spacing: 16) {
                // 用户头像
                Circle()
                    .fill(Color.blue.gradient)
                    .frame(width: 60, height: 60)
                    .overlay(
                        Group {
                            if let avatar = userData.currentUser?.avatar, !avatar.isEmpty {
                                AsyncImage(url: URL(string: avatar)) { image in
                                    image
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                } placeholder: {
                                    Image(systemName: "person.fill")
                                        .foregroundColor(.white)
                                        .font(.system(size: 24))
                                }
                                .clipShape(Circle())
                            } else {
                                Image(systemName: "person.fill")
                                    .foregroundColor(.white)
                                    .font(.system(size: 24))
                            }
                        }
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    if let user = userData.currentUser {
                        Text(user.username ?? user.email)
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Text(user.email)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            .padding(.vertical, 8)
        } header: {
            Text("用户信息")
        }
    }
    
    private var appearanceSection: some View {
        Section {
            HStack {
                Image(systemName: "paintbrush.fill")
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                Text(languageManager.localized("theme"))
                
                Spacer()
                
                Button(action: {
                    themeSettings.toggleTheme()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: themeSettings.isDarkMode ? "moon.fill" : "sun.max.fill")
                            .foregroundColor(themeSettings.isDarkMode ? .purple : .orange)
                        Text(themeSettings.isDarkMode ? "深色" : "浅色")
                            .foregroundColor(.primary)
                    }
                }
            }
        } header: {
            Text("外观")
        }
    }
    
    private var languageSection: some View {
        Section {
            HStack {
                Image(systemName: "globe")
                    .foregroundColor(.green)
                    .frame(width: 24)
                
                Text(languageManager.localized("language"))
                
                Spacer()
                
                Menu {
                    ForEach(Array(languageManager.availableLanguages.keys), id: \.self) { langCode in
                        Button(action: {
                            languageManager.setLanguage(langCode)
                        }) {
                            HStack {
                                Text(languageManager.availableLanguages[langCode] ?? langCode)
                                if languageManager.currentLanguage == langCode {
                                    Image(systemName: "checkmark")
                                }
                            }
                        }
                    }
                } label: {
                    HStack(spacing: 8) {
                        Text(languageManager.availableLanguages[languageManager.currentLanguage] ?? "中文")
                            .foregroundColor(.primary)
                        Image(systemName: "chevron.up.chevron.down")
                            .foregroundColor(.secondary)
                            .font(.caption)
                    }
                }
            }
        } header: {
            Text("语言")
        }
    }
    
    private var aboutSection: some View {
        Section {
            // 版本信息
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                    .frame(width: 24)
                
                Text("版本")
                
                Spacer()
                
                Text(getAppVersion())
                    .foregroundColor(.secondary)
            }
            
            // 隐私政策
            HStack {
                Image(systemName: "hand.raised.fill")
                    .foregroundColor(.orange)
                    .frame(width: 24)
                
                Text("隐私政策")
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            
            // 用户协议
            HStack {
                Image(systemName: "doc.text.fill")
                    .foregroundColor(.purple)
                    .frame(width: 24)
                
                Text("用户协议")
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
            
            // 反馈
            HStack {
                Image(systemName: "envelope.fill")
                    .foregroundColor(.green)
                    .frame(width: 24)
                
                Text("意见反馈")
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .foregroundColor(.secondary)
                    .font(.caption)
            }
        } header: {
            Text("关于")
        }
    }
    
    private var logoutSection: some View {
        Section {
            Button(action: {
                showingLogoutAlert = true
            }) {
                HStack {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .foregroundColor(.red)
                        .frame(width: 24)
                    
                    Text(languageManager.localized("logout"))
                        .foregroundColor(.red)
                }
            }
        }
    }
    
    private func getAppVersion() -> String {
        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String,
           let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String {
            return "\(version) (\(build))"
        }
        return "1.0.0"
    }
}

#Preview {
    SettingsView()
        .environmentObject(ThemeSettings())
        .environmentObject(LanguageManager())
        .environmentObject(UserData())
} 