# Your AI Agent

## 项目简介

Your AI Agent 是一个全功能的人工智能代理系统，集成了多种AI能力，特别是在加密货币预测和交易方面的应用。该系统包含完整的前后端实现，支持用户认证、基于角色的访问控制、多语言支持等功能。

## 系统特性

- 💻 **现代化Web界面**: 基于Vue 3的响应式前端，使用Naive UI和Tailwind CSS
- 🧠 **AI代理系统**: 包含多种可扩展的AI代理模块，如加密货币预测代理
- 🔒 **完整认证体系**: 用户注册、登录、权限管理
- 🌍 **多语言支持**: 基于i18n的国际化实现
- 📱 **多端支持**: Web前端和iOS移动应用
- 🔧 **扩展性强**: 模块化设计，易于添加新功能
- 🐳 **容器化部署**: 基于Docker的完整微服务架构，包含TimescaleDB数据库、后端服务和前端应用，支持快速部署和弹性扩展
- 🔒 **安全代理**: 采用Traefik作为反向代理，提供SSL终端、负载均衡和自动证书管理，确保系统安全性

## 项目结构

```
├── README.md
├── backend
│ ├── Dockerfile
│ ├── README.md
│ ├── __pycache__
│ ├── agents
│ ├── app
│ ├── docs
│ ├── examples
│ ├── main.py
│ ├── quant
│ ├── query.sql
│ ├── requirements.txt
│ └── sql
├── docker-compose.yml
├── frontend
│ ├── Dockerfile
│ ├── README-PROXY.md
│ ├── README.md
│ ├── babel.config.js
│ ├── dist
│ ├── index.html
│ ├── nginx.conf
│ ├── node_modules
│ ├── package.json
│ ├── postcss.config.js
│ ├── public
│ ├── src
│ ├── tailwind.config.js
│ ├── tsconfig.json
│ ├── vite.config.ts
│ └── yarn.lock
├── ios
└── upload.py

```

## 详细介绍

---

### 后端 (backend/)

- 使用 FastAPI 开发的 Python 后端服务
- 包含认证、用户管理、通知和RBAC（基于角色的访问控制）API
- 集成了 AI 代理和自定义工具，其中包含加密货币预测代理
- 使用 PostgreSQL/TimescaleDB 作为数据库，使用TimescaleDB进行时序数据存储，特别适合加密货币历史数据分析。相关优化配置已在`docker-compose.yml`中设置。
- 有完整的日志系统和中间件

#### 技术栈

- [FastAPI](https://fastapi.tiangolo.com/) - 现代高性能Web框架
- [SQLAlchemy](https://www.sqlalchemy.org/) - SQL工具包和ORM
- [Pydantic](https://pydantic-docs.helpmanual.io/) - 数据验证和设置管理
- [PostgreSQL](https://www.postgresql.org/) - 关系型数据库
- [Redis](https://redis.io/) - 缓存和消息队列
- [Celery](https://docs.celeryq.dev/) - 分布式任务队列

#### API文档

启动应用后，访问以下URL查看自动生成的API文档：

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

--- 

### 前端 (frontend/)

- 基于 Vue 3 开发的 Web 应用
- 使用 Pinia 进行状态管理，支持持久化存储
- 实现了国际化 (i18n) 支持
- 使用 Naive UI 组件库和 Tailwind CSS 设计样式
- 包含用户认证、代理界面等模块

#### 技术栈

- [Vue 3](https://v3.vuejs.org/) - 渐进式JavaScript框架
- [Vite](https://vitejs.dev/) - 现代前端构建工具
- [Pinia](https://pinia.vuejs.org/) - 状态管理
- [Vue Router](https://router.vuejs.org/) - 路由管理
- [Vue i18n](https://vue-i18n.intlify.dev/) - 国际化
- [Naive UI](https://www.naiveui.com/) - Vue 3组件库
- [Tailwind CSS](https://tailwindcss.com/) - 实用优先的CSS框架
- [TypeScript](https://www.typescriptlang.org/) - JavaScript的超集
- https://mdit-plugins.github.io/zh/

#### 目录结构

前端项目采用模块化结构：
- `src/components` - UI组件
- `src/views` - 页面视图
- `src/stores` - Pinia状态管理
- `src/apis` - API调用封装
- `src/router` - 路由配置
- `src/i18n` - 国际化配置
- `src/utils` - 工具函数
- `src/composables` - 组合式API

---

### iOS应用 (ios/)

- 使用 SwiftUI 开发的 iOS 应用
- 处于早期开发阶段
- 提供移动端访问AI代理系统的能力

---
### 环境要求

- Docker 和 Docker Compose
- Node.js 16+ (前端开发)
- Python 3.12+ (后端开发)
- Swift 6+ (iOS开发)

---

整体架构遵循现代的微服务设计模式，前后端分离，使用 Docker 进行容器化部署，提供了完整的开发、测试和生产环境配置。系统支持 AI 代理，特别是在加密货币预测和交易方面的应用。
