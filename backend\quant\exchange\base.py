"""
交易所API基础定义
包含接口类和常用枚举
"""
from enum import Enum, auto
from typing import Dict, Any, List, Callable

from app.models import Symbol, ApiKey


class OrderDirection(Enum):
    """开单方向"""
    OPEN_LONG = "OPEN_LONG"  # 开多
    OPEN_SHORT = "OPEN_SHORT"  # 开空
    CLOSE_LONG = "CLOSE_LONG"  # 平多
    CLOSE_SHORT = "CLOSE_SHORT"  # 平空


class OrderType(Enum):
    """成交方式"""
    LIMIT = "LIMIT"  # 普通委托
    MAKER = "MAKER"  # 只做maker
    FOK = "FOK"  # 全部成交或者立即取消
    IOC = "IOC"  # 立即成交并取消剩余
    MARKET = "MARKET"  # 市价委托


class OrderFilledState(Enum):
    """订单成交状态"""
    FAILED = "FAILED"  # 失败
    CANCELED = "CANCELED"  # 撤单成功
    WAIT = "WAIT"  # 等待成交
    PARTIAL = "PARTIAL"  # 部分成交
    COMPLETE = "COMPLETE"  # 完全成交
    CREATING = "CREATING"  # 下单中
    CANCELING = "CANCELING"  # 撤单中


class MarketType(Enum):
    """市场类型"""
    SPOT = auto()  # 现货
    USDT_FUTURE = auto()  # U本位合约
    COIN_FUTURE = auto()  # 币本位合约


class WsChannelType(Enum):
    """WebSocket频道类型"""
    TICKER = "ticker"  # 行情
    KLINE = "kline"  # K线
    DEPTH = "depth"  # 深度
    TRADE = "trade"  # 成交


class ExchangeApiInterface:
    """
    交易所API接口抽象类
    所有交易所实现必须继承此类
    """

    def __init__(self, api: ApiKey = None, symbol: Symbol = None):
        self.api = api
        self.symbol = symbol

    # 抽象方法
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """获取市场行情"""
        raise NotImplementedError

    async def get_klines(self, symbol: str, interval: str, limit: int = 500) -> List[Dict[str, Any]]:
        """获取K线数据"""
        raise NotImplementedError

    async def get_account(self) -> Dict[str, Any]:
        """获取账户信息"""
        raise NotImplementedError

    async def place_order(self, symbol: str, side: str, order_type: str,
                          quantity: float = None, price: float = None, **kwargs) -> Dict[str, Any]:
        """下单"""
        raise NotImplementedError

    async def cancel_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """取消订单"""
        raise NotImplementedError

    async def get_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """查询订单"""
        raise NotImplementedError

    # 工具方法
    @staticmethod
    def parse_timeframe(timeframe: str) -> int:
        """
        解析K线周期转换成秒
        例如: 1m, 1h, 1d, 1w, 1M, 1y
        """
        amount = int(timeframe[0:-1])
        unit = timeframe[-1]

        if unit == 'y':
            scale = 60 * 60 * 24 * 365
        elif unit == 'M':
            scale = 60 * 60 * 24 * 30
        elif unit == 'w':
            scale = 60 * 60 * 24 * 7
        elif unit == 'd':
            scale = 60 * 60 * 24
        elif unit == 'h':
            scale = 60 * 60
        else:  # 默认为分钟
            scale = 60

        return amount * scale

    @staticmethod
    def param_to_string(params):
        return '&'.join([f"{key}={params[key]}" for key in sorted(params.keys())])


class ExchangeWsInterface:
    """
    交易所WebSocket接口抽象类
    所有交易所WebSocket实现必须继承此类
    """

    async def connect(self) -> None:
        """建立WebSocket连接"""
        raise NotImplementedError

    async def disconnect(self) -> None:
        """断开WebSocket连接"""
        raise NotImplementedError

    async def subscribe(self, channel: str, symbols: List[str], callback: Callable) -> None:
        """
        订阅频道
        
        Args:
            channel: 频道类型
            symbols: 交易对列表
            callback: 回调函数
        """
        raise NotImplementedError

    async def unsubscribe(self, channel: str, symbols: List[str]) -> None:
        """
        取消订阅
        
        Args:
            channel: 频道类型
            symbols: 交易对列表
        """
        raise NotImplementedError
