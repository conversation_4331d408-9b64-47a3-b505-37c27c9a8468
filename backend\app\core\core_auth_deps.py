from typing import List, Set, Union

from fastapi import Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Bearer
from jose import JW<PERSON>rror
from pydantic import ValidationError
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.core_constants import TOKEN_URL
from app.core.core_exceptions import AuthenticationException, AuthorizationException
from app.core.core_jwt import verify_token
from app.db.db_session import get_async_db
from app.models.auth import User
from app.models.rbac import Role

# OAuth2认证方案
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=TOKEN_URL)


async def get_current_user(token: str = Depends(oauth2_scheme), db: AsyncSession = get_async_db) -> User:
    try:
        # 验证令牌
        token_data = verify_token(token)

        # 查询用户
        user = await User.get_by(session=db, id=token_data.sub)
        if not user:
            raise AuthenticationException("用户不存在")

        if not user.is_active:
            raise AuthenticationException("用户已被禁用")

        return user

    except (JWTError, ValidationError):
        raise AuthenticationException("无效的凭据")


async def get_current_active_user(current_user: User = Depends(get_current_user), ) -> User:
    if not current_user.is_active:
        raise AuthenticationException("用户未激活")
    return current_user


async def get_current_superuser(current_user: User = Depends(get_current_active_user), ) -> User:
    if not current_user.is_system:
        raise AuthorizationException("需要超级管理员权限")
    return current_user


async def get_user_permissions(user: User, db: AsyncSession) -> Set[str]:
    # 超级管理员拥有所有权限
    if user.is_superuser:
        return {"*"}

    # 查询用户角色
    roles = await db.execute(select(Role).join(Role.users).where(User.id == user.id))

    # 汇总权限
    permissions = set()
    for role in roles.scalars().all():
        for permission in role.permissions:
            permissions.add(permission.code)

    return permissions


async def check_permissions(permissions: Union[str, List[str]], user: User = Depends(get_current_active_user), db: AsyncSession = get_async_db, ) -> bool:
    # 转换为列表
    if isinstance(permissions, str):
        permissions = [permissions]

    # 获取用户权限
    user_permissions = await get_user_permissions(user, db)

    # 超级管理员
    if "*" in user_permissions:
        return True

    # 检查是否拥有所有指定权限
    for permission in permissions:
        if permission not in user_permissions:
            raise AuthorizationException(f"缺少权限: {permission}")

    return True
