from typing import ForwardRef, List
from uuid import UUID

from sqlalchemy import Column, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixTime
from app.models.rbac.model_rbac_base import UserRoleBase

# 避免循环导入
User = ForwardRef("User")
Role = ForwardRef("Role")


# 用户角色分配模型
class UserRoleAssign(SQLModel):
    """用户角色分配模型
    
    用于分配用户角色
    """
    user_id: UUID
    role_ids: List[UUID]


# 用户角色数据库模型
class UserRole(UserRoleBase, MixTime, table=True):
    """用户角色表
    
    用户与角色的关联
    """
    __tablename__ = "user_roles"
    __table_args__ = (
        UniqueConstraint("user_id", "role_id", name="uq_user_role"),
        {"schema": "rbac"}
    )

    # 主键
    id: int | None = Field(default=None, primary_key=True, title="ID")

    # 关联字段
    user_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("auth.users.id"), nullable=False), title="用户ID")
    role_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("rbac.roles.id"), nullable=False), title="角色ID")

    # 关系
    user: "User" = Relationship(back_populates="roles")
    role: "Role" = Relationship(back_populates="users")


# 用户角色响应模型
class UserRoleResponse(SQLModel):
    """用户角色响应模型"""
    id: int
    user_id: UUID
    role_id: UUID

    class Config:
        from_attributes = True
