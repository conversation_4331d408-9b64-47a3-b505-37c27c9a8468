from fastapi import HTTPException, status


class BaseAppException(HTTPException):
    """自定义应用基础异常类"""
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    detail = "服务器内部错误"

    def __init__(self, detail=None, headers=None):
        if detail:
            self.detail = detail
        super().__init__(status_code=self.status_code, detail=self.detail, headers=headers)


class NotFoundException(BaseAppException):
    """资源不存在异常"""
    status_code = status.HTTP_404_NOT_FOUND
    detail = "请求的资源不存在"


class AuthenticationException(BaseAppException):
    """认证异常"""
    status_code = status.HTTP_401_UNAUTHORIZED
    detail = "认证失败"

    def __init__(self, detail=None, headers=None):
        super().__init__(detail=detail, headers={"WWW-Authenticate": "Bearer", **(headers or {})})


class AuthorizationException(BaseAppException):
    """授权异常"""
    status_code = status.HTTP_403_FORBIDDEN
    detail = "无权限执行此操作"


class ValidationException(BaseAppException):
    """数据验证异常"""
    status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
    detail = "数据验证失败"


class DatabaseException(BaseAppException):
    """数据库操作异常"""
    status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
    detail = "数据库操作失败"


class BusinessException(BaseAppException):
    """业务逻辑异常"""
    status_code = status.HTTP_400_BAD_REQUEST
    detail = "业务处理失败"


class RateLimitException(BaseAppException):
    """请求频率限制异常"""
    status_code = status.HTTP_429_TOO_MANY_REQUESTS
    detail = "请求频率过高，请稍后重试"
