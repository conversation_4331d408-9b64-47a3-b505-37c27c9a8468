import hashlib
import os
import uuid
from datetime import timedelta
from typing import <PERSON>ple

import pendulum
from jose import jwt
from passlib.context import CryptContext

from app.core.core_config import settings

# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def get_password_hash(password: str) -> Tuple[str, str]:
    """生成密码哈希和盐"""
    salt = os.urandom(32).hex()
    hashed_password = pwd_context.hash(password + salt)
    return hashed_password, salt


def verify_password(plain_password: str, hashed_password: str, salt: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password + salt, hashed_password)


def create_access_token(subject: str, expires_delta: timedelta | None = None) -> str:
    """创建访问令牌"""
    if expires_delta:
        expire = pendulum.now("UTC").add(seconds=expires_delta.total_seconds())
    else:
        expire = pendulum.now("UTC").add(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode = {"exp": expire.int_timestamp, "sub": subject, "type": "access"}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(subject: str, expires_delta: timedelta | None = None) -> str:
    """创建刷新令牌"""
    if expires_delta:
        expire = pendulum.now("UTC").add(seconds=expires_delta.total_seconds())
    else:
        expire = pendulum.now("UTC").add(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)

    to_encode = {"exp": expire.int_timestamp, "sub": subject, "type": "refresh"}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_verification_token() -> str:
    """创建验证令牌"""
    return uuid.uuid4().hex


def hash_verification_token(token: str) -> str:
    """哈希验证令牌"""
    return hashlib.sha256(token.encode()).hexdigest()
