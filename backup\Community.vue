<template>
  <div class="min-h-screen bg-background-light dark:bg-dark-primary text-text-light dark:text-white">
    <!-- 页面头部 -->
    <div class="bg-white dark:bg-dark-secondary border-b border-gray-200 dark:border-gray-800">
      <div class="container py-16">
        <div class="text-center max-w-3xl mx-auto">
          <h1 class="text-4xl font-bold mb-4">用户社区</h1>
          <p class="text-lg text-gray-600 dark:text-gray-300 mb-8">与全球用户分享经验，交流AI助手使用心得</p>
          
          <!-- 发布按钮 -->
          <button
            @click="showPostModal = true"
            class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-accent transition-colors font-medium"
          >
            发布新帖
          </button>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="container py-8">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 侧边栏 -->
        <div class="lg:col-span-1">
          <div class="bg-white dark:bg-dark-secondary rounded-lg border border-gray-200 dark:border-gray-800 p-6 sticky top-8">
            <h3 class="text-lg font-semibold mb-4">社区导航</h3>
            
            <!-- 分类列表 -->
            <div class="space-y-2">
              <button
                v-for="category in communityCategories"
                :key="category.id"
                @click="selectedCategory = category.id"
                :class="[
                  'w-full text-left px-3 py-2 rounded-lg text-sm transition-colors',
                  selectedCategory === category.id
                    ? 'bg-primary text-white'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-300'
                ]"
              >
                <span class="mr-2">{{ category.icon }}</span>
                {{ category.name }}
                <span class="float-right text-xs opacity-60">({{ category.count }})</span>
              </button>
            </div>

            <!-- 热门标签 -->
            <div class="mt-8">
              <h4 class="text-sm font-medium mb-3 text-gray-600 dark:text-gray-400">热门标签</h4>
              <div class="flex flex-wrap gap-2">
                <span
                  v-for="tag in popularTags"
                  :key="tag"
                  class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 cursor-pointer hover:bg-primary hover:text-white transition-colors"
                  @click="searchByTag(tag)"
                >
                  #{{ tag }}
                </span>
              </div>
            </div>

            <!-- 社区统计 -->
            <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-800">
              <h4 class="text-sm font-medium mb-3 text-gray-600 dark:text-gray-400">社区数据</h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span>总帖子</span>
                  <span class="font-medium">1,234</span>
                </div>
                <div class="flex justify-between">
                  <span>活跃用户</span>
                  <span class="font-medium">567</span>
                </div>
                <div class="flex justify-between">
                  <span>今日新增</span>
                  <span class="font-medium text-green-500">+12</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 主内容区 -->
        <div class="lg:col-span-3">
          <!-- 排序和筛选 -->
          <div class="flex items-center justify-between mb-6">
            <div class="flex space-x-2">
              <button
                v-for="sort in sortOptions"
                :key="sort.id"
                @click="selectedSort = sort.id"
                :class="[
                  'px-4 py-2 rounded-lg text-sm font-medium transition-colors',
                  selectedSort === sort.id
                    ? 'bg-primary text-white'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                ]"
              >
                {{ sort.name }}
              </button>
            </div>
            
            <!-- 搜索框 -->
            <div class="relative">
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索帖子..."
                class="w-64 px-4 py-2 pl-10 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-primary text-text-light dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>

          <!-- 帖子列表 -->
          <div class="space-y-4">
            <div
              v-for="post in filteredPosts"
              :key="post.id"
              class="bg-white dark:bg-dark-secondary rounded-lg border border-gray-200 dark:border-gray-800 p-6 hover:shadow-md transition-shadow cursor-pointer"
              @click="viewPost(post)"
            >
              <!-- 帖子头部 -->
              <div class="flex items-start justify-between mb-4">
                <div class="flex items-center space-x-3">
                  <img
                    :src="post.author.avatar"
                    :alt="post.author.name"
                    class="w-10 h-10 rounded-full object-cover"
                  />
                  <div>
                    <h4 class="font-medium text-text-light dark:text-white">{{ post.author.name }}</h4>
                    <p class="text-sm text-gray-500">{{ formatTime(post.createdAt) }}</p>
                  </div>
                </div>
                
                <!-- 分类标签 -->
                <span
                  :class="[
                    'px-2 py-1 text-xs rounded-full',
                    getCategoryColor(post.category)
                  ]"
                >
                  {{ getCategoryName(post.category) }}
                </span>
              </div>

              <!-- 帖子标题和内容 -->
              <h3 class="text-lg font-semibold mb-2 text-text-light dark:text-white hover:text-primary transition-colors">
                {{ post.title }}
              </h3>
              <p class="text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">{{ post.content }}</p>

              <!-- 标签 -->
              <div class="flex flex-wrap gap-2 mb-4">
                <span
                  v-for="tag in post.tags"
                  :key="tag"
                  class="px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400"
                >
                  #{{ tag }}
                </span>
              </div>

              <!-- 帖子统计 -->
              <div class="flex items-center justify-between text-sm text-gray-500">
                <div class="flex space-x-4">
                  <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
                    </svg>
                    {{ post.likes }}
                  </span>
                  <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd" />
                    </svg>
                    {{ post.comments }}
                  </span>
                  <span class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                    </svg>
                    {{ post.views }}
                  </span>
                </div>
                
                <button class="text-primary hover:text-accent transition-colors">
                  查看详情 →
                </button>
              </div>
            </div>
          </div>

          <!-- 加载更多 -->
          <div v-if="hasMorePosts" class="text-center mt-8">
            <button
              @click="loadMorePosts"
              :disabled="loading"
              class="px-6 py-3 bg-white dark:bg-dark-secondary border border-gray-200 dark:border-gray-800 rounded-lg text-text-light dark:text-white hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors disabled:opacity-50"
            >
              <span v-if="loading">加载中...</span>
              <span v-else>加载更多</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 发布帖子模态框 -->
    <div
      v-if="showPostModal"
      class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      @click.self="showPostModal = false"
    >
      <div class="bg-white dark:bg-dark-secondary rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div class="p-6">
          <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-semibold">发布新帖</h3>
            <button
              @click="showPostModal = false"
              class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- 发布表单 -->
          <form @submit.prevent="submitPost" class="space-y-4">
            <div>
              <label class="block text-sm font-medium mb-2">分类</label>
              <select
                v-model="newPost.category"
                class="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-dark-primary text-text-light dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              >
                <option value="">请选择分类</option>
                <option v-for="category in communityCategories.slice(1)" :key="category.id" :value="category.id">
                  {{ category.name }}
                </option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium mb-2">标题</label>
              <input
                v-model="newPost.title"
                type="text"
                placeholder="请输入帖子标题"
                class="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-dark-primary text-text-light dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>

            <div>
              <label class="block text-sm font-medium mb-2">内容</label>
              <textarea
                v-model="newPost.content"
                rows="6"
                placeholder="分享你的想法和经验..."
                class="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-dark-primary text-text-light dark:text-white focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                required
              ></textarea>
            </div>

            <div>
              <label class="block text-sm font-medium mb-2">标签</label>
              <input
                v-model="newPost.tags"
                type="text"
                placeholder="用逗号分隔多个标签，如：AI,编程,学习"
                class="w-full px-3 py-2 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-dark-primary text-text-light dark:text-white focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>

            <div class="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                @click="showPostModal = false"
                class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                取消
              </button>
              <button
                type="submit"
                :disabled="!newPost.title || !newPost.content"
                class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-accent transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                发布
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 响应式数据
const searchQuery = ref('');
const selectedCategory = ref('all');
const selectedSort = ref('latest');
const loading = ref(false);
const hasMorePosts = ref(true);
const showPostModal = ref(false);

// 新帖子数据
const newPost = ref({
  title: '',
  content: '',
  category: '',
  tags: '',
});

// 分类数据
const communityCategories = ref([
  { id: 'all', name: '全部', icon: '🏠', count: 1234 },
  { id: 'discussion', name: '讨论交流', icon: '💬', count: 456 },
  { id: 'showcase', name: '作品展示', icon: '🎨', count: 234 },
  { id: 'tutorial', name: '教程分享', icon: '📚', count: 189 },
  { id: 'question', name: '问题求助', icon: '❓', count: 167 },
  { id: 'news', name: '资讯动态', icon: '📰', count: 98 },
  { id: 'feedback', name: '意见反馈', icon: '💡', count: 90 },
]);

// 排序选项
const sortOptions = ref([
  { id: 'latest', name: '最新' },
  { id: 'popular', name: '最热' },
  { id: 'mostLiked', name: '最多赞' },
  { id: 'mostCommented', name: '最多评论' },
]);

// 热门标签
const popularTags = ref([
  'AI助手', '编程', '学习', '工作流', '创意', '技巧', '分享', '讨论'
]);

// 帖子数据
const posts = ref([
  {
    id: 1,
    title: '如何训练一个更适合自己工作的AI助手？',
    content: '最近在使用各种AI助手，发现通用的助手虽然功能强大，但在处理特定领域的任务时还是不够精准。想请教大家是否有经验分享，如何训练或调优一个更适合自己工作场景的AI助手...',
    category: 'discussion',
    tags: ['AI助手', '训练', '工作流'],
    author: {
      name: '技术小白',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=1',
    },
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    likes: 24,
    comments: 8,
    views: 156,
  },
  {
    id: 2,
    title: '我用AI助手搭建的自动化工作流，效率提升300%！',
    content: '分享一下我最近搭建的工作流自动化系统，通过AI助手串联了数据收集、分析、报告生成等多个环节。现在每天的重复性工作基本都可以自动完成，真的是解放了双手...',
    category: 'showcase',
    tags: ['自动化', '工作流', '效率'],
    author: {
      name: '效率达人',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=2',
    },
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
    likes: 67,
    comments: 15,
    views: 342,
  },
  {
    id: 3,
    title: '新手入门：AI助手使用的10个实用技巧',
    content: '整理了一些AI助手使用过程中的实用技巧，适合刚开始接触AI助手的朋友。包括提示词优化、上下文管理、结果调优等方面的经验...',
    category: 'tutorial',
    tags: ['新手', '技巧', '教程'],
    author: {
      name: 'AI导师',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=3',
    },
    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
    likes: 89,
    comments: 23,
    views: 567,
  },
  {
    id: 4,
    title: '遇到AI助手回答不准确的问题，如何改进？',
    content: '最近在使用过程中发现AI助手有时候会给出不太准确的回答，特别是在处理专业问题时。想请教大家有什么好的方法来改进这种情况...',
    category: 'question',
    tags: ['问题', '改进', '准确性'],
    author: {
      name: '困惑用户',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=4',
    },
    createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
    likes: 12,
    comments: 6,
    views: 89,
  },
]);

// 过滤帖子
const filteredPosts = computed(() => {
  let filtered = posts.value;
  
  // 按分类过滤
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(p => p.category === selectedCategory.value);
  }
  
  // 按搜索关键词过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(p => 
      p.title.toLowerCase().includes(query) ||
      p.content.toLowerCase().includes(query) ||
      p.tags.some(tag => tag.toLowerCase().includes(query))
    );
  }
  
  // 排序
  switch (selectedSort.value) {
    case 'popular':
      filtered = filtered.sort((a, b) => (b.likes + b.comments) - (a.likes + a.comments));
      break;
    case 'mostLiked':
      filtered = filtered.sort((a, b) => b.likes - a.likes);
      break;
    case 'mostCommented':
      filtered = filtered.sort((a, b) => b.comments - a.comments);
      break;
    default: // latest
      filtered = filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }
  
  return filtered;
});

// 工具函数
const formatTime = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));
  
  if (hours < 1) return '刚刚';
  if (hours < 24) return `${hours}小时前`;
  return `${Math.floor(hours / 24)}天前`;
};

const getCategoryName = (categoryId: string) => {
  const category = communityCategories.value.find(c => c.id === categoryId);
  return category ? category.name : '未知';
};

const getCategoryColor = (categoryId: string) => {
  const colors: Record<string, string> = {
    discussion: 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400',
    showcase: 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400',
    tutorial: 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400',
    question: 'bg-orange-100 text-orange-600 dark:bg-orange-900/30 dark:text-orange-400',
    news: 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400',
    feedback: 'bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400',
  };
  return colors[categoryId] || colors.feedback;
};

// 事件处理
const viewPost = (post: any) => {
  // 这里可以跳转到帖子详情页
  console.log('查看帖子:', post);
};

const searchByTag = (tag: string) => {
  searchQuery.value = tag;
};

const loadMorePosts = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    hasMorePosts.value = false;
  }, 1000);
};

const submitPost = () => {
  if (!newPost.value.title || !newPost.value.content) return;
  
  // 这里应该调用API提交帖子
  console.log('提交新帖子:', newPost.value);
  
  // 重置表单
  newPost.value = {
    title: '',
    content: '',
    category: '',
    tags: '',
  };
  
  showPostModal.value = false;
};

onMounted(() => {
  // 页面加载完成后的初始化
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style> 