from typing import ForwardRef, List
from uuid import UUID

from sqlalchemy import Column, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixTime
from app.models.rbac.model_rbac_base import RolePermissionBase

# 避免循环导入
Role = ForwardRef("Role")
Permission = ForwardRef("Permission")


# 角色权限分配模型
class RolePermissionAssign(SQLModel):
    """角色权限分配模型
    
    用于分配角色权限
    """
    role_id: UUID
    permission_ids: List[UUID]


# 角色权限数据库模型
class RolePermission(RolePermissionBase, MixTime, table=True):
    """角色权限表
    
    角色与权限的关联
    """
    __tablename__ = "role_permissions"
    __table_args__ = (
        UniqueConstraint("role_id", "permission_id", name="uq_role_permission"),
        {"schema": "rbac"}
    )

    # 主键
    id: int | None = Field(default=None, primary_key=True, title="ID")

    # 关联字段
    role_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("rbac.roles.id"), nullable=False), title="角色ID")
    permission_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("rbac.permissions.id"), nullable=False), title="权限ID")

    # 关系
    role: "Role" = Relationship(back_populates="permissions")
    permission: "Permission" = Relationship(back_populates="roles")


# 角色权限响应模型
class RolePermissionResponse(SQLModel):
    """角色权限响应模型"""
    id: int
    role_id: UUID
    permission_id: UUID

    class Config:
        from_attributes = True
