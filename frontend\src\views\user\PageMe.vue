<template>
  <div class="min-h-screen bg-gray-50 dark:bg-dark transition-colors duration-300">
    <!-- 主要内容区 -->
    <main class="p-6 max-w-7xl mx-auto">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">个人中心</h1>
        <p class="text-gray-600 dark:text-gray-400 mt-2">管理您的个人信息和偏好设置</p>
      </div>

      <!-- 主要内容网格 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- 左侧列：用户档案和快捷操作 -->
        <div class="lg:col-span-1 space-y-6">
          <!-- 用户档案卡片 -->
          <n-card class="shadow-sm">
            <div class="text-center">
              <!-- 用户头像 -->
              <div class="relative inline-block mb-4">
                <n-avatar :size="80" :src="userStore.avatar_url || undefined" class="border-4 border-white dark:border-gray-700 shadow-lg">
                  {{ userStore.userInitials }}
                </n-avatar>
                <n-button circle size="small" type="primary" class="absolute -bottom-1 -right-1" @click="showEditProfile = true">
                  <template #icon>
                    <n-icon><edit-icon /></n-icon>
                  </template>
                </n-button>
              </div>

              <!-- 用户信息 -->
              <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-1">
                {{ userStore.fullName }}
              </h3>
              <p class="text-gray-500 dark:text-gray-400 text-sm mb-2">{{ userStore.email }}</p>
              <p class="text-gray-600 dark:text-gray-300 text-sm">
                {{ userStore.bio || '这个人很懒，什么都没有留下' }}
              </p>

              <!-- 账户状态标签 -->
              <div class="mt-4">
                <n-tag :type="userStore.is_active ? 'success' : 'warning'" size="small">
                  {{ userStore.is_active ? '活跃用户' : '待激活' }}
                </n-tag>
              </div>

              <!-- 注册信息 -->
              <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex justify-between text-sm text-gray-500 dark:text-gray-400">
                  <span>注册时间</span>
                  <span>{{ formatDate(userStore.create_time) }}</span>
                </div>
                <div class="flex justify-between text-sm text-gray-500 dark:text-gray-400 mt-1">
                  <span>今天是第</span>
                  <span>{{ registerDays }} 天</span>
                </div>
              </div>
            </div>
          </n-card>

          <!-- 快捷操作面板 -->
          <n-card title="快捷操作" class="shadow-sm">
            <div class="grid grid-cols-2 gap-3">
              <n-button @click="router.push('/playground')" class="h-16 flex flex-col items-center justify-center" ghost type="primary">
                <n-icon size="20" class="mb-1"><chat-icon /></n-icon>
                <span class="text-xs">开始聊天</span>
              </n-button>

              <n-button @click="router.push('/agent')" class="h-16 flex flex-col items-center justify-center" ghost type="info">
                <n-icon size="20" class="mb-1"><robot-icon /></n-icon>
                <span class="text-xs">创建助手</span>
              </n-button>

              <n-button @click="router.push('/documentation')" class="h-16 flex flex-col items-center justify-center" ghost type="success">
                <n-icon size="20" class="mb-1"><book-icon /></n-icon>
                <span class="text-xs">查看文档</span>
              </n-button>

              <n-button @click="showSettings = true" class="h-16 flex flex-col items-center justify-center" ghost type="warning">
                <n-icon size="20" class="mb-1"><settings-icon /></n-icon>
                <span class="text-xs">账户设置</span>
              </n-button>
            </div>
          </n-card>

          <!-- 设置面板 -->
          <n-card title="偏好设置" class="shadow-sm">
            <div class="space-y-4">
              <!-- 主题切换 -->
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <n-icon :color="isDark ? '#fbbf24' : '#6b7280'">
                    <component :is="isDark ? MoonIcon : SunIcon" />
                  </n-icon>
                  <span class="text-sm text-gray-700 dark:text-gray-300">深色模式</span>
                </div>
                <n-switch :value="isDark" @update:value="toggleTheme" />
              </div>

              <!-- 通知设置 -->
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <n-icon color="#6b7280"><notifications-icon /></n-icon>
                  <span class="text-sm text-gray-700 dark:text-gray-300">通知</span>
                </div>
                <n-switch :value="notificationsEnabled" @update:value="notificationsEnabled = $event" />
              </div>
            </div>
          </n-card>
        </div>

        <!-- 右侧列：数据统计和活动 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 数据概览 -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <n-card class="shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <n-icon size="24" color="#3b82f6">
                    <chat-bubble-icon />
                  </n-icon>
                </div>
                <div>
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">
                    {{ statsData.conversations }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">对话次数</div>
                  <div class="text-xs text-green-500">+12% 本周</div>
                </div>
              </div>
            </n-card>

            <n-card class="shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                  <n-icon size="24" color="#8b5cf6">
                    <robot-icon />
                  </n-icon>
                </div>
                <div>
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">
                    {{ statsData.agents }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">创建助手</div>
                  <div class="text-xs text-blue-500">{{ statsData.agents > 0 ? '最近活跃' : '待创建' }}</div>
                </div>
              </div>
            </n-card>

            <n-card class="shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <n-icon size="24" color="#10b981">
                    <time-icon />
                  </n-icon>
                </div>
                <div>
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ statsData.totalTime }}h</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">使用时长</div>
                  <div class="text-xs text-green-500">本月累计</div>
                </div>
              </div>
            </n-card>

            <n-card class="shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="p-3 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
                  <n-icon size="24" color="#f59e0b">
                    <star-icon />
                  </n-icon>
                </div>
                <div>
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">
                    {{ statsData.favorites }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">收藏内容</div>
                  <div class="text-xs text-orange-500">{{ statsData.favorites > 0 ? '已收藏' : '待收藏' }}</div>
                </div>
              </div>
            </n-card>

            <n-card class="shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="p-3 bg-red-100 dark:bg-red-900/30 rounded-lg">
                  <n-icon size="24" color="#ef4444">
                    <heart-icon />
                  </n-icon>
                </div>
                <div>
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">
                    {{ statsData.likes }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">获得赞数</div>
                  <div class="text-xs text-red-500">社区互动</div>
                </div>
              </div>
            </n-card>

            <n-card class="shadow-sm hover:shadow-md transition-shadow cursor-pointer">
              <div class="flex items-center gap-4">
                <div class="p-3 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
                  <n-icon size="24" color="#6366f1">
                    <trophy-icon />
                  </n-icon>
                </div>
                <div>
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">
                    {{ statsData.achievements }}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">成就徽章</div>
                  <div class="text-xs text-indigo-500">探索更多</div>
                </div>
              </div>
            </n-card>
          </div>

          <!-- 使用趋势图表 -->
          <n-card title="使用趋势" class="shadow-sm">
            <div class="h-48 flex items-center justify-center text-gray-500 dark:text-gray-400">
              <div class="text-center">
                <n-icon size="48" class="mb-2"><bar-chart-icon /></n-icon>
                <p>图表功能开发中...</p>
                <p class="text-sm">将展示您的使用趋势和活跃度</p>
              </div>
            </div>
          </n-card>

          <!-- 最近活动时间线 -->
          <n-card title="最近活动" class="shadow-sm">
            <div class="space-y-4">
              <div
                v-for="(activity, index) in enhancedActivities"
                :key="index"
                class="flex items-start gap-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
              >
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 rounded-full flex items-center justify-center" :style="{ backgroundColor: activity.bgColor }">
                    <n-icon :color="activity.iconColor" size="18">
                      <component :is="activity.icon" />
                    </n-icon>
                  </div>
                </div>
                <div class="flex-1 min-w-0">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ activity.title }}
                  </h4>
                  <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {{ activity.description }}
                  </p>
                  <div class="flex items-center gap-4 mt-2">
                    <span class="text-xs text-gray-500 dark:text-gray-500">
                      {{ activity.time }}
                    </span>
                    <n-tag v-if="activity.tag" :type="activity.tagType as 'default' | 'error' | 'warning' | 'success' | 'info' | 'primary'" size="tiny">
                      {{ activity.tag }}
                    </n-tag>
                  </div>
                </div>
              </div>
            </div>

            <!-- 查看更多活动 -->
            <div class="mt-6 text-center">
              <n-button text type="primary">
                查看更多活动
                <template #icon>
                  <n-icon><arrow-down-icon /></n-icon>
                </template>
              </n-button>
            </div>
          </n-card>
        </div>
      </div>

      <!-- 用户资料编辑模态框 -->
      <n-modal v-model:show="showEditProfile" preset="dialog" title="编辑个人资料">
        <div class="space-y-4">
          <!-- 头像上传 -->
          <div class="text-center">
            <n-avatar :size="80" :src="editForm.avatar_url" class="mb-2">
              {{ userStore.userInitials }}
            </n-avatar>
            <div>
              <n-button size="small" @click="handleAvatarUpload">更换头像</n-button>
            </div>
          </div>

          <!-- 表单字段 -->
          <n-form ref="editFormRef" :model="editForm" :rules="editFormRules">
            <n-form-item label="显示名称" path="display_name">
              <n-input v-model:value="editForm.display_name" placeholder="请输入显示名称" />
            </n-form-item>

            <n-form-item label="全名" path="full_name">
              <n-input v-model:value="editForm.full_name" placeholder="请输入全名" />
            </n-form-item>

            <n-form-item label="个人简介" path="bio">
              <n-input v-model:value="editForm.bio" type="textarea" placeholder="介绍一下自己吧..." :rows="3" />
            </n-form-item>

            <n-form-item label="手机号码" path="phone_number">
              <n-input v-model:value="editForm.phone_number" placeholder="请输入手机号码" />
            </n-form-item>
          </n-form>
        </div>

        <template #action>
          <n-space>
            <n-button @click="showEditProfile = false">取消</n-button>
            <n-button type="primary" @click="handleSaveProfile">保存</n-button>
          </n-space>
        </template>
      </n-modal>

      <!-- 设置模态框 -->
      <n-modal v-model:show="showSettings" style="width: 600px" preset="card" title="账户设置">
        <n-tabs type="line" animated>
          <n-tab-pane name="account" tab="账户信息">
            <div class="space-y-4">
              <n-form>
                <n-form-item label="用户名">
                  <n-input :value="userStore.username" readonly />
                </n-form-item>
                <n-form-item label="邮箱">
                  <n-input :value="userStore.email" readonly />
                </n-form-item>
                <n-form-item label="账户状态">
                  <n-tag :type="userStore.is_active ? 'success' : 'warning'">
                    {{ userStore.is_active ? '正常' : '待激活' }}
                  </n-tag>
                </n-form-item>
              </n-form>
            </div>
          </n-tab-pane>

          <n-tab-pane name="security" tab="安全设置">
            <div class="space-y-4">
              <n-button type="warning" block @click="handleChangePassword"> 修改密码 </n-button>
              <n-divider />
              <n-button type="error" block @click="handleLogout"> 退出登录 </n-button>
            </div>
          </n-tab-pane>

          <n-tab-pane name="preferences" tab="偏好设置">
            <div class="space-y-4">
              <n-form-item label="时区">
                <n-select :value="userStore.timezone || 'Asia/Shanghai'" :options="timezoneOptions" @update:value="handleTimezoneChange" />
              </n-form-item>
              <n-form-item label="语言">
                <n-select :value="currentLocale" :options="languageOptions" @update:value="handleLanguageChange" />
              </n-form-item>
            </div>
          </n-tab-pane>
        </n-tabs>
      </n-modal>
    </main>
  </div>
</template>

<script lang="ts" setup>
import { computed, reactive, ref, watchEffect } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import { NAvatar, NButton, NCard, NIcon, NList, NListItem, NThing, NTag, NSwitch, NModal, NForm, NFormItem, NInput, NSelect, NSpace, NTabs, NTabPane, NDivider } from 'naive-ui';
import {
  AnalyticsOutline,
  CalendarOutline,
  DocumentTextOutline,
  MoonOutline,
  PersonOutline,
  SunnyOutline,
  ChatbubbleEllipsesOutline,
  ConstructOutline,
  BookOutline,
  SettingsOutline,
  NotificationsOutline,
  GlobeOutline,
  CreateOutline,
  TimeOutline,
  StarOutline,
  HeartOutline,
  TrophyOutline,
  BarChartOutline,
  ChevronDownOutline,
  RocketOutline,
  CheckmarkCircleOutline,
  WarningOutline,
} from '@vicons/ionicons5';
import { useUserStore } from '@/stores/user';
import { useThemeStore } from '@/stores/theme';

// 组件定义
const SunIcon = SunnyOutline;
const MoonIcon = MoonOutline;
const CalendarIcon = CalendarOutline;
const ChartLineIcon = AnalyticsOutline;
const DocumentIcon = DocumentTextOutline;
const EditIcon = CreateOutline;
const ChatIcon = ChatbubbleEllipsesOutline;
const RobotIcon = ConstructOutline;
const BookIcon = BookOutline;
const SettingsIcon = SettingsOutline;
const NotificationsIcon = NotificationsOutline;
const GlobeIcon = GlobeOutline;
const ChatBubbleIcon = ChatbubbleEllipsesOutline;
const TimeIcon = TimeOutline;
const StarIcon = StarOutline;
const HeartIcon = HeartOutline;
const TrophyIcon = TrophyOutline;
const BarChartIcon = BarChartOutline;
const ArrowDownIcon = ChevronDownOutline;

const router = useRouter();
const userStore = useUserStore();
const themeStore = useThemeStore();
const message = useMessage();

// 响应式数据
const showEditProfile = ref(false);
const showSettings = ref(false);
const notificationsEnabled = ref(true);
const currentLocale = ref('zh');

// 默认头像URL（如果用户没有头像）
const defaultAvatar = '/default-avatar.png';

// 编辑表单
const editForm = reactive({
  display_name: '',
  full_name: '',
  bio: '',
  phone_number: '',
  avatar_url: '',
});

const editFormRef = ref();

// 表单验证规则
const editFormRules = {
  display_name: [{ max: 50, message: '显示名称长度不能超过50个字符', trigger: 'blur' }],
  full_name: [{ max: 100, message: '全名长度不能超过100个字符', trigger: 'blur' }],
  bio: [{ max: 500, message: '个人简介长度不能超过500个字符', trigger: 'blur' }],
  phone_number: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
};

// 计算属性：深色模式状态
const isDark = computed(() => themeStore.isDarkMode);

// 格式化日期函数
const formatDate = (dateString: string | null) => {
  if (!dateString) return '未知';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

// 注册天数计算
const registerDays = computed(() => {
  if (!userStore.create_time) return 1;
  const createDate = new Date(userStore.create_time);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - createDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// 模拟统计数据
const statsData = reactive({
  conversations: 127,
  agents: 3,
  totalTime: 45,
  favorites: 12,
  likes: 89,
  achievements: 5,
});

// 语言选项
const languageOptions = [
  { label: '中文', value: 'zh' },
  { label: 'English', value: 'en' },
];

// 时区选项
const timezoneOptions = [
  { label: 'Asia/Shanghai (GMT+8)', value: 'Asia/Shanghai' },
  { label: 'UTC (GMT+0)', value: 'UTC' },
  { label: 'America/New_York (GMT-5)', value: 'America/New_York' },
];

// 增强的活动数据
const enhancedActivities = [
  {
    title: '创建新的AI助手',
    description: '您成功创建了一个专业的编程助手，已开始为您提供代码建议',
    time: '2小时前',
    icon: RocketOutline,
    iconColor: '#ffffff',
    bgColor: '#3b82f6',
    tag: '新创建',
    tagType: 'info',
  },
  {
    title: '完成每日对话目标',
    description: '恭喜！您已达成今日对话目标，与AI助手进行了10次有效交流',
    time: '今天 16:30',
    icon: CheckmarkCircleOutline,
    iconColor: '#ffffff',
    bgColor: '#10b981',
    tag: '成就解锁',
    tagType: 'success',
  },
  {
    title: '更新个人资料',
    description: '您更新了个人头像和简介信息，让助手更好地了解您',
    time: '今天 09:30',
    icon: PersonOutline,
    iconColor: '#ffffff',
    bgColor: '#8b5cf6',
    tag: '资料完善',
    tagType: 'default',
  },
  {
    title: '系统功能更新',
    description: '平台新增了语音识别功能，现在可以通过语音与AI助手交流',
    time: '昨天 20:15',
    icon: WarningOutline,
    iconColor: '#ffffff',
    bgColor: '#f59e0b',
    tag: '功能更新',
    tagType: 'warning',
  },
  {
    title: '获得社区点赞',
    description: '您分享的AI使用技巧获得了15个点赞，影响力正在提升',
    time: '2天前',
    icon: HeartOutline,
    iconColor: '#ffffff',
    bgColor: '#ef4444',
    tag: '社区互动',
    tagType: 'error',
  },
];

// 初始化编辑表单
const initEditForm = () => {
  editForm.display_name = userStore.display_name || '';
  editForm.full_name = userStore.full_name || '';
  editForm.bio = userStore.bio || '';
  editForm.phone_number = userStore.phone_number || '';
  editForm.avatar_url = userStore.avatar_url || '';
};

// 切换主题
const toggleTheme = () => {
  themeStore.toggleTheme();
};

// 处理头像上传
const handleAvatarUpload = () => {
  message.info('头像上传功能开发中...');
};

// 保存个人资料
const handleSaveProfile = async () => {
  try {
    await editFormRef.value?.validate();
    // 这里应该调用API保存用户资料
    message.success('个人资料保存成功！');
    showEditProfile.value = false;
  } catch (error) {
    message.error('请检查表单输入');
  }
};

// 修改密码
const handleChangePassword = () => {
  message.info('密码修改功能开发中...');
};

// 处理语言变更
const handleLanguageChange = (value: string) => {
  currentLocale.value = value;
  message.info(`语言已切换为${value === 'zh' ? '中文' : 'English'}`);
};

// 处理时区变更
const handleTimezoneChange = (value: string) => {
  message.info(`时区已更新为${value}`);
};

// 退出登录
const handleLogout = () => {
  userStore.$reset();
  router.push('/login');
};

// 监听编辑对话框的打开状态
watchEffect(() => {
  if (showEditProfile.value) {
    initEditForm();
  }
});

// 初始化时设置表单数据
initEditForm();
</script>

<style scoped>
/* 可以添加自定义样式，但大部分使用Tailwind类 */
</style>
