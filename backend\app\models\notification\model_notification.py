from datetime import datetime
from typing import Dict, List, ForwardRef, TYPE_CHECKING, Optional
from uuid import UUID

from sqlalchemy import Column, DateTime, Enum as SQLEnum, ForeignKey, Text
from sqlalchemy.dialects.postgresql import JSONB, UUID as PGUUID
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixUUID, MixTime
from app.models.notification.model_notification_base import (
    NotificationBase, NotificationStatus, NotificationType, NotificationPriority
)

# 避免循环导入
User = ForwardRef("User")
NotificationTemplate = ForwardRef("NotificationTemplate")

if TYPE_CHECKING:
    pass
else:
    NotificationChannelConfig = ForwardRef("NotificationChannelConfig")


# 通知创建模型
class NotificationCreate(NotificationBase):
    """通知创建模型
    
    用于创建新通知
    """
    user_id: UUID
    template_id: UUID | None = None
    data: Dict | None = None
    channels: List[UUID] | None = None


# 通知批量创建模型
class NotificationBatchCreate(SQLModel):
    """通知批量创建模型
    
    用于批量创建通知
    """
    user_ids: List[UUID]
    subject: str
    content: str
    notification_type: NotificationType
    priority: NotificationPriority = NotificationPriority.NORMAL
    template_id: UUID | None = None
    data: Dict | None = None
    channels: List[UUID] | None = None


# 通知更新模型
class NotificationUpdate(SQLModel):
    """通知更新模型
    
    用于更新通知
    """
    status: NotificationStatus | None = None
    read_at: datetime | None = None
    is_read: bool | None = None


# 通知数据库模型
class Notification(NotificationBase, MixUUID, MixTime, table=True):
    """通知表
    
    存储系统中的所有通知
    """
    __tablename__ = "notifications"
    __table_args__ = {"schema": "notification"}

    # 关联字段
    user_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("auth.users.id"), nullable=False), title="用户ID")
    template_id: UUID | None = Field(sa_column=Column(PGUUID, ForeignKey("notification.notification_templates.id")), title="模板ID")

    # 通知状态字段
    status: NotificationStatus = Field(sa_column=Column(SQLEnum(NotificationStatus), nullable=False, default=NotificationStatus.PENDING), title="状态")
    data: Dict | None = Field(sa_column=Column(JSONB), title="数据")
    read_at: datetime | None = Field(sa_column=Column(DateTime), title="阅读时间")
    is_read: bool = Field(default=False, title="是否已读")
    sent_at: datetime | None = Field(sa_column=Column(DateTime), title="发送时间")
    error_message: str | None = Field(sa_column=Column(Text), title="错误信息")
    retry_count: int = Field(default=0, title="重试次数")

    # 关系
    user: "User" = Relationship(back_populates="notifications")
    template: Optional["NotificationTemplate"] = Relationship(back_populates="notifications")
    # 暂时注释掉channels关系，解决循环导入问题
    # channels: List["NotificationChannelConfig"] = Relationship(back_populates="notifications", link_model=NotificationChannel)


# 通知响应模型
class NotificationResponse(NotificationBase):
    """通知响应模型
    
    用于API响应返回通知信息
    """
    id: UUID
    user_id: UUID
    template_id: UUID | None = None
    status: NotificationStatus
    data: Dict | None = None
    read_at: datetime | None = None
    is_read: bool
    sent_at: datetime | None = None
    create_time: datetime

    class Config:
        from_attributes = True


# 通知列表响应模型
class NotificationListResponse(SQLModel):
    """通知列表响应模型"""
    total_count: int
    data: List[NotificationResponse]
