import { createI18n } from 'vue-i18n';
// 使用类型声明导入本地化文件
import zh from '../locales/zh/index.js';
import en from '../locales/en/index.js';
import { useSettingsStore } from '@/stores/settings';

// 定义支持的语言类型
export type SupportedLocales = 'en' | 'zh';

// 检测浏览器默认语言
// eslint-disable-next-line no-unused-vars
const getBrowserLanguage = (): SupportedLocales => {
  const browserLang = navigator.language;
  return browserLang.startsWith('zh') ? 'zh' : 'en';
};

// 从localStorage获取用户设置的语言，如果没有则使用英文作为默认语言
const getUserLanguage = (): SupportedLocales => {
  // 优先使用store中的设置
  try {
    const settingsStore = useSettingsStore();
    if (settingsStore.language && (settingsStore.language === 'en' || settingsStore.language === 'zh')) {
      return settingsStore.language as SupportedLocales;
    }
  } catch (e) {
    // 如果store未初始化，fallback到localStorage
    const savedLang = localStorage.getItem('language') as SupportedLocales;
    return savedLang && (savedLang === 'en' || savedLang === 'zh') ? savedLang : 'en';
  }

  // 默认使用英文
  return 'en';
};

const messages = {
  zh,
  en,
};

export const i18n = createI18n({
  legacy: false,
  locale: getUserLanguage(),
  fallbackLocale: 'en',
  messages,
});

export default i18n;

// 便捷切换语言的函数
export const setLanguage = (lang: SupportedLocales): void => {
  i18n.global.locale.value = lang;

  // 更新store中的语言设置
  try {
    const settingsStore = useSettingsStore();
    settingsStore.updateSetting('language', lang);
  } catch (e) {
    // fallback到localStorage
    localStorage.setItem('language', lang);
  }

  document.querySelector('html')?.setAttribute('lang', lang);
};
