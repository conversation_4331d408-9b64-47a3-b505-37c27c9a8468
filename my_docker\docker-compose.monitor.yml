services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    environment:
      - TZ=Asia/Shanghai
    ports:
      - "9090:9090"
    entrypoint: >
      /bin/sh -c 'echo -e "global:\n  scrape_interval: 15s\n\nscrape_configs:\n  - job_name: '\''prometheus'\''\n    static_configs:\n      - targets: [ '\''prometheus:9090'\'']\n  - job_name: '\''node'\''\n    static_configs:\n      - targets: [ '\''node-exporter:9100'\'']" > /etc/prometheus/prometheus.yml && exec /bin/prometheus --config.file=/etc/prometheus/prometheus.yml --storage.tsdb.path=/prometheus'
    volumes:
      - prometheus_data:/prometheus

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    environment:
      - TZ=Asia/Shanghai
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro  # 只读挂载 /proc
      - /sys:/host/sys:ro    # 只读挂载 /sys

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    depends_on:
      - prometheus
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
      - TZ=Asia/Shanghai
    ports:
      - '3000:3000'
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  prometheus_data:
  grafana_data:  # 定义 Grafana 数据卷