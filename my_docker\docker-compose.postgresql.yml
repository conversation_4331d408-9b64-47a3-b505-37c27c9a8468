services:
  postgresql:
    image: timescale/timescaledb-ha:pg16
    container_name: postgresql
    restart: unless-stopped
    environment:
      POSTGRES_USER_FILE: /run/secrets/USERNAME
      POSTGRES_PASSWORD_FILE: /run/secrets/PASSWORD
      TIMESCALEDB_TELEMETRY: "off"
      TZ: Asia/Shanghai
    secrets:
      - USERNAME
      - PASSWORD
    volumes:
      - postgresql_data:/home/<USER>/pgdata
    ports:
      - "5432:5432" # 生产环境中视需求移除
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: "4g"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  postgresql_data:

secrets:
  USERNAME:
    external: true
  PASSWORD:
    external: true