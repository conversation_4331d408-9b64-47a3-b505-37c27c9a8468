# iOS技术实现指南

## 项目架构

### 整体架构模式
采用 **MVVM + Coordinator** 架构模式，确保代码的可维护性和可扩展性。

```
App Architecture
├── Presentation Layer (UI)
│   ├── Views (SwiftUI Views)
│   ├── ViewModels (ObservableObject)
│   └── Coordinators (Navigation)
├── Domain Layer (Business Logic)
│   ├── Use Cases
│   ├── Entities
│   └── Repository Protocols
├── Data Layer (Data Access)
│   ├── Repositories (Implementation)
│   ├── Data Sources (API, Local)
│   └── Models (DTOs)
└── Infrastructure Layer
    ├── Network
    ├── Storage
    └── Utilities
```

### 技术栈选择

#### 核心框架
```swift
// UI框架
import SwiftUI
import Combine

// 网络请求
import Foundation (URLSession)

// 数据存储
import CoreData
import SwiftData // iOS 17+

// 依赖注入
// 使用自定义DI容器或第三方库如Swinject
```

#### 第三方依赖 (最小化原则)
```swift
// 仅在必要时使用
dependencies: [
    .package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.8.0"), // 可选
    .package(url: "https://github.com/onevcat/Kingfisher.git", from: "7.0.0"), // 图片加载
]
```

## 项目结构

### 文件组织
```
YourAIAgent/
├── App/
│   ├── YourAIAgentApp.swift
│   ├── AppDelegate.swift
│   └── SceneDelegate.swift
├── Core/
│   ├── DependencyInjection/
│   ├── Extensions/
│   ├── Utilities/
│   └── Constants/
├── Features/
│   ├── Authentication/
│   │   ├── Views/
│   │   ├── ViewModels/
│   │   ├── Models/
│   │   └── Services/
│   ├── Chat/
│   │   ├── Views/
│   │   ├── ViewModels/
│   │   ├── Models/
│   │   └── Services/
│   ├── Settings/
│   └── Profile/
├── Shared/
│   ├── UI/
│   │   ├── Components/
│   │   ├── Styles/
│   │   └── Modifiers/
│   ├── Models/
│   ├── Services/
│   └── Repositories/
└── Resources/
    ├── Assets.xcassets
    ├── Localizable.strings
    └── Info.plist
```

## 核心实现

### 1. 应用入口点

```swift
// YourAIAgentApp.swift
import SwiftUI

@main
struct YourAIAgentApp: App {
    @StateObject private var appCoordinator = AppCoordinator()
    
    var body: some Scene {
        WindowGroup {
            AppCoordinatorView(coordinator: appCoordinator)
                .preferredColorScheme(appCoordinator.colorScheme)
        }
    }
}

// AppCoordinator.swift
class AppCoordinator: ObservableObject {
    @Published var currentFlow: AppFlow = .launch
    @Published var colorScheme: ColorScheme?
    
    enum AppFlow {
        case launch
        case authentication
        case main
    }
    
    func start() {
        // 检查用户登录状态
        if AuthenticationService.shared.isLoggedIn {
            currentFlow = .main
        } else {
            currentFlow = .authentication
        }
    }
}
```

### 2. 网络层实现

```swift
// APIService.swift
import Foundation
import Combine

protocol APIServiceProtocol {
    func request<T: Codable>(_ endpoint: APIEndpoint) -> AnyPublisher<T, APIError>
}

class APIService: APIServiceProtocol {
    private let session = URLSession.shared
    private let baseURL = "http://localhost:8000" // 配置为您的后端地址
    
    func request<T: Codable>(_ endpoint: APIEndpoint) -> AnyPublisher<T, APIError> {
        guard let url = URL(string: baseURL + endpoint.path) else {
            return Fail(error: APIError.invalidURL)
                .eraseToAnyPublisher()
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = endpoint.method.rawValue
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        if let body = endpoint.body {
            request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        }
        
        return session.dataTaskPublisher(for: request)
            .map(\.data)
            .decode(type: T.self, decoder: JSONDecoder())
            .mapError { error in
                if error is DecodingError {
                    return APIError.decodingError
                } else {
                    return APIError.networkError
                }
            }
            .eraseToAnyPublisher()
    }
}

// APIEndpoint.swift
enum APIEndpoint {
    case getAgents
    case sendMessage(agentId: String, message: ChatMessage)
    case healthCheck
    
    var path: String {
        switch self {
        case .getAgents:
            return "/v1/agents"
        case .sendMessage(let agentId, _):
            return "/v1/agents/\(agentId)/runs"
        case .healthCheck:
            return "/v1/health"
        }
    }
    
    var method: HTTPMethod {
        switch self {
        case .getAgents, .healthCheck:
            return .GET
        case .sendMessage:
            return .POST
        }
    }
    
    var body: [String: Any]? {
        switch self {
        case .sendMessage(_, let message):
            return [
                "message": message.content,
                "stream": true,
                "model": "gpt-4.1",
                "user_id": message.userId,
                "session_id": message.sessionId
            ]
        default:
            return nil
        }
    }
}

enum HTTPMethod: String {
    case GET = "GET"
    case POST = "POST"
    case PUT = "PUT"
    case DELETE = "DELETE"
}

enum APIError: Error, LocalizedError {
    case invalidURL
    case networkError
    case decodingError
    case serverError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .networkError:
            return "Network connection error"
        case .decodingError:
            return "Data parsing error"
        case .serverError(let message):
            return message
        }
    }
}
```

### 3. 数据模型

```swift
// ChatModels.swift
import Foundation

struct ChatMessage: Codable, Identifiable {
    let id = UUID()
    let content: String
    let sender: MessageSender
    let timestamp: Date
    let status: MessageStatus
    let userId: String
    let sessionId: String
    
    enum MessageSender: Codable {
        case user
        case ai(agentType: String)
        case system
    }
    
    enum MessageStatus: Codable {
        case sending
        case sent
        case delivered
        case failed
    }
}

struct Agent: Codable, Identifiable {
    let id: String
    let name: String
    let description: String
    let icon: String
    let isAvailable: Bool
}

struct ChatSession: Codable, Identifiable {
    let id = UUID()
    let sessionId: String
    let agentId: String
    let messages: [ChatMessage]
    let createdAt: Date
    let updatedAt: Date
}

// API Response Models
struct AgentsResponse: Codable {
    let agents: [String]
}

struct MessageResponse: Codable {
    let response: String
    let agentId: String
    let timestamp: Date
}
```

### 4. 聊天界面实现

```swift
// ChatView.swift
import SwiftUI

struct ChatView: View {
    @StateObject private var viewModel = ChatViewModel()
    @State private var messageText = ""
    @FocusState private var isInputFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            // 导航栏
            ChatNavigationBar(
                agentName: viewModel.selectedAgent?.name ?? "AI Assistant",
                onMenuTap: { viewModel.showAgentSelection = true },
                onSettingsTap: { viewModel.showSettings = true }
            )
            
            // 消息列表
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(viewModel.messages) { message in
                            MessageBubbleView(message: message)
                                .id(message.id)
                        }
                        
                        if viewModel.isLoading {
                            TypingIndicatorView()
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                }
                .onChange(of: viewModel.messages.count) { _ in
                    if let lastMessage = viewModel.messages.last {
                        withAnimation(.easeOut(duration: 0.3)) {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }
                    }
                }
            }
            
            // 输入区域
            ChatInputView(
                text: $messageText,
                isLoading: viewModel.isLoading,
                onSend: {
                    viewModel.sendMessage(messageText)
                    messageText = ""
                }
            )
            .focused($isInputFocused)
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $viewModel.showAgentSelection) {
            AgentSelectionView(
                selectedAgent: $viewModel.selectedAgent,
                agents: viewModel.availableAgents
            )
        }
        .sheet(isPresented: $viewModel.showSettings) {
            SettingsView()
        }
        .onAppear {
            viewModel.loadAgents()
        }
    }
}

// ChatViewModel.swift
class ChatViewModel: ObservableObject {
    @Published var messages: [ChatMessage] = []
    @Published var selectedAgent: Agent?
    @Published var availableAgents: [Agent] = []
    @Published var isLoading = false
    @Published var showAgentSelection = false
    @Published var showSettings = false
    
    private let apiService: APIServiceProtocol
    private let sessionId = UUID().uuidString
    private var cancellables = Set<AnyCancellable>()
    
    init(apiService: APIServiceProtocol = APIService()) {
        self.apiService = apiService
    }
    
    func loadAgents() {
        apiService.request(.getAgents)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    if case .failure(let error) = completion {
                        print("Failed to load agents: \(error)")
                    }
                },
                receiveValue: { [weak self] (response: AgentsResponse) in
                    self?.availableAgents = response.agents.map { agentId in
                        Agent(
                            id: agentId,
                            name: self?.getAgentDisplayName(agentId) ?? agentId,
                            description: self?.getAgentDescription(agentId) ?? "",
                            icon: self?.getAgentIcon(agentId) ?? "🤖",
                            isAvailable: true
                        )
                    }
                    self?.selectedAgent = self?.availableAgents.first
                }
            )
            .store(in: &cancellables)
    }
    
    func sendMessage(_ content: String) {
        guard !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty,
              let agent = selectedAgent else { return }
        
        let userMessage = ChatMessage(
            content: content,
            sender: .user,
            timestamp: Date(),
            status: .sending,
            userId: "current_user", // 从认证服务获取
            sessionId: sessionId
        )
        
        messages.append(userMessage)
        isLoading = true
        
        apiService.request(.sendMessage(agentId: agent.id, message: userMessage))
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.handleMessageError(error)
                    }
                },
                receiveValue: { [weak self] (response: MessageResponse) in
                    let aiMessage = ChatMessage(
                        content: response.response,
                        sender: .ai(agentType: agent.id),
                        timestamp: response.timestamp,
                        status: .delivered,
                        userId: "ai",
                        sessionId: self?.sessionId ?? ""
                    )
                    self?.messages.append(aiMessage)
                }
            )
            .store(in: &cancellables)
    }
    
    private func handleMessageError(_ error: APIError) {
        // 更新消息状态为失败
        if let lastIndex = messages.lastIndex(where: { $0.sender == .user }) {
            messages[lastIndex] = ChatMessage(
                content: messages[lastIndex].content,
                sender: messages[lastIndex].sender,
                timestamp: messages[lastIndex].timestamp,
                status: .failed,
                userId: messages[lastIndex].userId,
                sessionId: messages[lastIndex].sessionId
            )
        }
        
        // 显示错误消息
        let errorMessage = ChatMessage(
            content: "抱歉，发送消息时出现错误：\(error.localizedDescription)",
            sender: .system,
            timestamp: Date(),
            status: .delivered,
            userId: "system",
            sessionId: sessionId
        )
        messages.append(errorMessage)
    }
    
    private func getAgentDisplayName(_ agentId: String) -> String {
        switch agentId {
        case "web_agent": return "网络搜索助手"
        case "finance_agent": return "金融分析师"
        case "agno_assist": return "Agno助手"
        case "crypto_prediction_agent": return "加密货币分析师"
        default: return agentId
        }
    }
    
    private func getAgentDescription(_ agentId: String) -> String {
        switch agentId {
        case "web_agent": return "网络搜索和信息检索"
        case "finance_agent": return "金融数据分析和市场洞察"
        case "agno_assist": return "Agno框架使用指导和代码示例"
        case "crypto_prediction_agent": return "加密货币市场分析和预测"
        default: return "AI助手"
        }
    }
    
    private func getAgentIcon(_ agentId: String) -> String {
        switch agentId {
        case "web_agent": return "🔍"
        case "finance_agent": return "💰"
        case "agno_assist": return "🤖"
        case "crypto_prediction_agent": return "🪙"
        default: return "🤖"
        }
    }
}
```

### 5. UI组件实现

```swift
// MessageBubbleView.swift
struct MessageBubbleView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if case .user = message.sender {
                Spacer(minLength: UIScreen.main.bounds.width * 0.25)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(message.content)
                    .font(.body)
                    .foregroundColor(textColor)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(backgroundColor)
                    .cornerRadius(12)
                
                HStack {
                    if case .user = message.sender {
                        Spacer()
                    }
                    
                    Text(formatTime(message.timestamp))
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    if case .user = message.sender {
                        statusIcon
                    }
                }
                .padding(.horizontal, 4)
            }
            
            if case .ai = message.sender {
                Spacer(minLength: UIScreen.main.bounds.width * 0.2)
            }
        }
        .padding(.horizontal, 16)
    }
    
    private var backgroundColor: Color {
        switch message.sender {
        case .user:
            return .blue
        case .ai, .system:
            return Color(.systemGray5)
        }
    }
    
    private var textColor: Color {
        switch message.sender {
        case .user:
            return .white
        case .ai, .system:
            return .primary
        }
    }
    
    @ViewBuilder
    private var statusIcon: some View {
        switch message.status {
        case .sending:
            ProgressView()
                .scaleEffect(0.5)
        case .sent:
            Image(systemName: "checkmark")
                .foregroundColor(.secondary)
        case .delivered:
            Image(systemName: "checkmark.circle")
                .foregroundColor(.blue)
        case .failed:
            Image(systemName: "exclamationmark.circle")
                .foregroundColor(.red)
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// ChatInputView.swift
struct ChatInputView: View {
    @Binding var text: String
    let isLoading: Bool
    let onSend: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: 12) {
                Button(action: {
                    // 附件功能
                }) {
                    Image(systemName: "paperclip")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
                .disabled(isLoading)
                
                HStack {
                    TextField("输入消息...", text: $text, axis: .vertical)
                        .textFieldStyle(.plain)
                        .lineLimit(1...5)
                    
                    if !text.isEmpty {
                        Button(action: onSend) {
                            Image(systemName: "arrow.up.circle.fill")
                                .font(.title2)
                                .foregroundColor(.blue)
                        }
                        .disabled(isLoading)
                    } else {
                        Button(action: {
                            // 语音输入功能
                        }) {
                            Image(systemName: "mic.fill")
                                .font(.title2)
                                .foregroundColor(.blue)
                        }
                        .disabled(isLoading)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color(.systemGray6))
                .cornerRadius(20)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
        }
        .background(Color(.systemBackground))
    }
}
```

## 性能优化

### 内存管理
```swift
// 使用弱引用避免循环引用
class ChatViewModel: ObservableObject {
    private var cancellables = Set<AnyCancellable>()
    
    deinit {
        cancellables.removeAll()
    }
}

// 图片缓存
class ImageCache {
    static let shared = ImageCache()
    private let cache = NSCache<NSString, UIImage>()
    
    private init() {
        cache.countLimit = 100
        cache.totalCostLimit = 50 * 1024 * 1024 // 50MB
    }
}
```

### 网络优化
```swift
// 请求去重
class RequestManager {
    private var activeRequests: [String: AnyCancellable] = [:]
    
    func request<T: Codable>(_ endpoint: APIEndpoint) -> AnyPublisher<T, APIError> {
        let key = endpoint.cacheKey
        
        if let existingRequest = activeRequests[key] {
            // 返回现有请求
        }
        
        let publisher = apiService.request(endpoint)
            .handleEvents(receiveCompletion: { [weak self] _ in
                self?.activeRequests.removeValue(forKey: key)
            })
            .share()
        
        activeRequests[key] = publisher.sink(receiveCompletion: { _ in }, receiveValue: { _ in })
        
        return publisher
    }
}
```

### UI性能
```swift
// 使用LazyVStack优化长列表
ScrollView {
    LazyVStack(spacing: 8) {
        ForEach(messages) { message in
            MessageBubbleView(message: message)
                .id(message.id)
        }
    }
}

// 图片懒加载
struct AsyncImageView: View {
    let url: URL?
    
    var body: some View {
        AsyncImage(url: url) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fit)
        } placeholder: {
            ProgressView()
        }
    }
}
```

这份技术实现指南提供了完整的iOS应用开发框架，包括架构设计、核心功能实现和性能优化。开发团队可以基于这些指南快速搭建高质量的iOS AI助手应用。
