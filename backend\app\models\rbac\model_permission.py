from typing import List, ForwardRef
from uuid import UUID

from sqlalchemy import Column, Boolean, UniqueConstraint
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixUUID, MixDeleted, MixTime
from app.models.rbac.model_rbac_base import PermissionBase

# 避免循环导入
Role = ForwardRef("Role")
RolePermission = ForwardRef("RolePermission")


# 权限创建模型
class PermissionCreate(PermissionBase):
    """权限创建模型
    
    用于创建新权限
    """
    pass


# 权限更新模型
class PermissionUpdate(SQLModel):
    """权限更新模型
    
    用于更新权限信息
    """
    name: str | None = Field(None, min_length=2, max_length=50, title="权限名称")
    description: str | None = Field(None, title="权限描述")
    is_active: bool | None = Field(None, title="是否启用")


# 权限数据库模型
class Permission(PermissionBase, MixUUID, MixDeleted, MixTime, table=True):
    """权限表
    
    系统中的权限定义
    """
    __tablename__ = "permissions"
    __table_args__ = (
        UniqueConstraint("code", name="uq_permission_code"),
        UniqueConstraint("resource", "action", name="uq_resource_action"),
        {"schema": "rbac"}
    )

    # 扩展字段
    is_system: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="是否系统权限")
    is_active: bool = Field(default=True, sa_column=Column(Boolean, nullable=False, default=True), title="是否启用")

    # 关系
    roles: List["RolePermission"] = Relationship(back_populates="permission")


# 权限响应模型
class PermissionResponse(PermissionBase):
    """权限响应模型
    
    用于API响应返回权限信息
    """
    id: UUID
    is_system: bool
    is_active: bool

    class Config:
        from_attributes = True


# 权限列表响应模型
class PermissionListResponse(SQLModel):
    """权限列表响应模型"""
    total_count: int
    data: List[PermissionResponse]
