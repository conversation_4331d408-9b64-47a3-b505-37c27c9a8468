import SwiftUI

struct UserProfileView: View {
    @EnvironmentObject var userData: UserData
    @EnvironmentObject var themeSettings: ThemeSettings
    @EnvironmentObject var languageManager: LanguageManager
    @Binding var isPresented: Bool
    
    @State private var showingLogoutAlert = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 用户头像和基本信息
                    VStack(spacing: 16) {
                        // 头像
                        AsyncImage(url: URL(string: userData.currentUser?.avatar ?? "")) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Image(systemName: "person.crop.circle.fill")
                                .font(.system(size: 80))
                                .foregroundColor(.gray)
                        }
                        .frame(width: 100, height: 100)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.3), lineWidth: 2)
                        )
                        
                        // 用户名和邮箱
                        VStack(spacing: 4) {
                            Text(userData.currentUser?.username ?? "用户")
                                .font(.title2)
                                .fontWeight(.bold)
                            
                            Text(userData.currentUser?.email ?? "")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.top, 20)
                    
                    // 用户信息卡片
                    VStack(spacing: 16) {
                        // 账户信息
                        GroupBox {
                            VStack(spacing: 12) {
                                InfoRow(title: "用户ID", value: userData.currentUser?.id ?? "")
                                InfoRow(title: "用户名", value: userData.currentUser?.username ?? "")
                                InfoRow(title: "邮箱", value: userData.currentUser?.email ?? "")
                                if let createdAt = userData.currentUser?.createdAt {
                                    InfoRow(title: "注册时间", value: formatDate(createdAt))
                                }
                            }
                        } label: {
                            Label("账户信息", systemImage: "person.circle")
                                .font(.headline)
                        }
                        
                        // 应用设置
                        GroupBox {
                            VStack(spacing: 12) {
                                HStack {
                                    Label("主题模式", systemImage: "paintbrush")
                                    Spacer()
                                    Picker("主题", selection: $themeSettings.isDarkMode) {
                                        Text("浅色模式").tag(false)
                                        Text("深色模式").tag(true)
                                    }
                                    .pickerStyle(SegmentedPickerStyle())
                                    .frame(width: 200)
                                }
                                
                                HStack {
                                    Label("语言设置", systemImage: "globe")
                                    Spacer()
                                    Picker("语言", selection: $languageManager.currentLanguage) {
                                        Text("中文").tag("zh")
                                        Text("English").tag("en")
                                    }
                                    .pickerStyle(SegmentedPickerStyle())
                                    .frame(width: 120)
                                }
                            }
                        } label: {
                            Label("应用设置", systemImage: "gear")
                                .font(.headline)
                        }
                        
                        // 操作按钮
                        VStack(spacing: 12) {
                            Button(action: {
                                showingLogoutAlert = true
                            }) {
                                HStack {
                                    Image(systemName: "rectangle.portrait.and.arrow.right")
                                    Text("退出登录")
                                }
                                .foregroundColor(.red)
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 10)
                                        .stroke(Color.red, lineWidth: 1)
                                )
                            }
                        }
                    }
                    .padding(.horizontal)
                    
                    Spacer(minLength: 20)
                }
            }
            .navigationTitle("个人中心")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        isPresented = false
                    }
                }
            }
            .alert("确认退出", isPresented: $showingLogoutAlert) {
                Button("取消", role: .cancel) { }
                Button("退出", role: .destructive) {
                    userData.logout()
                    isPresented = false
                }
            } message: {
                Text("确定要退出登录吗？")
            }
        }
        .preferredColorScheme(themeSettings.colorScheme)
    }
    
    private func formatDate(_ dateString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'"
        
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateStyle = .medium
            displayFormatter.timeStyle = .short
            displayFormatter.locale = Locale(identifier: languageManager.currentLanguage == "zh" ? "zh_CN" : "en_US")
            return displayFormatter.string(from: date)
        }
        
        return dateString
    }
}

struct InfoRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            Spacer()
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .multilineTextAlignment(.trailing)
        }
    }
}

#Preview {
    UserProfileView(isPresented: .constant(true))
        .environmentObject(UserData())
        .environmentObject(ThemeSettings())
        .environmentObject(LanguageManager())
} 