import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { useUserStore } from '@/stores/user'; // 假设有用户store用于获取token

// 自定义扩展AxiosRequestConfig的接口
interface CustomAxiosConfig extends InternalAxiosRequestConfig {
  isToken?: boolean; // 是否需要携带token
  isLoading?: boolean; // 是否显示loading
  isRawResponse?: boolean; // 是否返回原始响应，不进行处理
}

// 存储请求标识和取消函数的Map
const pendingMap = new Map<string, AbortController>();

// 生成每个请求唯一的键
function getPendingKey(config: any): string {
  const { url, method, params, data } = config;
  return [url, method, JSON.stringify(params), JSON.stringify(data)].join('&');
}

// 储存每个请求的AbortController
function addPending(config: any): void {
  const pendingKey = getPendingKey(config);
  if (!pendingMap.has(pendingKey)) {
    const controller = new AbortController();
    config.signal = controller.signal;
    pendingMap.set(pendingKey, controller);
  }
}

// 取消重复的请求
function removePending(config: any): void {
  const pendingKey = getPendingKey(config);
  if (pendingMap.has(pendingKey)) {
    const controller = pendingMap.get(pendingKey);
    controller?.abort('重复的请求被取消');
    pendingMap.delete(pendingKey);
  }
}

// 清空所有pending请求
export function clearPending(): void {
  pendingMap.forEach(controller => {
    controller.abort('请求被取消');
  });
  pendingMap.clear();
}

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL as string, // 从环境变量读取API地址
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 移除重复请求
    removePending(config);
    // 添加请求到pending中
    addPending(config);

    // 默认需要携带token
    const customConfig = config as unknown as CustomAxiosConfig;
    if (customConfig.isToken !== false) {
      const userStore = useUserStore();
      const token = userStore.token;
      if (token) {
        // 正确设置headers
        config.headers.set('Authorization', `Bearer ${token}`);
      }
    }

    // GET请求防止缓存
    if (config.method?.toLowerCase() === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now(),
      };
    }

    return config;
  },
  (error: any) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    // 从pending中移除已完成的请求
    removePending(response.config);

    const customConfig = response.config as unknown as CustomAxiosConfig;

    // 如果设置了isRawResponse，直接返回原始响应数据
    if (customConfig.isRawResponse) {
      return response.data;
    }

    // 检查响应数据是否有code字段，有则按标准响应处理
    const res = response.data;
    if (res && typeof res === 'object' && 'code' in res) {
      if (res.code !== 200) {
        // 处理各种错误码
        const errorMessage = res.message || '未知错误';

        // 处理特定错误码
        if (res.code === 401) {
          // Token失效，清除用户信息并跳转到登录页
          const userStore = useUserStore();
          userStore.$reset(); // 使用 $reset 代替 logout
          window.location.href = '/login';
          return Promise.reject(new Error('登录状态已过期，请重新登录'));
        }

        // 显示错误消息 (实际项目中可以使用UI库的消息提示)
        console.error(errorMessage);

        return Promise.reject(new Error(errorMessage));
      }

      // 只返回数据部分
      return res.data;
    }

    // 没有code字段，直接返回响应数据
    return res;
  },
  (error: any) => {
    // 从pending中移除已完成的请求
    if (error.config) {
      removePending(error.config);
    }

    // 如果是请求取消的错误，不做处理
    if (axios.isCancel(error)) {
      console.log('已取消的请求：', error.message);
      return Promise.reject(error);
    }

    // 处理网络错误
    const status = error.response?.status;
    let message = '';
    // 提前声明变量，解决case中词法声明的问题
    let userStore;

    switch (status) {
      case 400:
        message = '请求错误';
        break;
      case 401:
        message = '未授权，请重新登录';
        // 跳转到登录页
        userStore = useUserStore();
        userStore.$reset(); // 使用 $reset 代替 logout
        window.location.href = '/login';
        break;
      case 403:
        message = '拒绝访问';
        break;
      case 404:
        message = '请求地址出错';
        break;
      case 408:
        message = '请求超时';
        break;
      case 500:
        message = '服务器内部错误';
        break;
      case 501:
        message = '服务未实现';
        break;
      case 502:
        message = '网关错误';
        break;
      case 503:
        message = '服务不可用';
        break;
      case 504:
        message = '网关超时';
        break;
      case 505:
        message = 'HTTP版本不受支持';
        break;
      default:
        message = '网络连接故障';
    }

    // 显示错误消息
    console.error(message);

    return Promise.reject(error);
  }
);

// 封装常用请求方法
export const request = {
  get<T = any>(url: string, params?: any, config?: any): Promise<T> {
    return service.get(url, { params, ...config });
  },

  post<T = any>(url: string, data?: any, config?: any): Promise<T> {
    return service.post(url, data, config);
  },

  put<T = any>(url: string, data?: any, config?: any): Promise<T> {
    return service.put(url, data, config);
  },

  delete<T = any>(url: string, config?: any): Promise<T> {
    return service.delete(url, config);
  },

  // 上传文件
  upload<T = any>(url: string, file: File, config?: any): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);

    return service.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      ...config,
    });
  },

  // 下载文件
  download(url: string, params?: any, config?: any): Promise<Blob> {
    return service.get(url, {
      params,
      responseType: 'blob',
      ...config,
    });
  },

  // 流式响应处理
  stream: async (options: {
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    data?: any;
    headers?: Record<string, string>;
    onChunk: (chunk: any) => void;
    onError?: (error: Error) => void;
    onComplete?: () => void;
  }): Promise<void> => {
    const { url, method = 'POST', data, headers = {}, onChunk, onError = () => {}, onComplete = () => {} } = options;

    // 获取用户token
    const userStore = useUserStore();
    const token = userStore.token;

    // 构建请求头
    const requestHeaders: Record<string, string> = {
      ...headers,
    };

    // 添加认证头
    if (token) {
      requestHeaders['Authorization'] = `Bearer ${token}`;
    }

    // 构建请求体
    let requestBody: FormData | string | undefined;
    if (data) {
      if (data instanceof FormData) {
        requestBody = data;
      } else {
        requestHeaders['Content-Type'] = 'application/json';
        requestBody = JSON.stringify(data);
      }
    }

    // 构建完整URL（使用代理）
    const fullUrl = url.startsWith('/') ? url : `/${url}`;

    let buffer = '';

    try {
      const response = await fetch(fullUrl, {
        method,
        headers: requestHeaders,
        body: requestBody,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      if (!response.body) {
        throw new Error('响应体不存在');
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      // 解析流缓冲区中的 JSON 对象
      const parseStreamBuffer = (buffer: string): string => {
        let jsonStartIndex = buffer.indexOf('{');

        while (jsonStartIndex !== -1) {
          let braceCount = 0;
          let inString = false;
          let escapeNext = false;
          let jsonEndIndex = -1;

          // 遍历缓冲区以找到 JSON 对象的结尾
          for (let i = jsonStartIndex; i < buffer.length; i++) {
            const char = buffer[i];

            if (escapeNext) {
              // 如果前一个字符是转义字符，跳过当前字符
              escapeNext = false;
              continue;
            }

            if (char === '\\') {
              // 当前字符是转义字符
              escapeNext = true;
              continue;
            }

            // 检查字符是否为双引号
            if (char === '"') {
              inString = !inString;
            }

            // 如果字符不在字符串内，计算大括号
            if (!inString) {
              if (char === '{') braceCount++;
              if (char === '}') braceCount--;
            }

            // 如果大括号计数为0，我们找到了 JSON 对象的结尾
            if (braceCount === 0) {
              jsonEndIndex = i;
              break;
            }
          }

          // 如果找到完整的 JSON 对象，处理它
          if (jsonEndIndex !== -1) {
            const jsonString = buffer.slice(jsonStartIndex, jsonEndIndex + 1);
            try {
              const parsed = JSON.parse(jsonString);
              onChunk(parsed);
            } catch (error) {
              console.warn('解析JSON失败:', error, 'JSON:', jsonString);
              // 跳过无效的 JSON，继续累积
              break;
            }
            buffer = buffer.slice(jsonEndIndex + 1).trim();
            jsonStartIndex = buffer.indexOf('{');
          } else {
            // 没有找到完整的 JSON，等待下一个数据块
            break;
          }
        }

        return buffer;
      };

      // 递归处理流
      const processStream = async (): Promise<void> => {
        const { done, value } = await reader.read();

        if (done) {
          // 处理缓冲区中的任何最终数据
          buffer = parseStreamBuffer(buffer);
          onComplete();
          return;
        }

        // 解码并累积数据块
        buffer += decoder.decode(value, { stream: true });

        // 解析缓冲区中可用的任何完整 JSON 对象
        buffer = parseStreamBuffer(buffer);

        // 继续处理流
        await processStream();
      };

      await processStream();
    } catch (error) {
      onError(error instanceof Error ? error : new Error(String(error)));
    }
  },
};

// 导出实例和请求方法
export default service;
