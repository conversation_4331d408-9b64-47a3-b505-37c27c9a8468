"""市场行情模型

对应数据库中的 crypto.ticker 表，遵循SQL文件定义
"""

from sqlmodel import Field, Relationship

from app.db.db_orm_base import SchemaBase
from app.db.db_orm_base_model import MixTime

# 前向引用
Symbol = "Symbol"


# 数据库市场行情模型
class Ticker(SchemaBase, MixTime, table=True):
    """市场行情数据库模型，对应crypto.ticker表"""
    __tablename__ = "ticker"
    __table_args__ = ({"schema": "crypto"})

    # 主键字段
    symbol_id: int = Field(..., primary_key=True, title="关联的交易对ID")
    open: float = Field(..., title="开盘价")
    high: float = Field(..., title="最高价")
    low: float = Field(..., title="最低价")
    close: float = Field(..., title="最新成交价")
    count: int = Field(0, title="成交笔数")
    volume: float = Field(..., title="成交量")

    # 关系
    symbol: Symbol = Relationship(
        back_populates="ticker",
        sa_relationship_kwargs={
            "primaryjoin": "Ticker.symbol_id == Symbol.id",
            "foreign_keys": "Ticker.symbol_id"
        }
    )
