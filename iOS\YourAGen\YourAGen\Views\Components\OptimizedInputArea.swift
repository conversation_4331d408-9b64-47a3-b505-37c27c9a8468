import SwiftUI

struct OptimizedInputArea: View {
    @Binding var inputText: String
    @Binding var isInputFocused: Bool
    
    let canSendMessage: Bool
    let isStreaming: Bool
    let isLoading: Bool
    let errorMessage: String?
    let feedbackMessage: String?
    let placeholder: String
    
    let onSend: () -> Void
    let onRetry: () -> Void
    
    // 键盘预热状态
    @State private var keyboardPrewarmed = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 分割线
            Divider()
            
            // 错误提示
            if let errorMessage = errorMessage {
                ErrorMessageView(message: errorMessage, onRetry: onRetry)
            }
            
            // 反馈消息
            if let feedbackMessage = feedbackMessage {
                FeedbackMessageView(message: feedbackMessage)
            }
            
            // 输入框区域
            inputSection
            
            // 状态指示器
            if isStreaming || isLoading {
                StatusIndicatorView(isStreaming: isStreaming)
            }
            
            // 隐藏的键盘预热TextField
            if !keyboardPrewarmed {
                TextField("", text: .constant(""))
                    .frame(width: 0, height: 0)
                    .opacity(0)
                    .onAppear {
                        // 延迟预热键盘，避免影响初始加载
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            keyboardPrewarmed = true
                        }
                    }
            }
        }
    }
    
    private var inputSection: some View {
        HStack(spacing: 12) {
            // 优化的文本输入框
            OptimizedTextField(
                text: $inputText,
                isInputFocused: $isInputFocused,
                placeholder: placeholder,
                onSubmit: {
                    if canSendMessage {
                        onSend()
                    }
                }
            )
            
            // 发送按钮
            SendButton(
                isStreaming: isStreaming,
                canSendMessage: canSendMessage,
                onSend: onSend
            )
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(Color(.systemBackground))
    }
}

// MARK: - 优化的TextField组件
struct OptimizedTextField: View {
    @Binding var text: String
    @Binding var isInputFocused: Bool
    
    let placeholder: String
    let onSubmit: () -> Void
    
    // 内部的FocusState
    @FocusState private var internalFocus: Bool
    
    var body: some View {
        TextField(placeholder, text: $text, axis: .vertical)
            .textFieldStyle(OptimizedTextFieldStyle())
            .focused($internalFocus)
            .lineLimit(1...6)
            .onSubmit {
                if !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    onSubmit()
                }
            }
            .onChange(of: isInputFocused) { newValue in
                internalFocus = newValue
            }
            .onChange(of: internalFocus) { newValue in
                isInputFocused = newValue
            }
    }
}

// MARK: - 优化的TextField样式
struct OptimizedTextFieldStyle: TextFieldStyle {
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                // 使用更简单的背景实现
                RoundedRectangle(cornerRadius: 24, style: .continuous)
                    .fill(Color(.systemGray6))
            )
    }
}

// MARK: - 发送按钮组件
struct SendButton: View {
    let isStreaming: Bool
    let canSendMessage: Bool
    let onSend: () -> Void
    
    var body: some View {
        Button(action: onSend) {
            Image(systemName: isStreaming ? "stop.circle.fill" : "arrow.up.circle.fill")
                .font(.system(size: 32))
                .foregroundColor(canSendMessage ? .blue : .gray)
        }
        .disabled(!canSendMessage && !isStreaming)
        .buttonStyle(PlainButtonStyle()) // 减少按钮动画开销
    }
}

// MARK: - 错误消息视图
struct ErrorMessageView: View {
    let message: String
    let onRetry: () -> Void
    
    var body: some View {
        HStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)
            Text(message)
                .font(.caption)
                .foregroundColor(.red)
            Spacer()
            Button("重试", action: onRetry)
                .font(.caption)
                .foregroundColor(.blue)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color.red.opacity(0.1))
    }
}

// MARK: - 反馈消息视图
struct FeedbackMessageView: View {
    let message: String
    
    var body: some View {
        HStack {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
            Text(message)
                .font(.caption)
                .foregroundColor(.green)
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .background(Color.green.opacity(0.1))
        .transition(.opacity.combined(with: .move(edge: .top)))
    }
}

// MARK: - 状态指示器视图
struct StatusIndicatorView: View {
    let isStreaming: Bool
    
    var body: some View {
        HStack(spacing: 8) {
            ProgressView()
                .scaleEffect(0.8)
            Text(isStreaming ? "AI正在思考..." : "加载中...")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.bottom, 8)
    }
}

// MARK: - 预览
#Preview {
    OptimizedInputArea(
        inputText: .constant(""),
        isInputFocused: .constant(false),
        canSendMessage: true,
        isStreaming: false,
        isLoading: false,
        errorMessage: nil,
        feedbackMessage: nil,
        placeholder: "输入消息...",
        onSend: {},
        onRetry: {}
    )
} 