services:
  traefik:
    image: traefik:v3.3.5  # 升级到 v3.3.5
    container_name: traefik
    restart: always
    ports:
      - "80:80"
      - "443:443/tcp"
      - "443:443/udp"  # 保留 HTTP/3 支持
      - "8080:8080"
    environment:
      - CF_API_EMAIL=<EMAIL>
      - CF_API_KEY=e633e24a810c85e272850d7ced08b91efb01f
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/etc/traefik/traefik.yml
      - ./traefik_dynamic.yml:/etc/traefik/traefik_dynamic.yml
      - ./certs:/etc/traefik/certs
    networks:
      - proxy-network
    # labels:  # Docker标签，用于Traefik自身配置
      # - "traefik.enable=true"  # 启用Traefik对该容器的路由
      # 以下是管理后台访问配置（已注释）
      # - "traefik.http.routers.dashboard.rule=Host(`traefik.aiquant.io`)"  # 设置仪表盘域名
      # - "traefik.http.routers.dashboard.service=api@internal"  # 指向Traefik内部API服务
      # - "traefik.http.routers.dashboard.entrypoints=websecure"  # 使用websecure入口点(HTTPS)
      # - "traefik.http.routers.dashboard.tls=true"  # 启用TLS加密
      # - "traefik.http.routers.dashboard.tls.certresolver=letsencryptResolver"  # 使用Let's Encrypt解析器
      # - "traefik.http.routers.dashboard.priority=200"  # 路由优先级，数字越大优先级越高
      # 可选：添加基本认证保护（HTTP Basic Auth）
      # - "traefik.http.middlewares.auth.basicauth.users=admin:$$apr1$$talLP/WS$$J8qJRDmxHKx9L3RdUUXER/"  # 用户名:密码哈希
      # - "traefik.http.routers.dashboard.middlewares=auth@docker"  # 应用认证中间件到仪表盘

  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: always
    expose:
      - "80"
    volumes:
      - ./nginx_docker.conf:/etc/nginx/nginx.conf
      - ./certs:/etc/nginx/certs
      - nginx_logs:/var/log/nginx
      - ../coin_img:/root/PycharmProjects/aiquant2022/coin_img
      - ../image:/root/PycharmProjects/aiquant2022/image
      - /home/<USER>/aiquant-frontend/dist:/home/<USER>/aiquant-frontend/dist
    networks:
      - proxy-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.nginx.rule=Host(`aiquant.io`) || Host(`www.aiquant.io`) || Host(`api.aiquant.io`)"
      - "traefik.http.routers.nginx.entrypoints=websecure"
      - "traefik.http.routers.nginx.tls=true"
      - "traefik.http.routers.nginx.tls.certresolver=letsencryptResolver"
      - "traefik.http.routers.nginx.tls.options=default"  # 使用默认TLS配置选项
      - "traefik.http.routers.nginx.tls.domains[0].main=aiquant.io"
      - "traefik.http.routers.nginx.tls.domains[0].sans=*.aiquant.io"
      - "traefik.http.services.nginx.loadbalancer.server.port=80"
      - "traefik.http.middlewares.nginx-compress.compress=true"  # 启用压缩
      - "traefik.http.routers.nginx.middlewares=default-headers@file,nginx-compress@docker"  # 应用多个中间件

volumes:  # 定义持久化数据卷
  nginx_logs:  # Nginx日志卷，保存日志数据

networks:  # 定义容器网络
  proxy-network:  # 创建proxy-network网络
    driver: bridge  # 使用桥接模式，允许容器之间及容器与主机之间通信
    external: true