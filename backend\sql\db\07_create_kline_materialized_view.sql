
CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_30m
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('30m', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('30m', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_1h
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('1h', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('1h', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_2h
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('2h', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('2h', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_3h
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('3h', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('3h', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_4h
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('4h', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('4h', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_6h
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('6h', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('6h', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_8h
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('8h', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('8h', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_12h
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('12h', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('12h', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_1d
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('1d', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('1d', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_2d
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('2d', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('2d', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_3d
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('3d', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('3d', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_1w
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('1w', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('1w', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_2w
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('2w', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('2w', timestamp)
WITH NO DATA;

CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_3w
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('3w', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('3w', timestamp)
WITH NO DATA;

CALL refresh_continuous_aggregate('kline.kline_30m', NULL, now() - INTERVAL '30m');
CALL refresh_continuous_aggregate('kline.kline_1h', NULL, now() - INTERVAL '1h');
CALL refresh_continuous_aggregate('kline.kline_2h', NULL, now() - INTERVAL '2h');
CALL refresh_continuous_aggregate('kline.kline_3h', NULL, now() - INTERVAL '3h');
CALL refresh_continuous_aggregate('kline.kline_4h', NULL, now() - INTERVAL '4h');
CALL refresh_continuous_aggregate('kline.kline_6h', NULL, now() - INTERVAL '6h');
CALL refresh_continuous_aggregate('kline.kline_8h', NULL, now() - INTERVAL '8h');
CALL refresh_continuous_aggregate('kline.kline_12h', NULL, now() - INTERVAL '12h');
CALL refresh_continuous_aggregate('kline.kline_1d', NULL, now() - INTERVAL '1d');
CALL refresh_continuous_aggregate('kline.kline_2d', NULL, now() - INTERVAL '2d');
CALL refresh_continuous_aggregate('kline.kline_3d', NULL, now() - INTERVAL '3d');
CALL refresh_continuous_aggregate('kline.kline_1w', NULL, now() - INTERVAL '1w');
CALL refresh_continuous_aggregate('kline.kline_2w', NULL, now() - INTERVAL '2w');
CALL refresh_continuous_aggregate('kline.kline_3w', NULL, now() - INTERVAL '3w');


-- SELECT remove_continuous_aggregate_policy('kline.kline_30m');
SELECT add_continuous_aggregate_policy('kline.kline_30m', NULL, INTERVAL '30m', INTERVAL '30m');

-- SELECT remove_continuous_aggregate_policy('kline.kline_1h');
SELECT add_continuous_aggregate_policy('kline.kline_1h', NULL, INTERVAL '1h', INTERVAL '1h');

-- SELECT remove_continuous_aggregate_policy('kline.kline_2h');
SELECT add_continuous_aggregate_policy('kline.kline_2h', NULL, INTERVAL '2h', INTERVAL '2h');

-- SELECT remove_continuous_aggregate_policy('kline.kline_3h');
SELECT add_continuous_aggregate_policy('kline.kline_3h', NULL, INTERVAL '3h', INTERVAL '3h');

-- SELECT remove_continuous_aggregate_policy('kline.kline_4h');
SELECT add_continuous_aggregate_policy('kline.kline_4h', NULL, INTERVAL '4h', INTERVAL '4h');

-- SELECT remove_continuous_aggregate_policy('kline.kline_6h');
SELECT add_continuous_aggregate_policy('kline.kline_6h', NULL, INTERVAL '6h', INTERVAL '6h');

-- SELECT remove_continuous_aggregate_policy('kline.kline_8h');
SELECT add_continuous_aggregate_policy('kline.kline_8h', NULL, INTERVAL '8h', INTERVAL '8h');

-- SELECT remove_continuous_aggregate_policy('kline.kline_12h');
SELECT add_continuous_aggregate_policy('kline.kline_12h', NULL, INTERVAL '12h', INTERVAL '12h');

-- SELECT remove_continuous_aggregate_policy('kline.kline_1d');
SELECT add_continuous_aggregate_policy('kline.kline_1d', NULL, INTERVAL '1d', INTERVAL '1d');

-- SELECT remove_continuous_aggregate_policy('kline.kline_2d');
SELECT add_continuous_aggregate_policy('kline.kline_2d', NULL, INTERVAL '2d', INTERVAL '2d');

-- SELECT remove_continuous_aggregate_policy('kline.kline_3d');
SELECT add_continuous_aggregate_policy('kline.kline_3d', NULL, INTERVAL '3d', INTERVAL '3d');

-- SELECT remove_continuous_aggregate_policy('kline.kline_1w');
SELECT add_continuous_aggregate_policy('kline.kline_1w', NULL, INTERVAL '1w', INTERVAL '1w');

-- SELECT remove_continuous_aggregate_policy('kline.kline_2w');
SELECT add_continuous_aggregate_policy('kline.kline_2w', NULL, INTERVAL '2w', INTERVAL '2w');

-- SELECT remove_continuous_aggregate_policy('kline.kline_3w');
SELECT add_continuous_aggregate_policy('kline.kline_3w', NULL, INTERVAL '3w', INTERVAL '3w');
