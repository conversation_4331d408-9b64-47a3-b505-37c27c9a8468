# 阶段一：构建阶段
FROM python:3.12-slim AS builder

# 设置环境变量优化 Python 行为
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONOPTIMIZE=2

# 配置源
#RUN rm -f /etc/apt/sources.list.d/* && \
#    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian bookworm main" > /etc/apt/sources.list && \
#    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian-security bookworm-security main" >> /etc/apt/sources.list

# 配置虚拟环境
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# 配置豆瓣镜像源
#RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple \
#    && pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

WORKDIR /build

# 安装依赖
COPY requirements.txt .
RUN apt-get update && \
    apt-get install -y --no-install-recommends gcc python3-dev libpq-dev && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir gunicorn && \
    apt-get purge -y gcc python3-dev libpq-dev && \
    apt-get autoremove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 阶段二：最终运行时镜像
FROM python:3.12-slim

# 配置源
#RUN rm -f /etc/apt/sources.list.d/* && \
#    echo "deb https://mirrors.tuna.tsinghua.edu.cn/debian bookworm main" > /etc/apt/sources.list

# 设置环境
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONOPTIMIZE=2 \
    TZ=Asia/Shanghai \
    PATH="/opt/venv/bin:$PATH"

WORKDIR /app

# 从构建阶段复制虚拟环境
COPY --from=builder /opt/venv /opt/venv

# 复制应用代码
COPY . .

# 安装精简的运行时依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends libpq5 curl tzdata && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    useradd --create-home --shell /bin/bash --system appuser && \
    mkdir -p /app/logs && \
    chmod 755 /app/logs && \
    chown -R appuser:appuser /app

# 切换用户
USER appuser

EXPOSE $BACKEND_PORT

# 启动命令
CMD ["sh", "-c", "gunicorn -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:$BACKEND_PORT --workers $BACKEND_WORKER_NUM --preload"]