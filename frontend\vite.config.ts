import { defineConfig, loadEnv, UserConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
// import eslintPlugin from 'vite-plugin-eslint'
import path from 'path';
import type { ProxyOptions } from 'vite';

/**
 * Vite 配置文件
 *
 * 这个文件配置了前端开发和构建环境的各种设置，包括：
 * - Vue 3 支持
 * - 开发服务器设置
 * - API 代理配置
 * - 路径别名
 * - CSS 预处理器（Tailwind CSS）
 *
 * 环境变量说明：
 * - VITE_PORT: 前端开发服务器端口（默认：20003）
 * - VITE_API_SERVER: 后端API服务器完整地址（默认：http://localhost:20002）
 * - VITE_API_BASE_URL: API基础路径（默认：/api）
 */

interface EnvConfig {
  port: number;
  apiServer: string;
  apiBaseUrl: string;
  debug: boolean;
  env: string;
}

function getEnvConfig(mode: string): EnvConfig {
  /**
   * Vite 环境变量加载机制详解：
   *
   * loadEnv(mode, process.cwd(), 'VITE_') 函数会按以下优先级加载环境变量文件：
   * 1. .env.local                    (最高优先级，本地开发配置，不会被提交到 git)
   * 2. .env.[mode].local             (如 .env.development.local)
   * 3. .env.[mode]                   (如 .env.development、.env.production)
   * 4. .env                          (默认环境变量文件)
   *
   * 参数说明：
   * - mode: 当前模式 ('development', 'production', 'test' 等)
   * - process.cwd(): 项目根目录路径
   * - 'VITE_': 只加载以 VITE_ 开头的环境变量（出于安全考虑）
   *
   * 例如：开发模式下 (yarn dev)，加载顺序为：
   * .env.local → .env.development.local → .env.development → .env
   */
  const env = loadEnv(mode, process.cwd(), 'VITE_');

  // 从加载的环境变量中提取配置，如果没有找到则使用默认值
  const port = parseInt(env.VITE_PORT || '20003');
  const apiServer = env.VITE_API_SERVER || 'http://localhost:20002';
  const apiBaseUrl = env.VITE_API_BASE_URL || '/api';
  const debug = env.VITE_DEBUG === 'true';
  const envMode = env.VITE_ENV || mode;

  return {
    port,
    apiServer,
    apiBaseUrl,
    debug,
    env: envMode,
  };
}

function createDevServerConfig(envConfig: EnvConfig) {
  if (envConfig.debug) {
    console.log(`🚀 Development server: http://localhost:${envConfig.port}`);
    console.log(`🔗 API proxy target: ${envConfig.apiServer}`);
    console.log(`📝 Environment: ${envConfig.env}`);
    console.log(`🔄 Proxy rule: ${envConfig.apiBaseUrl} -> ${envConfig.apiServer}`);
  }

  return {
    port: envConfig.port,
    open: true,
    host: true, // 允许外部访问
    proxy: {
      [envConfig.apiBaseUrl]: {
        target: envConfig.apiServer,
        changeOrigin: true,
        secure: true, // https需要secure: true
        ws: true, // 支持websocket
        rewrite: (path: string) => path, // 保持原始路径
        configure: (proxy: any, _options: any) => {
          proxy.on('error', (err: Error, _req: any, _res: any) => {
            console.log('❌ Proxy error:', err.message);
            console.log('❌ Full error:', err);
          });
          proxy.on('proxyReq', (_proxyReq: any, req: any, _res: any) => {
            console.log('📤 Proxying:', req.method, req.url);
            console.log('📤 To target:', envConfig.apiServer + req.url);
          });
          proxy.on('proxyRes', (proxyRes: any, req: any, _res: any) => {
            console.log('📥 Response:', proxyRes.statusCode, req.url);
          });
        },
      } as ProxyOptions,
    },
  };
}

function createResolveConfig() {
  return {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  };
}

function createPluginsConfig() {
  return [
    vue(),
    // eslintPlugin() // 可以根据需要启用
  ];
}

function createCssConfig() {
  return {
    postcss: {
      plugins: [require('tailwindcss'), require('autoprefixer')],
    },
  };
}

function createBuildConfig(envConfig: EnvConfig) {
  return {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: envConfig.debug,
    minify: envConfig.env === 'production',
  };
}

export default defineConfig(({ mode }): UserConfig => {
  // 获取环境配置（这里会自动加载 .env.local 文件）
  const envConfig = getEnvConfig(mode);

  return {
    plugins: createPluginsConfig(),
    resolve: createResolveConfig(),
    server: createDevServerConfig(envConfig),
    css: createCssConfig(),
    build: createBuildConfig(envConfig),
    define: {
      // 将环境变量暴露给客户端代码
      __APP_ENV__: JSON.stringify(envConfig.env),
      __APP_DEBUG__: envConfig.debug,
    },
  };
});
