import os
import sys
from contextvars import ContextVar
from pathlib import Path

from loguru import logger as loguru_logger

# Context variables for thread-safe request tracking
request_id_var = ContextVar("request_id", default="")
user_id_var = ContextVar("user_id", default="")

BASE_DIR = Path(__file__).resolve().parent.parent.parent


# 自定义过滤器，修改记录并添加模块名
def module_name_filter(record):
    # 处理特殊模块名情况
    if record["name"] == "__main__" or record["name"] == "__mp_main__":
        filename = os.path.basename(record["file"].path)
        module_name = os.path.splitext(filename)[0]
        record["extra"]["module_name"] = module_name
    else:
        # 否则使用原始模块名
        record["extra"]["module_name"] = record["name"]
    return True


def get_logger(name: str | None = 'app') -> loguru_logger:
    """Returns a logger with current context and name"""
    # Format context values for display
    req_id = request_id_var.get()
    user_id = user_id_var.get()

    req_str = f"[req:{req_id}] " if req_id else ""
    user_str = f"[user:{user_id}] " if user_id else ""

    return loguru_logger.bind(name=name, request_id=req_str, user_id=user_str)


def set_request_id(request_id: str):
    """Set request ID for the current context"""
    request_id_var.set(request_id)


def set_user_id(user_id: str):
    """Set user ID for the current context"""
    user_id_var.set(user_id)


class LogManager:
    """Manages logging configuration and provides loggers with context support"""

    def __init__(self):
        # Create logs directory
        self.logs_dir = BASE_DIR / "logs"
        self.logs_dir.mkdir(exist_ok=True)

        # 修改日志格式，使用extra中的module_name替代name
        self.log_format = ("<green>{time:YYYY-MM-DD HH:mm:ss.SSS Z}</green> |"
                           "<level>{level: ^8}</level>| "
                           "<cyan>{extra[module_name]}.py:{line}</cyan>]: "
                           "<level>{message}</level>")

        # Remove default handler
        loguru_logger.remove()

        # Console handler with filter
        loguru_logger.add(
            sys.stderr,
            format=self.log_format,
            level="INFO",
            colorize=True,
            filter=module_name_filter
        )

        # Full log file with filter
        loguru_logger.add(
            self.logs_dir / "app_{time:YYYY-MM-DD}.log",
            format=self.log_format,
            level="DEBUG",
            rotation="00:00",
            retention="7 days",
            compression="zip",
            encoding="utf-8",
            enqueue=True,
            filter=module_name_filter
        )

        # Error log file with filter
        loguru_logger.add(
            self.logs_dir / "error_{time:YYYY-MM-DD}.log",
            format=self.log_format,
            level="ERROR",
            rotation="00:00",
            retention="30 days",
            compression="zip",
            encoding="utf-8",
            enqueue=True,
            filter=module_name_filter
        )


# Initialize log manager
log_manager = LogManager()

# Compatibility with existing code
logger = get_logger()

if __name__ == '__main__':
    # Test code
    set_request_id("test-123")
    set_user_id("user-456")

    test_logger = get_logger("test_module")
    test_logger.info("This is an info message.")
    test_logger.debug("This is a debug message.")
    test_logger.warning("This is a warning message.")
    test_logger.error("This is an error message.")
    test_logger.critical("This is a critical message.")
    test_logger.exception("This is an exception message.")

    # Test exception logging
    try:
        1 / 0
    except ZeroDivisionError as e:
        test_logger.exception(f"division by zero")

    # 测试直接导入的logger
    from loguru import logger as direct_logger

    direct_logger.info("This is a message from direct logger import")
