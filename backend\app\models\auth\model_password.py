from datetime import datetime
from typing import List, ForwardRef
from uuid import UUID

from sqlalchemy import Column, DateTime, ForeignKey, String
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixTime
from app.models.auth.model_auth_base import PasswordHistoryBase

# 避免循环导入
User = ForwardRef("User")


# 密码更改请求模型
class PasswordChangeRequest(SQLModel):
    """密码更改请求模型
    
    用于请求更改密码
    """
    current_password: str = Field(..., title="当前密码")
    new_password: str = Field(..., min_length=8, title="新密码")
    confirm_password: str = Field(..., min_length=8, title="确认密码")


# 密码重置请求模型
class PasswordResetRequest(SQLModel):
    """密码重置请求模型
    
    用于请求重置密码
    """
    email: str = Field(..., title="电子邮件")


# 密码重置确认模型
class PasswordResetConfirm(SQLModel):
    """密码重置确认模型
    
    用于确认重置密码
    """
    token: str = Field(..., title="令牌")
    new_password: str = Field(..., min_length=8, title="新密码")
    confirm_password: str = Field(..., min_length=8, title="确认密码")


# 密码历史数据库模型
class PasswordHistory(PasswordHistoryBase, MixTime, table=True):
    """密码历史表
    
    记录用户密码变更历史，用于密码策略执行
    """
    __tablename__ = "password_history"
    __table_args__ = {"schema": "auth"}

    # 主键
    id: int | None = Field(default=None, primary_key=True, title="ID")

    # 关联字段
    user_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("auth.users.id"), nullable=False), title="用户ID")

    # 密码相关字段
    password_salt: str | None = Field(None, sa_column=Column(String(64)), title="密码盐")
    changed_at: datetime = Field(sa_column=Column(DateTime, nullable=False), title="更改时间")

    # 关系
    user: "User" = Relationship(back_populates="password_history")


# 密码规则模型
class PasswordPolicy(SQLModel):
    """密码规则模型
    
    定义密码策略
    """
    min_length: int = Field(default=8, title="最小长度")
    require_uppercase: bool = Field(default=True, title="需要大写字母")
    require_lowercase: bool = Field(default=True, title="需要小写字母")
    require_digit: bool = Field(default=True, title="需要数字")
    require_special_char: bool = Field(default=True, title="需要特殊字符")
    history_count: int = Field(default=3, title="历史记录数")
    max_age_days: int | None = Field(default=90, title="最长使用天数")


# 密码验证结果模型
class PasswordValidationResult(SQLModel):
    """密码验证结果模型
    
    用于返回密码验证结果
    """
    valid: bool = Field(..., title="是否有效")
    errors: List[str] = Field(default=[], title="错误信息")
