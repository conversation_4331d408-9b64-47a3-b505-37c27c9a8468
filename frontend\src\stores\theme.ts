import { defineStore } from 'pinia';

type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeState {
  mode: ThemeMode;
  isSystemDark: boolean;
}

export const useThemeStore = defineStore('theme', {
  state: (): ThemeState => ({
    mode: 'system',
    isSystemDark: false,
  }),

  getters: {
    isDarkMode(): boolean {
      if (this.mode === 'dark') return true;
      if (this.mode === 'light') return false;
      return this.isSystemDark;
    },
  },

  actions: {
    init() {
      // 检测系统主题
      this.detectSystemTheme();

      // 监听系统主题变化
      this.setupSystemThemeListener();

      // 初始应用主题
      this.applyTheme();
    },

    setMode(newMode: ThemeMode) {
      this.mode = newMode;
      this.applyTheme();
    },

    toggleTheme() {
      if (this.mode === 'light') {
        this.setMode('dark');
      } else if (this.mode === 'dark') {
        this.setMode('light');
      } else {
        // 如果是系统模式，切换到相反的模式
        this.setMode(this.isSystemDark ? 'light' : 'dark');
      }
    },

    detectSystemTheme() {
      this.isSystemDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    },

    setupSystemThemeListener() {
      // 监听系统主题变化
      window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
        this.isSystemDark = e.matches;
        if (this.mode === 'system') {
          this.applyTheme();
        }
      });
    },

    applyTheme() {
      if (this.isDarkMode) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    },
  },

  persist: {
    key: 'theme-store',
    pick: ['mode'],
  },
});
