# 模型设计文档

## 概述

本项目使用SQLModel作为ORM框架，统一数据库模型和API验证模型。SQLModel结合了SQLAlchemy和Pydantic的功能，使用同一个类既可以定义数据库表结构，也可以用于API请求和响应的数据验证。

## 目录结构

```
models/
  ├── __init__.py               # 统一导出所有模型
  ├── auth/                     # 认证相关模型
  │   ├── __init__.py           # 导出认证模块的所有模型
  │   ├── base.py               # 认证模块的基础模型
  │   ├── user.py               # 用户模型
  │   ├── token.py              # 令牌模型
  │   ├── password.py           # 密码模型
  │   ├── auth_provider.py      # 认证提供者模型
  │   └── login.py              # 登录模型
  ├── rbac/                     # 角色权限控制模块
  │   ├── __init__.py           # 导出RBAC模块的所有模型
  │   ├── base.py               # RBAC模块的基础模型
  │   ├── role.py               # 角色模型
  │   ├── permission.py         # 权限模型
  │   ├── user_role.py          # 用户角色关联模型
  │   └── role_permission.py    # 角色权限关联模型
  └── notification/             # 通知系统模块
      ├── __init__.py           # 导出通知模块的所有模型
      ├── base.py               # 通知模块的基础模型
      ├── notification.py       # 通知模型
      ├── template.py           # 通知模板模型
      ├── channel.py            # 通知渠道模型
      └── preference.py         # 用户通知偏好模型
```

## 设计模式

每个模块均遵循以下设计模式：

1. **基础模型**：定义共享字段
   ```python
   class UserBase(SQLModel):
       username: str
       email: Optional[str] = None
   ```

2. **数据库模型**：继承基础模型，添加`table=True`标记
   ```python
   class User(UserBase, MixUUID, MixTime, table=True):
       __tablename__ = "users"
       # 数据库特有字段和关系
   ```

3. **API请求模型**：继承基础模型，添加请求特有字段
   ```python
   class UserCreate(UserBase):
       password: str
       confirm_password: str
   ```

4. **API响应模型**：继承基础模型，添加响应特有字段
   ```python
   class UserResponse(UserBase):
       id: UUID
       create_time: datetime
   ```

## 混合类 (Mixins)

项目使用了几个混合类来复用常见的字段：

- `MixUUID`: 提供UUID主键
- `MixTime`: 提供创建时间和更新时间
- `MixDelete`: 提供软删除功能

## 使用示例

### 创建新用户

```python
from app.models import User, UserCreate

# API请求验证
user_data = UserCreate(
    username="john_doe",
    email="<EMAIL>",
    password="password123",
    confirm_password="password123"
)

# 数据库模型
db_user = User(
    username=user_data.username,
    email=user_data.email,
    password_hash="hashed_password"
)
```

### 使用响应模型

```python
from app.models import UserResponse


@app.get("/users/{user_id}", response_model=UserResponse)
async def get_user(user_id: UUID):
    user = await User.get_by(id=user_id)
    return user  # 自动转换为UserResponse
```

## 注意事项

1. 避免循环导入问题，使用`ForwardRef`
2. 使用`__tablename__`和`__table_args__`指定表名和表选项
3. 每个模型的`__init__.py`负责导出该模块的所有模型
4. 顶层`models/__init__.py`负责统一导出所有模型

## 优势

1. **代码复用**：通过继承减少重复代码
2. **类型安全**：Pydantic提供类型验证
3. **一致性**：数据库模型和API模型保持一致
4. **IDE支持**：完整的类型提示和自动补全 