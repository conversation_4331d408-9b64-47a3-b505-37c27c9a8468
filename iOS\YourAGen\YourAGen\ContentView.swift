//
//  ContentView.swift
//  YourAGen
//
//  Created by john xiong on 2025/6/7.
//

import SwiftUI

struct ContentView: View {
    @State private var selectedTab = 0
    @EnvironmentObject var themeSettings: ThemeSettings
    @EnvironmentObject var languageManager: LanguageManager
    @EnvironmentObject var userData: UserData
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Playground页面 - 主要的AI聊天界面
            PlaygroundView()
                .tabItem {
                    Image(systemName: "brain.head.profile")
                    Text(languageManager.localized("playground"))
                }
                .tag(0)
            
            // 设置页面
            SettingsView()
                .tabItem {
                    Image(systemName: "gear")
                    Text(languageManager.localized("settings"))
                }
                .tag(1)
        }
        .preferredColorScheme(themeSettings.colorScheme)
        .tint(.blue)
    }
}

#Preview {
    ContentView()
        .environmentObject(ThemeSettings())
        .environmentObject(LanguageManager())
        .environmentObject(UserData())
}
