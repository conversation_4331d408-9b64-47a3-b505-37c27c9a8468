from datetime import datetime

import pandas as pd
import pendulum
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.core_consts import IntervalType
from app.db.db_orm_base import QueryMethods
from app.db.db_session import db_manager
from app.db.db_view import View
from app.models.crypto.model_symbol import Symbol


class MarketDataService:
    @staticmethod
    @db_manager
    async def get_kline_data_by_symbol_id(
            symbol_id: int,
            timeframe: IntervalType,
            start_time: datetime | str | None = None,
            end_time: datetime | str | None = None,
            limit: int | None = None,
            session: AsyncSession | None = None
    ):
        view_name = f"kline.v_kline_indicator_real_{timeframe}"
        view = View.get_view(view_name)
        if limit is not None:
            return await QueryMethods.filter(
                view,
                symbol_id=symbol_id,
                session=session,
                limit=limit
            )
        else:
            conditions = [view.c.symbol_id == symbol_id]
            if start_time is not None:
                conditions.append(view.c.timestamp >= pendulum.parse(start_time))
            if end_time is not None:
                conditions.append(view.c.timestamp <= pendulum.parse(end_time))
            return await QueryMethods.filter(
                view,
                where=tuple(conditions),
                session=session,
            )

    @db_manager
    async def get_kline_data_by_symbol(
            self,
            symbol: str,
            market_type: str,
            exchange: str,
            timeframe: IntervalType,
            start_time: datetime | str | None = None,
            end_time: datetime | str | None = None,
            limit: int | None = None,
            session: AsyncSession | None = None
    ) -> pd.DataFrame:
        symbol = await QueryMethods.get(
            Symbol,
            symbol=symbol,
            market_type=market_type,
            exchange=exchange,
            session=session
        )
        data = await self.get_kline_data_by_symbol_id(
            symbol_id=symbol.id,
            timeframe=timeframe,
            start_time=start_time,
            end_time=end_time,
            limit=limit,
            session=session
        )
        df = pd.DataFrame(data)
        return df

    @staticmethod
    @db_manager
    async def is_kline_data_available(
            symbol: str,
            market_type: str,
            exchange: str,
            session: AsyncSession | None = None
    ) -> bool:
        """验证指定交易对的行情数据可用性

        该方法通过查询数据库视图来检查指定交易对是否存在行情数据。

        参数:
            symbol (str): 交易对符号，如 BTCUSDT
            market_type (str): 市场类型，例如 'spot'、'usdt_future'、'coin_future'
            exchange (str): 交易所名称，如 binance
            session (AsyncSession | None): 可选的数据库会话对象

        返回:
            bool: 如果存在行情数据返回 True，否则返回 False
        """
        view = View.get_view('crypto.v_symbol_spot_volume_rank')
        result = await QueryMethods.filter(
            view,
            symbol=symbol,
            market_type=market_type,
            exchange=exchange,
            session=session
        )
        return len(result) > 0


if __name__ == '__main__':
    import asyncio

    asyncio.run(MarketDataService.is_kline_data_available('BTCUSDT', 'spot', 'binance'))
