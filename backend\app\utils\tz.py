"""
时区工具模块

提供各种时区相关的工具和常量
"""

import pendulum

# 定义上海时区对象
ShanghaiTimeZone = "Asia/Shanghai"


def get_shanghai_now():
    """
    获取上海当前时间
    
    Returns:
        pendulum.DateTime: 上海当前时间
    """
    return pendulum.now(ShanghaiTimeZone)


def to_shanghai_time(dt):
    """
    将时间转换为上海时间
    
    Args:
        dt: 要转换的时间对象
        
    Returns:
        pendulum.DateTime: 转换后的上海时间
    """
    return pendulum.instance(dt).in_timezone(ShanghaiTimeZone)
