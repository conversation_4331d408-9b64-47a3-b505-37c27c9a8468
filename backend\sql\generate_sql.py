timeframe_list = ['30m', '1h', '2h', '3h', '4h', '6h', '8h', '12h', '1d', '2d', '3d', '1w', '2w', '3w']


class CreateDDL:
    @staticmethod
    def create_kline_materialized_view():
        with open(f'07_create_kline_materialized_view.sql', 'w') as f:
            for timeframe in timeframe_list:
                if timeframe == '15m':
                    continue
                sql = f"""
CREATE MATERIALIZED VIEW IF NOT EXISTS kline.kline_{timeframe}
    WITH (timescaledb.continuous) AS
SELECT symbol_id,
       time_bucket('{timeframe}', timestamp) AS timestamp,
       FIRST(open, "timestamp")     AS open,
       MAX(high)                    AS high,
       MIN(low)                     AS low,
       LAST(close, "timestamp")     AS close,
       SUM(count)                   AS count,
       SUM(buy_volume)              AS buy_volume,
       SUM(volume)                  AS volume
FROM kline.kline
GROUP BY symbol_id, time_bucket('{timeframe}', timestamp)
WITH NO DATA;\n"""
                f.writelines(sql)

            f.writelines("\n")

            for timeframe in timeframe_list:
                sql = f"""CALL refresh_continuous_aggregate('kline.kline_{timeframe}', NULL, now() - INTERVAL '{timeframe}');\n"""
                f.writelines(sql)

            f.writelines("\n")

            for timeframe in timeframe_list:
                sql = f"""
SELECT remove_continuous_aggregate_policy('kline.kline_{timeframe}');
SELECT add_continuous_aggregate_policy('kline.kline_{timeframe}', NULL, INTERVAL '{timeframe}', INTERVAL '{timeframe}');\n"""
                f.writelines(sql)

    @staticmethod
    def create_v_kline_indicator_real_view():
        n = '\n'
        with open(f'09_create_v_kline_indicator_real_view.sql', 'w') as f:
            for timeframe in timeframe_list:
                sql = f"""
DROP VIEW IF EXISTS kline.v_kline_indicator_real_{timeframe} CASCADE;
CREATE VIEW kline.v_kline_indicator_real_{timeframe} AS
WITH k AS (
    SELECT *,
           FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
{''.join(["           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN %s - 1 PRECEDING AND CURRENT ROW))        AS ma_%s,%s" % (i, i, n) for i in range(10, 701, 10)])}
{''.join(["           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN %s - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_%s,%s" % (i, i, n) for i in range(10, 701, 10)])}
           (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
           (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
           (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
           (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
           (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
           (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
           (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
           (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
           (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
           (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
           (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
           (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
           (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
           (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
           RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
           RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
           RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
           CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
           CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
           CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

    FROM kline.kline_{timeframe}
    WHERE timestamp > now() - 801 * INTERVAL '{timeframe}'
    GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
        WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp DESC )
)
SELECT cs.exchange,
       cs.market_type,
       cs.symbol,
       k.*
FROM crypto.symbol cs
         JOIN k ON k.symbol_id = cs.id
ORDER BY timestamp DESC;\n"""
                f.writelines(sql)

    @staticmethod
    def create_v_kline_indicator_history_view():
        n = '\n'
        with open(f'08_create_kline_indicator_history_view.sql', 'w') as f:
            for timeframe in timeframe_list:
                sql = f"""
DROP VIEW IF EXISTS kline.v_kline_indicator_history_{timeframe} CASCADE;
CREATE VIEW kline.v_kline_indicator_history_{timeframe} AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
{''.join(["           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN %s - 1 PRECEDING AND CURRENT ROW))        AS ma_%s,%s" % (i, i, n) for i in range(10, 701, 10)])}
{''.join(["           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN %s - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_%s,%s" % (i, i, n) for i in range(10, 701, 10)])}
        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_{timeframe}
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;\n"""
                f.writelines(sql)


if __name__ == '__main__':
    CreateDDL.create_kline_materialized_view()
    CreateDDL.create_v_kline_indicator_real_view()
    CreateDDL.create_v_kline_indicator_history_view()
