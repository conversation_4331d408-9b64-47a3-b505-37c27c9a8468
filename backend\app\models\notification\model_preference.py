from typing import List, ForwardRef
from uuid import UUID

from sqlalchemy import Column, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixTime
from app.models.notification.model_notification_base import (
    UserPreferenceBase
)

# 避免循环导入
User = ForwardRef("User")


# 用户通知偏好创建模型
class UserPreferenceCreate(UserPreferenceBase):
    """用户通知偏好创建模型
    
    用于创建新的用户通知偏好
    """
    user_id: UUID


# 用户通知偏好更新模型
class UserPreferenceUpdate(SQLModel):
    """用户通知偏好更新模型
    
    用于更新用户通知偏好
    """
    is_enabled: bool | None = None


# 用户通知偏好批量更新模型
class UserPreferenceBatchUpdate(SQLModel):
    """用户通知偏好批量更新模型
    
    用于批量更新用户通知偏好
    """
    user_id: UUID
    preferences: List[dict]  # 包含notification_type, channel_type, is_enabled的字典列表


# 用户通知偏好数据库模型
class UserPreference(UserPreferenceBase, MixTime, table=True):
    """用户通知偏好表
    
    存储用户对不同类型通知的偏好设置
    """
    __tablename__ = "user_preferences"
    __table_args__ = (
        UniqueConstraint("user_id", "notification_type", "channel_type", name="uq_user_notification_channel"),
        {"schema": "notification"}
    )

    # 主键
    id: int | None = Field(default=None, primary_key=True, title="ID")

    # 关联字段
    user_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("auth.users.id"), nullable=False), title="用户ID")

    # 关系
    user: "User" = Relationship(back_populates="notification_preferences")


# 用户通知偏好响应模型
class UserPreferenceResponse(UserPreferenceBase):
    """用户通知偏好响应模型
    
    用于API响应返回用户通知偏好信息
    """
    id: int
    user_id: UUID

    class Config:
        from_attributes = True


# 用户通知偏好列表响应模型
class UserPreferenceListResponse(SQLModel):
    """用户通知偏好列表响应模型"""
    total_count: int
    data: List[UserPreferenceResponse]
