import SwiftUI

struct PlaygroundView: View {
    @StateObject private var playgroundStore = PlaygroundStore()
    @EnvironmentObject var userData: UserData
    @EnvironmentObject var languageManager: LanguageManager
    @EnvironmentObject var themeSettings: ThemeSettings
    
    @State private var inputText = ""
    @State private var showAgentSelector = false
    @State private var showSessionHistory = false
    @State private var showLoginSheet = false
    @State private var showRegisterSheet = false
    @State private var showUserProfile = false
    @FocusState private var isInputFocused: Bool
    
    var body: some View {
        NavigationView {
                VStack(spacing: 0) {
                    // 顶部工具栏
                    topToolbar
                    
                    // 聊天消息区域
                    chatArea
                    
                    // 底部输入区域
                    inputArea
                }
                .navigationBarHidden(true)
                .preferredColorScheme(themeSettings.colorScheme)
                .onAppear {
                    loadInitialData()
                }
                .simultaneousGesture(
                    // 优化的手势处理，只在需要时关闭键盘
                    TapGesture()
                        .onEnded { _ in
                            if isInputFocused {
                                isInputFocused = false
                            }
                        }
                )
        }
        .navigationViewStyle(StackNavigationViewStyle())
        .sheet(isPresented: $showAgentSelector) {
            AgentSelectorView(
                agents: playgroundStore.agents,
                selectedAgent: playgroundStore.selectedAgent,
                onAgentSelected: { agent in
                    if let token = userData.token {
                        playgroundStore.selectAgent(agent, token: token)
                    }
                    showAgentSelector = false
                    
                    // 选择智能体后重新加载会话
                    let token = userData.token ?? ""
                    playgroundStore.loadSessions(agentId: agent.agentId, token: token)
                    
                    // 清空当前消息
                    playgroundStore.clearMessages()
                }
            )
        }
        .sheet(isPresented: $showSessionHistory) {
            SessionHistoryView(
                sessions: playgroundStore.sessions,
                onSessionSelected: { session in
                    if let token = userData.token {
                        playgroundStore.selectSession(session, token: token)
                    }
                    showSessionHistory = false
                }
            )
            .environmentObject(playgroundStore)
        }
        .sheet(isPresented: $showLoginSheet) {
            LoginView(isPresented: $showLoginSheet)
        }
        .sheet(isPresented: $showRegisterSheet) {
            RegisterView(isPresented: $showRegisterSheet)
        }
        .sheet(isPresented: $showUserProfile) {
            UserProfileView(isPresented: $showUserProfile)
        }
    }
    
    private var loginPromptView: some View {
        VStack(spacing: 32) {
            Spacer()
            
            // 应用图标和标题
            VStack(spacing: 16) {
                Image(systemName: "brain.head.profile")
                    .font(.system(size: 100))
                    .foregroundColor(.blue.opacity(0.8))
                
                VStack(spacing: 8) {
                    Text("Your AI Agent")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("智能对话，无限可能")
                        .font(.title3)
                        .foregroundColor(.secondary)
                }
            }
            
            // 功能介绍
            VStack(spacing: 16) {
                FeatureRow(icon: "message.circle", title: "智能对话", description: "与多种AI助手进行自然对话")
                FeatureRow(icon: "brain", title: "专业助手", description: "代码专家、创意写手、数据分析师等")
                FeatureRow(icon: "clock.arrow.circlepath", title: "历史记录", description: "保存和管理您的对话历史")
            }
            .padding(.horizontal, 32)
            
            // 登录注册按钮
            VStack(spacing: 16) {
                Button(action: {
                    showLoginSheet = true
                }) {
                    Text(languageManager.localizedString("auth.login.actions.signIn"))
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.blue)
                        )
                }
                
                Button(action: {
                    showRegisterSheet = true
                }) {
                    Text(languageManager.localizedString("auth.register.actions.signUp"))
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.blue, lineWidth: 2)
                        )
                }
            }
            .padding(.horizontal, 32)
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
    
    private var topToolbar: some View {
        HStack {
            // 会话历史按钮（仅登录用户可用）
            if userData.isLoggedIn {
            Button(action: { showSessionHistory = true }) {
                Image(systemName: "clock.arrow.circlepath")
                    .font(.system(size: 20))
                    .foregroundColor(.primary)
                }
            } else {
                // 未登录时显示登录按钮
                Button(action: { showLoginSheet = true }) {
                    HStack(spacing: 4) {
                        Image(systemName: "person.circle")
                            .font(.system(size: 16))
                        Text("登录")
                            .font(.caption)
                    }
                    .foregroundColor(.blue)
                }
            }
            
            Spacer()
            
            // 当前选中的Agent信息
            if let selectedAgent = playgroundStore.selectedAgent {
                Button(action: { showAgentSelector = true }) {
                    HStack(spacing: 8) {
                        Image(systemName: selectedAgent.icon)
                            .font(.system(size: 16))
                        Text(selectedAgent.name)
                            .font(.headline)
                            .fontWeight(.medium)
                        Image(systemName: "chevron.down")
                            .font(.system(size: 12))
                    }
                    .foregroundColor(.primary)
                }
            } else {
                Button(action: { showAgentSelector = true }) {
                    HStack(spacing: 8) {
                        Image(systemName: "plus.circle")
                            .font(.system(size: 16))
                        Text(languageManager.localized("select_agent"))
                            .font(.headline)
                    }
                    .foregroundColor(.blue)
                }
            }
            
            Spacer()
            
            // 右侧按钮组
            HStack(spacing: 12) {
            // 新对话按钮
            Button(action: startNewChat) {
                Image(systemName: "plus.message")
                    .font(.system(size: 20))
                    .foregroundColor(.primary)
                }
                
                // 用户头像或登录状态
                if userData.isLoggedIn {
                    Button(action: { 
                        showUserProfile = true
                    }) {
                        Image(systemName: "person.crop.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.blue)
                    }
                }
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            Color(.systemBackground)
                .shadow(color: .black.opacity(0.05), radius: 1, x: 0, y: 1)
        )
    }
    
    private var chatArea: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 0) {
                    if playgroundStore.messages.isEmpty {
                        // 空状态
                        emptyStateView
                    } else {
                        // 消息列表
                        ForEach(playgroundStore.messages) { message in
                            MessageBubble(
                                message: message,
                                agentIcon: playgroundStore.selectedAgent?.icon
                            )
                            .id(message.id)
                        }
                    }
                }
                .padding(.bottom, 20)
            }
            .background(Color(.systemGroupedBackground))
            .onChange(of: playgroundStore.messages.count) { _ in
                // 自动滚动到最新消息
                if let lastMessage = playgroundStore.messages.last {
                    withAnimation(.easeOut(duration: 0.3)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
            .onChange(of: playgroundStore.lastMessage?.content) { _ in
                // 流式响应时也要滚动
                if let lastMessage = playgroundStore.messages.last {
                    withAnimation(.easeOut(duration: 0.1)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // 欢迎图标
            Image(systemName: "brain.head.profile")
                .font(.system(size: 80))
                .foregroundColor(.blue.opacity(0.6))
            
            VStack(spacing: 12) {
                Text("Your AI Agent")
                    .font(.title2)
                    .fontWeight(.bold)
                    .multilineTextAlignment(.center)
                
                if let selectedAgent = playgroundStore.selectedAgent {
                    Text("与 \(selectedAgent.name) 开始对话")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                } else {
                    Text("选择一个AI助手开始对话")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // 未登录提示
                if !userData.isLoggedIn {
                    VStack(spacing: 8) {
                        Text("💡 提示")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.orange)
                        
                        Text("登录后可保存对话历史和个人设置")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.orange.opacity(0.1))
                    )
                }
            }
            
            // 建议问题
            if playgroundStore.selectedAgent != nil {
                VStack(spacing: 12) {
                    Text("试试这些问题:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 8) {
                        ForEach(suggestionQuestions, id: \.self) { question in
                            Button(action: {
                                inputText = question
                                sendMessage()
                            }) {
                                Text(question)
                                    .font(.caption)
                                    .padding(.horizontal, 12)
                                    .padding(.vertical, 8)
                                    .background(
                                        RoundedRectangle(cornerRadius: 16)
                                            .fill(Color.blue.opacity(0.1))
                                    )
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                }
                .padding(.horizontal, 32)
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var inputArea: some View {
        OptimizedInputArea(
            inputText: $inputText,
            isInputFocused: Binding(
                get: { isInputFocused },
                set: { isInputFocused = $0 }
            ),
            canSendMessage: canSendMessage,
            isStreaming: playgroundStore.isStreaming,
            isLoading: playgroundStore.isLoading,
            errorMessage: playgroundStore.streamingErrorMessage ?? playgroundStore.errorMessage,
            feedbackMessage: playgroundStore.feedbackMessage,
            placeholder: languageManager.localized("message_placeholder"),
            onSend: sendMessage,
            onRetry: {
                if !inputText.isEmpty {
                    sendMessage()
                }
            }
        )
    }
    
    private var canSendMessage: Bool {
        return !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               playgroundStore.selectedAgent != nil &&
               !playgroundStore.isStreaming
    }
    
    private var suggestionQuestions: [String] {
        guard let agent = playgroundStore.selectedAgent else { return [] }
        
        switch agent.agentId {
        case "web_agent":
            return ["搜索最新新闻", "查找技术文档", "寻找学习资源", "搜索产品信息"]
        case "agno_assist":
            return ["如何使用Agno框架？", "创建一个Agent", "Agno最佳实践", "代码示例"]
        case "finance_agent":
            return ["分析股票走势", "查看财务指标", "投资建议", "市场分析"]
        case "crypto_prediction_agent":
            return ["比特币价格预测", "加密货币分析", "市场趋势", "投资策略"]
        default:
            return ["你好", "你能帮我做什么？", "介绍一下你的功能", "开始对话"]
        }
    }
    
    private func loadInitialData() {
        // 加载agents（未登录用户也可以使用）
        let token = userData.token ?? ""
        print("🚀 PlaygroundView 启动，开始加载数据...")
        print("📱 登录状态: \(userData.isLoggedIn ? "已登录" : "未登录")")
        print("🔑 Token: \(token.isEmpty ? "空" : "有效")")
        
        playgroundStore.loadAgents(token: token)
        
        // loadAgents 会自动加载会话，这里不需要重复调用
    }
    
    private func sendMessage() {
        guard canSendMessage || playgroundStore.isStreaming else { return }
        
        if playgroundStore.isStreaming {
            // 停止流式响应（这里需要实现停止功能）
            return
        }
        
        let message = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !message.isEmpty else { return }
        
        inputText = ""
        isInputFocused = false
        
        // 如果用户已登录，使用token；否则使用空token（匿名模式）
        let token = userData.token ?? ""
        playgroundStore.sendMessage(message, token: token)
    }
    
    private func startNewChat() {
        playgroundStore.startNewChat()
        inputText = ""
        isInputFocused = true
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.system(size: 24))
                .foregroundColor(.blue)
                .frame(width: 32, height: 32)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.medium)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    PlaygroundView()
        .environmentObject(UserData())
        .environmentObject(LanguageManager())
        .environmentObject(ThemeSettings())
} 