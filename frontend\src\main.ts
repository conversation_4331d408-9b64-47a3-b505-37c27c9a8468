import { createApp } from 'vue';
import { createHead } from '@vueuse/head';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import App from './App.vue';
import './assets/tailwind.css';
import { i18n } from '@/i18n';
import router from '@/router';

// 创建head实例用于管理SEO元数据
const head = createHead();

// 创建Pinia状态管理
const pinia = createPinia();

// 使用持久化插件
pinia.use(piniaPluginPersistedstate);

// 创建Vue应用
const app = createApp(App);

// 使用路由、i18n、head和pinia
app.use(router);
app.use(i18n);
app.use(head);
app.use(pinia);

// 挂载应用
app.mount('#app');
