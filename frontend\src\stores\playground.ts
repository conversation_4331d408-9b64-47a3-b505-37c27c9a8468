import { defineStore } from 'pinia';
import type { PlaygroundChatMessage, SessionEntry, Agent, ComboboxAgent } from '@/types/playground';

interface PlaygroundState {
  hydrated: boolean;
  streamingErrorMessage: string;
  isStreaming: boolean;
  isLoading: boolean;
  messages: PlaygroundChatMessage[];
  hasStorage: boolean;
  agents: ComboboxAgent[];
  selectedAgent: string;
  sessionsData: SessionEntry[] | null;
  isSessionsLoading: boolean;
  currentSessionId: string | null;
  isEndpointLoading: boolean;
  chatInputRef: HTMLTextAreaElement | null;
}

export const usePlaygroundStore = defineStore('playground', {
  state: (): PlaygroundState => ({
    hydrated: false,
    streamingErrorMessage: '',
    isStreaming: false,
    isLoading: false,
    messages: [],
    hasStorage: false,
    agents: [],
    selectedAgent: '',
    sessionsData: null,
    isSessionsLoading: false,
    currentSessionId: null,
    isEndpointLoading: false,
    chatInputRef: null,
  }),

  getters: {
    currentSession(): SessionEntry | null {
      if (this.currentSessionId && this.sessionsData) {
        return this.sessionsData.find(s => s.session_id === this.currentSessionId) || null;
      }
      return this.sessionsData?.[0] || null;
    },

    hasMessages(): boolean {
      return this.messages.length > 0;
    },

    selectedAgentInfo(): ComboboxAgent | null {
      return this.agents.find(a => a.value === this.selectedAgent) || null;
    },

    isReady(): boolean {
      return this.hydrated && !this.isLoading && this.agents.length > 0;
    },
  },

  actions: {
    setHydrated(hydrated: boolean) {
      this.hydrated = hydrated;
    },

    setStreamingErrorMessage(message: string) {
      this.streamingErrorMessage = message;
    },

    setIsStreaming(isStreaming: boolean) {
      this.isStreaming = isStreaming;
    },

    setIsLoading(isLoading: boolean) {
      this.isLoading = isLoading;
    },

    setMessages(messages: PlaygroundChatMessage[]) {
      this.messages = messages;
    },

    addMessage(message: PlaygroundChatMessage) {
      this.messages.push(message);
    },

    updateLastMessage(content: string) {
      if (this.messages.length > 0) {
        const lastMessage = this.messages[this.messages.length - 1];
        lastMessage.content = content;
      }
    },

    clearMessages() {
      this.messages = [];
    },

    setAgents(agents: ComboboxAgent[]) {
      this.agents = agents;
    },

    setHasStorage(hasStorage: boolean) {
      this.hasStorage = hasStorage;
    },

    setSelectedAgent(agentId: string) {
      this.selectedAgent = agentId;
    },

    setSessionsData(sessions: SessionEntry[]) {
      this.sessionsData = sessions;
    },

    setIsSessionsLoading(loading: boolean) {
      this.isSessionsLoading = loading;
    },

    setCurrentSessionId(sessionId: string | null) {
      this.currentSessionId = sessionId;
    },

    setIsEndpointLoading(loading: boolean) {
      this.isEndpointLoading = loading;
    },

    setChatInputRef(ref: HTMLTextAreaElement | null) {
      this.chatInputRef = ref;
    },

    reset() {
      this.messages = [];
      this.currentSessionId = null;
      this.sessionsData = null;
      this.streamingErrorMessage = '';
      this.isStreaming = false;
    },
  },

  persist: {
    key: 'playground-store',
    pick: ['selectedAgent', 'hasStorage'],
  },
});
