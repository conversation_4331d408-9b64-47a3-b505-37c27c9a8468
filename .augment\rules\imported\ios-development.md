---
type: "agent_requested"
---

# iOS开发规范 

## 项目结构
- 移动端应用 ([iOS/YourAGen/YourAGen/](mdc:iOS/YourAGen/YourAGen)):
  - `Models/`: 数据模型
    - [Agent.swift](mdc:iOS/YourAGen/YourAGen/Models/Agent.swift): AI代理模型
    - [User.swift](mdc:iOS/YourAGen/YourAGen/Models/User.swift): 用户模型
    - [Message.swift](mdc:iOS/YourAGen/YourAGen/Models/Message.swift): 消息模型
    - [Session.swift](mdc:iOS/YourAGen/YourAGen/Models/Session.swift): 会话模型
    - [ThemeSettings.swift](mdc:iOS/YourAGen/YourAGen/Models/ThemeSettings.swift): 主题设置
    - [LanguageManager.swift](mdc:iOS/YourAGen/YourAGen/Models/LanguageManager.swift): 语言管理
  - `Views/`: SwiftUI视图
    - [ContentView.swift](mdc:iOS/YourAGen/YourAGen/ContentView.swift): 主界面
    - [PlaygroundView.swift](mdc:iOS/YourAGen/YourAGen/Views/PlaygroundView.swift): 游乐场视图
    - [LoginView.swift](mdc:iOS/YourAGen/YourAGen/Views/LoginView.swift): 登录视图
    - [RegisterView.swift](mdc:iOS/YourAGen/YourAGen/Views/RegisterView.swift): 注册视图
    - [SettingsView.swift](mdc:iOS/YourAGen/YourAGen/Views/SettingsView.swift): 设置视图
    - [UserProfileView.swift](mdc:iOS/YourAGen/YourAGen/Views/UserProfileView.swift): 用户资料视图
    - [AgentSelectorView.swift](mdc:iOS/YourAGen/YourAGen/Views/AgentSelectorView.swift): 代理选择视图
    - [SessionHistoryView.swift](mdc:iOS/YourAGen/YourAGen/Views/SessionHistoryView.swift): 会话历史视图
    - [MessageBubble.swift](mdc:iOS/YourAGen/YourAGen/Views/MessageBubble.swift): 消息气泡组件
    - [MarkdownText.swift](mdc:iOS/YourAGen/YourAGen/Views/MarkdownText.swift): Markdown文本组件
  - `Services/`: 业务逻辑服务
    - [APIService.swift](mdc:iOS/YourAGen/YourAGen/Services/APIService.swift): API服务
    - [PlaygroundStore.swift](mdc:iOS/YourAGen/YourAGen/Services/PlaygroundStore.swift): 游乐场状态管理
  - [YourAGenApp.swift](mdc:iOS/YourAGen/YourAGen/YourAGenApp.swift): 应用入口点

## 开发最佳实践
- Swift 6:
  - 全面使用Swift 6新特性
  - 遵循官方API设计指南和严格并发模型
- SwiftUI:
  - 采用MVVM架构组织代码
  - 合理使用 `@State`, `@StateObject`, `@ObservableObject` 等属性包装器
- 设计规范:
  - 遵循Apple Human Interface Guidelines
  - 优先使用系统原生组件，支持深色/浅色模式
- 性能优化:
  - 使用 `LazyVStack` 和 `LazyHStack` 优化长列表性能
  - 避免不必要的视图重绘

## 编译与调试
- 核心要求:
  - 每次代码修改后必须在 `iPhone 16` 模拟器上编译 (⌘+B)
  - 必须修复所有编译错误和警告

## 依赖与组件管理
- 包管理器: 使用Swift Package Manager管理依赖
- 组件选择: 优先使用成熟、维护良好的开源组件

## 开发工具
- Xcode: 16.4
- 目标模拟器: `iPhone 16`
- 目标系统: iOS 18.5
