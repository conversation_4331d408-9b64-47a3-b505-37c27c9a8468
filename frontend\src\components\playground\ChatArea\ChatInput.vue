<template>
  <div class="relative mx-auto mb-1 flex w-full max-w-4xl items-end justify-center">
    <!-- 输入框容器 -->
    <div class="relative flex-1">
      <!-- 输入提示 -->
      <Transition name="input-hint" mode="out-in"> </Transition>

      <!-- 主输入框 - 使用naive-ui n-input + 自定义 Placeholder -->
      <div class="relative">
        <n-input
          ref="chatInputRef"
          v-model:value="inputMessage"
          type="textarea"
          placeholder=""
          :disabled="!selectedAgent || isStreaming"
          :autosize="{ minRows: 1, maxRows: 6 }"
          :class="['chat-input', { 'chat-input--streaming': isStreaming }]"
          @keydown="handleKeyDown"
          @focus="isFocused = true"
          @blur="isFocused = false"
        />

        <!-- 自定义 Placeholder -->
        <div
          v-if="!inputMessage && !isFocused"
          class="absolute left-4 top-3 text-sm text-gray-400 pointer-events-none select-none transition-opacity duration-200"
          style="line-height: 1.5; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
        >
          {{ $t('playground.chat.inputPlaceholder') }}
        </div>

        <!-- 发送按钮 - 集成在输入框内部 -->
        <div class="absolute bottom-3 right-3 flex items-center gap-2">
          <!-- 停止生成按钮 -->
          <Transition name="stop-button" mode="out-in">
            <button
              v-if="isStreaming"
              @click="handleStop"
              class="flex h-6 w-6 items-center justify-center rounded-md bg-orange-50 hover:bg-orange-100 dark:bg-orange-900/20 dark:hover:bg-orange-900/40 text-orange-600 dark:text-orange-400 transition-all duration-200 hover:scale-105 border border-orange-200/60 dark:border-orange-800/40"
            >
              <n-icon size="12" class="text-current">
                <StopOutline />
              </n-icon>
            </button>
          </Transition>

          <!-- 发送按钮 -->
          <button
            :disabled="!selectedAgent || !inputMessage.trim() || isStreaming || inputMessage.length > 2000"
            @click="handleSubmit"
            class="flex h-6 w-6 items-center justify-center rounded-md bg-emerald-50 hover:bg-emerald-100 disabled:bg-gray-50 dark:bg-emerald-900/20 dark:hover:bg-emerald-900/40 dark:disabled:bg-gray-800/50 text-emerald-600 dark:text-emerald-400 disabled:text-gray-400 dark:disabled:text-gray-500 transition-all duration-200 hover:scale-105 disabled:hover:scale-100 disabled:cursor-not-allowed border border-emerald-200/60 dark:border-emerald-800/40 disabled:border-gray-200/50 dark:disabled:border-gray-700/30"
          >
            <Transition name="send-icon" mode="out-in">
              <n-icon v-if="!isStreaming" size="12" key="send" class="text-current">
                <SendOutline />
              </n-icon>
              <div v-else key="loading" class="flex items-center justify-center">
                <div class="w-2.5 h-2.5 border border-current border-t-transparent rounded-full animate-spin"></div>
              </div>
            </Transition>
          </button>
        </div>
      </div>

      <!-- 输入框底部工具栏 -->
      <div class="absolute bottom-3 left-4 flex items-center justify-between" :class="{ 'right-18': isStreaming, 'right-14': !isStreaming }">
        <div class="flex items-center gap-2">
          <!-- 字符计数 -->
          <Transition mode="out-in" name="fade">
            <div v-if="inputMessage.length > 0" :class="{ 'text-red-400': inputMessage.length > 2000 }" class="text-xs text-gray-400 dark:text-gray-500">
              {{ inputMessage.length }}/2000
            </div>
          </Transition>
        </div>

        <!-- 快捷键提示 -->
        <div class="flex items-center gap-1.5 text-xs text-gray-700 dark:text-gray-200">
          <kbd
            class="px-1.5 py-0.5 bg-gray-800/70 dark:bg-gray-600/70 rounded text-xs border border-gray-500/40 dark:border-gray-500/40 shadow-sm text-gray-100 dark:text-gray-100 font-medium"
            >Enter</kbd
          >
          <span class="font-medium">{{ $t('playground.chat.sendLabel') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { NIcon, NInput, useMessage } from 'naive-ui';
import { SendOutline, StopOutline } from '@vicons/ionicons5';
import { useI18n } from 'vue-i18n';
import { usePlaygroundStore } from '@/stores/playground';
import { useStreamHandler } from '@/composables/useStreamHandler';

const playgroundStore = usePlaygroundStore();
const { handleStreamResponse, stopStreaming } = useStreamHandler();
const message = useMessage();
const { t } = useI18n();

const chatInputRef = ref<HTMLTextAreaElement>();
const inputMessage = ref('');
const isFocused = ref(false);

const selectedAgent = computed(() => playgroundStore.selectedAgent);
const isStreaming = computed(() => playgroundStore.isStreaming);

const handleSubmit = async () => {
  if (!inputMessage.value.trim() || inputMessage.value.length > 2000) return;

  const currentMessage = inputMessage.value.trim();
  inputMessage.value = '';

  try {
    await handleStreamResponse(currentMessage);
  } catch (error) {
    message.error(t('playground.chat.sendFailed', { error: error instanceof Error ? error.message : String(error) }));
  }
};

const handleStop = () => {
  try {
    stopStreaming();
    message.info(t('playground.chat.generationStopped'));
  } catch (error) {
    console.error('停止生成失败:', error);
  }
};

const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !e.shiftKey && !isStreaming.value && inputMessage.value.trim() && inputMessage.value.length <= 2000) {
    e.preventDefault();
    handleSubmit();
  }
};

onMounted(() => {
  if (chatInputRef.value) {
    playgroundStore.setChatInputRef(chatInputRef.value);
  }
});
</script>

<style scoped>
/* naive-ui 输入框基础样式 */
:deep(.n-input) {
  @apply rounded-2xl;
  min-height: 48px;
  backdrop-filter: blur(16px);
  padding: 12px 60px 32px 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  position: relative;
  transition: box-shadow 0.2s ease;

  /* 浅色模式 - 无边框设计 */
  border: none !important;
}

/* 思考状态下的输入框样式 */
:deep(.chat-input--streaming .n-input) {
  padding: 12px 75px 32px 18px;
}

:deep(.n-input:hover) {
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
}

:deep(.n-input.n-input--focus) {
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.08);
}

:deep(.n-input.n-input--disabled) {
  opacity: 0.7 !important;
  transform: none !important;
  cursor: not-allowed !important;
  position: relative;
}

:deep(.n-input.n-input--disabled .n-input__textarea-el) {
  cursor: not-allowed !important;
}

/* 深色模式简化样式 */
.dark :deep(.n-input) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dark :deep(.n-input:hover) {
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.2);
}

.dark :deep(.n-input.n-input--focus) {
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.08),
    0 4px 16px rgba(0, 0, 0, 0.25);
}

/* 文本域样式 */
:deep(.n-input__textarea-el) {
  font:
    14px/1.5 -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  resize: none;
  padding: 0;
  margin: 0;
  background: transparent;
  border: none;
  outline: none;
}

/* 隐藏原生placeholder */
:deep(.n-input__textarea-el::placeholder) {
  opacity: 0;
  color: transparent;
}

/* 简化动画过渡效果 */
.input-hint-enter-active,
.input-hint-leave-active,
.fade-enter-active,
.fade-leave-active,
.send-icon-enter-active,
.send-icon-leave-active {
  transition: opacity 0.2s ease;
}

.input-hint-enter-from,
.input-hint-leave-to,
.fade-enter-from,
.fade-leave-to,
.send-icon-enter-from,
.send-icon-leave-to {
  opacity: 0;
}

.stop-button-enter-active,
.stop-button-leave-active {
  transition: opacity 0.2s ease;
}

.stop-button-enter-from,
.stop-button-leave-to {
  opacity: 0;
}
</style>
