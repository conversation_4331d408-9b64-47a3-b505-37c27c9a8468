"""
币安API端点配置
"""


class BinanceEndpoints:
    """币安API端点配置"""

    # API基础URL
    BASE_URLS = {
        "spot": 'https://api.binance.com/api',
        "spot_data": 'https://data-api.binance.vision/api',
        "usdt_future": 'https://fapi.binance.com/fapi',
        "coin_future": 'https://dapi.binance.com/dapi'
    }

    # WebSocket URL
    WS_URLS = {
        "spot": "wss://stream.binance.com:9443/ws",
        "usdt_future": "wss://fstream.binance.com/ws",
        "coin_future": "wss://dstream.binance.com/ws"
    }

    # 所有端点配置
    ENDPOINTS = {
        "spot": {
            "market": {
                "ping": "/v3/ping",  # 测试连接
                "time": "/v3/time",  # 获取服务器时间
                "exchange_info": "/v3/exchangeInfo",  # 获取交易规则和交易对信息
                "depth": "/v3/depth",  # 获取订单簿
                "trades": "/v3/trades",  # 获取近期成交
                "historical_trades": "/v3/historicalTrades",  # 获取历史成交
                "agg_trades": "/v3/aggTrades",  # 获取归集成交
                "klines": "/v3/klines",  # 获取K线数据
                "ui_klines": "/v3/uiKlines",  # 获取UI K线数据
                "avg_price": "/v3/avgPrice",  # 获取当前平均价格
                "ticker": "/v3/ticker",  # 获取最新价格(所有交易对)
                "ticker_24hr": "/v3/ticker/24hr",  # 获取24hr价格变动情况
                "ticker_price": "/v3/ticker/price",  # 获取最新价格
                "ticker_book": "/v3/ticker/bookTicker"  # 获取最优挂单
            },
            "account": {
                "account": "/v3/account",  # 获取账户信息
                "my_trades": "/v3/myTrades",  # 获取账户成交历史
                "open_orders": "/v3/openOrders",  # 获取当前挂单
                "all_orders": "/v3/allOrders",  # 获取所有订单
                "order": "/v3/order",  # 查询订单
                "test_order": "/v3/order/test"  # 测试下单
            },
            "trade": {
                "order": "/v3/order",  # 下单
                "cancel_order": "/v3/order",  # 撤销订单
                "cancel_all_orders": "/v3/openOrders"  # 撤销所有订单
            }
        },
        "usdt_future": {
            "market": {
                "ping": "/v1/ping",  # 测试连接
                "time": "/v1/time",  # 获取服务器时间
                "exchange_info": "/v1/exchangeInfo",  # 获取交易规则和交易对信息
                "depth": "/v1/depth",  # 获取订单簿
                "trades": "/v1/trades",  # 获取近期成交
                "historical_trades": "/v1/historicalTrades",  # 获取历史成交
                "agg_trades": "/v1/aggTrades",  # 获取归集成交
                "klines": "/v1/klines",  # 获取K线数据
                "ticker_24hr": "/v1/ticker/24hr",  # 获取24hr价格变动情况
                "ticker_price": "/v1/ticker/price",  # 获取最新价格
                "ticker_book": "/v1/ticker/bookTicker",  # 获取最优挂单
                "mark_price": "/v1/premiumIndex",  # 获取标记价格
                "funding_rate": "/v1/fundingRate"  # 获取资金费率
            },
            "account": {
                "account": "/v2/account",  # 获取账户信息
                "balance": "/v2/balance",  # 获取余额
                "position_risk": "/v2/positionRisk",  # 获取持仓风险
                "my_trades": "/v1/userTrades",  # 获取账户成交历史
                "income": "/v1/income",  # 获取账户损益资金流水
                "leverage": "/v1/leverage",  # 调整开仓杠杆
                "open_orders": "/v1/openOrders",  # 获取当前挂单
                "all_orders": "/v1/allOrders",  # 获取所有订单
                "order": "/v1/order",  # 查询订单
                "position_mode": "/v1/positionSide/dual"  # 持仓模式
            },
            "trade": {
                "order": "/v1/order",  # 下单
                "batch_orders": "/v1/batchOrders",  # 批量下单
                "cancel_order": "/v1/order",  # 撤销订单
                "cancel_all_orders": "/v1/allOpenOrders",  # 撤销所有订单
                "cancel_batch_orders": "/v1/batchOrders"  # 批量撤销订单
            }
        },
        "coin_future": {
            "market": {
                "ping": "/v1/ping",  # 测试连接
                "time": "/v1/time",  # 获取服务器时间
                "exchange_info": "/v1/exchangeInfo",  # 获取交易规则和交易对信息
                "depth": "/v1/depth",  # 获取订单簿
                "trades": "/v1/trades",  # 获取近期成交
                "historical_trades": "/v1/historicalTrades",  # 获取历史成交
                "agg_trades": "/v1/aggTrades",  # 获取归集成交
                "klines": "/v1/klines",  # 获取K线数据
                "ticker_24hr": "/v1/ticker/24hr",  # 获取24hr价格变动情况
                "ticker_price": "/v1/ticker/price",  # 获取最新价格
                "ticker_book": "/v1/ticker/bookTicker",  # 获取最优挂单
                "mark_price": "/v1/premiumIndex",  # 获取标记价格
                "funding_rate": "/v1/fundingRate",  # 获取资金费率
                "index_price": "/v1/indexPrice"  # 获取指数价格
            },
            "account": {
                "account": "/v1/account",  # 获取账户信息
                "balance": "/v1/balance",  # 获取余额
                "position_risk": "/v1/positionRisk",  # 获取持仓风险
                "my_trades": "/v1/userTrades",  # 获取账户成交历史
                "income": "/v1/income",  # 获取账户损益资金流水
                "leverage": "/v1/leverage",  # 调整开仓杠杆
                "open_orders": "/v1/openOrders",  # 获取当前挂单
                "all_orders": "/v1/allOrders",  # 获取所有订单
                "order": "/v1/order",  # 查询订单
                "position_mode": "/v1/positionSide/dual"  # 持仓模式
            },
            "trade": {
                "order": "/v1/order",  # 下单
                "batch_orders": "/v1/batchOrders",  # 批量下单
                "cancel_order": "/v1/order",  # 撤销订单
                "cancel_all_orders": "/v1/allOpenOrders",  # 撤销所有订单
                "cancel_batch_orders": "/v1/batchOrders"  # 批量撤销订单
            }
        }
    }

    @classmethod
    def get_endpoint(cls, endpoint_type: str, name: str, market_type: str = "spot") -> str:
        """获取指定类型的端点URL
        
        Args:
            endpoint_type: 端点类型，可选值: 'market', 'account', 'trade'
            name: 端点名称
            market_type: 市场类型，可选值: 'spot', 'usdt_future', 'coin_future'
            
        Returns:
            端点完整URL
        """
        market_type = market_type.lower()
        if market_type not in cls.ENDPOINTS:
            raise ValueError(f"不支持的市场类型: {market_type}")

        if endpoint_type not in cls.ENDPOINTS[market_type]:
            raise ValueError(f"不支持的端点类型: {endpoint_type}")

        endpoint = cls.ENDPOINTS[market_type][endpoint_type].get(name)
        if not endpoint:
            raise ValueError(f"未知的{endpoint_type}端点: {name}")

        # 对于现货市场的公共数据接口，如果在支持列表中则使用数据API
        if market_type == "spot" and endpoint_type == "market":
            base_url = cls.BASE_URLS["spot_data"]
        else:
            base_url = cls.BASE_URLS[market_type]

        return f"{base_url}{endpoint}"

    @classmethod
    def get_market_endpoint(cls, name: str, market_type: str = "spot") -> str:
        """获取市场数据端点URL"""
        return cls.get_endpoint("market", name, market_type)

    @classmethod
    def get_account_endpoint(cls, name: str, market_type: str = "spot") -> str:
        """获取账户端点URL"""
        return cls.get_endpoint("account", name, market_type)

    @classmethod
    def get_trade_endpoint(cls, name: str, market_type: str = "spot") -> str:
        """获取交易端点URL"""
        return cls.get_endpoint("trade", name, market_type)

    @classmethod
    def get_ws_url(cls, market_type: str) -> str:
        """获取WebSocket URL"""
        market_type = market_type.lower()
        if market_type not in cls.WS_URLS:
            raise ValueError(f"不支持的市场类型: {market_type}")
        return cls.WS_URLS[market_type]
