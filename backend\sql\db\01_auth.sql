------------------------------------------
-- 用户认证表
-- 此模块处理用户账户管理、认证和身份验证相关功能
------------------------------------------
CREATE TABLE auth.users
(
    id                    UUID PRIMARY KEY                                                            DEFAULT uuid_generate_v4(),         -- 用户ID
    user_id               SMALLINT GENERATED BY DEFAULT AS IDENTITY (START WITH 1000) UNIQUE NOT NULL,                                    -- 用户的可读数字标识符
    username              VARCHAR(50) UNIQUE                                                 NOT NULL CHECK (LENGTH(TRIM(username)) > 0), -- 用户的唯一用户名
    email                 VARCHAR(255) UNIQUE CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
        ),                                                                                                                                -- 用户的电子邮件地址
    email_verified        BOOLEAN                                                            NOT NULL DEFAULT FALSE,                      -- 电子邮件是否已验证
    password_hash         VARCHAR(255),                                                                                                   -- 用户密码的加密哈希值
    password_salt         VARCHAR(64),                                                                                                    -- 用于密码加密的盐值
    password_last_changed TIMESTAMPTZ,                                                                                                    -- 密码最后修改时间
    phone_number          VARCHAR(20) CHECK (phone_number ~ '^\+?[0-9]{10,15}$'),                                                         -- 用户的电话号码
    phone_verified        BOOLEAN                                                            NOT NULL DEFAULT FALSE,                      -- 电话号码是否已验证
    display_name          VARCHAR(100),                                                                                                   -- 用户的显示名称
    full_name             VARCHAR(100),                                                                                                   -- 用户的全名
    avatar_url            VARCHAR(255),                                                                                                   -- 用户头像的URL
    bio                   TEXT,                                                                                                           -- 用户简介
    timezone              VARCHAR(50)                                                                 DEFAULT 'UTC',                      -- 用户的时区设置
    locale                VARCHAR(10)                                                                 DEFAULT 'en-US',                    -- 用户的语言和地区设置
    is_active             BOOLEAN                                                            NOT NULL DEFAULT TRUE,                       -- 用户账户是否活跃
    is_locked             BOOLEAN                                                            NOT NULL DEFAULT FALSE,                      -- 用户账户是否锁定
    is_system             BOOLEAN                                                            NOT NULL DEFAULT FALSE,                      -- 是否为系统用户
    is_anonymous          BOOLEAN                                                            NOT NULL DEFAULT FALSE,                      -- 是否为匿名用户
    account_status        VARCHAR(20)                                                        NOT NULL DEFAULT 'pending'                   -- 账户当前状态：待处理、活跃、暂停、锁定或已删除
        CHECK (account_status IN ('pending', 'active', 'suspended', 'locked', 'is_deleted')),
    status_reason         TEXT,                                                                                                           -- 账户状态变更原因
    mfa_enabled           BOOLEAN                                                            NOT NULL DEFAULT FALSE,                      -- 是否启用多因素认证
    mfa_secret            VARCHAR(255),                                                                                                   -- 多因素认证密钥
    recovery_codes        JSONB,                                                                                                          -- 账户恢复码
    last_login_at         TIMESTAMPTZ,                                                                                                    -- 最后登录时间
    login_attempts        SMALLINT                                                           NOT NULL DEFAULT 0,                          -- 登录尝试次数
    last_failed_login_at  TIMESTAMPTZ,                                                                                                    -- 最后一次失败登录时间
    invite_code           VARCHAR,                                                                                                        -- 邀请码
    preferences           JSONB                                                                       DEFAULT '{}'::jsonb,                -- 用户偏好设置
    remark                VARCHAR,                                                                                                        -- 用户备注
    create_time           TIMESTAMPTZ                                                        NOT NULL DEFAULT CURRENT_TIMESTAMP,          -- 创建时间
    update_time           TIMESTAMPTZ                                                        NOT NULL DEFAULT CURRENT_TIMESTAMP,          -- 更新时间
    is_deleted               BOOLEAN                                                            NOT NULL DEFAULT FALSE                       -- 是否已删除
) WITH (fillfactor = 90);

-- 用户表索引
CREATE INDEX idx_users_email ON auth.users (email) WHERE is_deleted = FALSE;
CREATE INDEX idx_users_username ON auth.users (username) WHERE is_deleted = FALSE;
CREATE INDEX idx_users_phone ON auth.users (phone_number) WHERE is_deleted = FALSE;
CREATE INDEX idx_users_status ON auth.users (is_active, is_locked, account_status) WHERE is_deleted = FALSE;
CREATE INDEX idx_users_login ON auth.users (last_login_at) WHERE is_deleted = FALSE;
CREATE INDEX idx_users_preferences_gin ON auth.users USING gin (preferences);

-- 用户状态历史表
-- 记录用户状态变更历史，提供审计追踪
CREATE TABLE auth.user_status_history
(
    id          SERIAL PRIMARY KEY,                            -- 状态历史记录ID
    user_id     UUID        NOT NULL,                          -- 用户ID
    status      VARCHAR(20) NOT NULL,                          -- 状态值
    reason      TEXT,                                          -- 状态变更原因
    actor_id    UUID,                                          -- 执行状态变更的操作者ID
    create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

CREATE INDEX idx_user_status_history_user ON auth.user_status_history (user_id);
CREATE INDEX idx_user_status_history_actor ON auth.user_status_history (actor_id);
CREATE INDEX idx_user_status_history_time ON auth.user_status_history (create_time);

-- 第三方认证提供者类型
CREATE TYPE auth.provider_type AS ENUM ( 'github', 'wechat', 'google', 'apple', 'microsoft', 'twitter', 'facebook', 'linkedin', 'gitlab', 'bitbucket' );

-- 用户第三方认证提供者表
-- 存储用户的第三方认证关联信息
CREATE TABLE auth.user_auth_providers
(
    id                   SERIAL PRIMARY KEY,                                    -- 主键ID
    user_id              UUID               NOT NULL,                           -- 关联的用户ID
    provider             auth.provider_type NOT NULL,                           -- 认证提供者类型
    provider_user_id     VARCHAR(255)       NOT NULL,                           -- 提供者平台上的用户ID
    access_token         TEXT,                                                  -- 访问令牌
    refresh_token        TEXT,                                                  -- 刷新令牌
    expires_at           TIMESTAMPTZ,                                           -- 令牌过期时间
    scopes               VARCHAR[],                                             -- 授权范围
    provider_email       VARCHAR(255),                                          -- 提供者平台上的邮箱
    provider_username    VARCHAR(100),                                          -- 提供者平台上的用户名
    provider_profile_url VARCHAR(255),                                          -- 提供者平台上的个人资料URL
    provider_avatar_url  VARCHAR(255),                                          -- 提供者平台上的头像URL
    provider_data        JSONB,                                                 -- 提供者返回的其他数据
    create_time          TIMESTAMPTZ        NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time          TIMESTAMPTZ        NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    is_deleted              BOOLEAN            NOT NULL DEFAULT FALSE,             -- 是否已删除
    UNIQUE (provider, provider_user_id)
);

CREATE INDEX idx_user_auth_user_id ON auth.user_auth_providers (user_id) WHERE is_deleted = FALSE;
CREATE INDEX idx_user_auth_provider ON auth.user_auth_providers (provider, provider_user_id) WHERE is_deleted = FALSE;

-- 密码历史表
-- 用于记录用户密码历史，支持密码重用策略
CREATE TABLE auth.password_history
(
    id            SERIAL PRIMARY KEY,                             -- 历史记录ID
    user_id       UUID         NOT NULL,                          -- 用户ID
    password_hash VARCHAR(255) NOT NULL,                          -- 密码哈希值
    create_time   TIMESTAMPTZ  NOT NULL DEFAULT CURRENT_TIMESTAMP -- 创建时间
);

CREATE INDEX idx_password_history_user ON auth.password_history (user_id);
CREATE INDEX idx_password_history_time ON auth.password_history (create_time);

-- 登录尝试记录表
-- 用于检测和防止暴力破解攻击
CREATE TABLE auth.login_attempts
(
    id            SERIAL PRIMARY KEY,                             -- 登录尝试ID
    ip_address    VARCHAR(45) NOT NULL,                           -- IP地址
    username      VARCHAR(255),                                   -- 尝试登录的用户名
    attempt_time  TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 尝试时间
    is_successful BOOLEAN     NOT NULL,                           -- 是否登录成功
    user_agent    TEXT,                                           -- 用户代理信息
    extra_data    JSONB                                           -- 额外数据
);

CREATE INDEX idx_login_attempts_ip ON auth.login_attempts (ip_address, attempt_time);
CREATE INDEX idx_login_attempts_username ON auth.login_attempts (username, attempt_time);
CREATE INDEX idx_login_attempts_time ON auth.login_attempts (attempt_time);

-- 验证令牌表
-- 用于处理电子邮件验证、手机验证、密码重置等功能
CREATE TABLE auth.verification_tokens
(
    id          SERIAL PRIMARY KEY,                                                                         -- 令牌ID
    user_id     UUID         NOT NULL,                                                                      -- 关联的用户ID
    token_type  VARCHAR(20)  NOT NULL CHECK (token_type IN ('email', 'phone', 'password_reset', 'invite')), -- 令牌类型
    token_hash  VARCHAR(255) NOT NULL,                                                                      -- 令牌哈希值
    token_value VARCHAR(64),                                                                                -- 令牌明文值
    expires_at  TIMESTAMPTZ  NOT NULL,                                                                      -- 过期时间
    is_used     BOOLEAN      NOT NULL DEFAULT FALSE,                                                        -- 是否已使用
    used_at     TIMESTAMPTZ,                                                                                -- 使用时间
    extra_data  JSONB,                                                                                      -- 额外数据
    create_time TIMESTAMPTZ  NOT NULL DEFAULT CURRENT_TIMESTAMP                                             -- 创建时间
);

CREATE INDEX idx_verification_tokens_user ON auth.verification_tokens (user_id, token_type);
CREATE INDEX idx_verification_tokens_hash ON auth.verification_tokens (token_hash);
CREATE INDEX idx_verification_tokens_expires ON auth.verification_tokens (expires_at) WHERE is_used = FALSE;



-- 自动更新时间戳触发器
-- 创建触发器以自动更新记录的时间戳
CREATE TRIGGER update_users_timestamp
    BEFORE UPDATE
    ON auth.users
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();

CREATE TRIGGER update_user_auth_providers_timestamp
    BEFORE UPDATE
    ON auth.user_auth_providers
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();


INSERT INTO auth.users (username, email, password_hash, password_salt, phone_number, display_name, full_name, timezone, locale)
VALUES ('yoursxiong',
        '<EMAIL>',
        '$2b$12$I6ZZ62t2gVh92ivrGhcCHuHs5JmROdWUbWd/.koUwAj7yP3AmzJvm',
        '261f62dd60b0e660a7a91681959fe91d82612f352dbdd27b67bc74f70a2de05a',
        '17805106808',
        '你家的熊',
        'john xiong',
        'UTC+8',
        'zh-CN');
