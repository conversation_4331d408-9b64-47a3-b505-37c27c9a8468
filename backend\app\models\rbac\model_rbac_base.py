from sqlmodel import Field, SQLModel


# 角色基础模型
class RoleBase(SQLModel):
    """角色基础信息模型
    
    包含角色的基本信息
    """
    name: str = Field(..., min_length=2, max_length=50, title="角色名称")
    description: str | None = Field(None, title="角色描述")
    code: str = Field(..., min_length=2, max_length=50, title="角色代码")


# 权限基础模型
class PermissionBase(SQLModel):
    """权限基础信息模型
    
    包含权限的基本信息
    """
    name: str = Field(..., min_length=2, max_length=50, title="权限名称")
    description: str | None = Field(None, title="权限描述")
    code: str = Field(..., min_length=2, max_length=50, title="权限代码")
    resource: str = Field(..., title="资源")
    action: str = Field(..., title="操作")


# 用户角色基础模型
class UserRoleBase(SQLModel):
    """用户角色基础信息模型
    
    包含用户角色的基本信息
    """
    pass


# 角色权限基础模型
class RolePermissionBase(SQLModel):
    """角色权限基础信息模型
    
    包含角色权限的基本信息
    """
    pass
