
DROP VIEW IF EXISTS kline.v_kline_indicator_history_30m CASCADE;
CREATE VIEW kline.v_kline_indicator_history_30m AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_30m
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_1h CASCADE;
CREATE VIEW kline.v_kline_indicator_history_1h AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_1h
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_2h CASCADE;
CREATE VIEW kline.v_kline_indicator_history_2h AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_2h
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_3h CASCADE;
CREATE VIEW kline.v_kline_indicator_history_3h AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_3h
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_4h CASCADE;
CREATE VIEW kline.v_kline_indicator_history_4h AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_4h
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_6h CASCADE;
CREATE VIEW kline.v_kline_indicator_history_6h AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_6h
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_8h CASCADE;
CREATE VIEW kline.v_kline_indicator_history_8h AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_8h
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_12h CASCADE;
CREATE VIEW kline.v_kline_indicator_history_12h AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_12h
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_1d CASCADE;
CREATE VIEW kline.v_kline_indicator_history_1d AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_1d
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_2d CASCADE;
CREATE VIEW kline.v_kline_indicator_history_2d AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_2d
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_3d CASCADE;
CREATE VIEW kline.v_kline_indicator_history_3d AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_3d
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_1w CASCADE;
CREATE VIEW kline.v_kline_indicator_history_1w AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_1w
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_2w CASCADE;
CREATE VIEW kline.v_kline_indicator_history_2w AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_2w
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;

DROP VIEW IF EXISTS kline.v_kline_indicator_history_3w CASCADE;
CREATE VIEW kline.v_kline_indicator_history_3w AS
SELECT *,
        FIRST_VALUE(close) OVER w                                                                                                                AS last_price,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW))        AS ma_10,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW))        AS ma_20,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW))        AS ma_30,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW))        AS ma_40,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW))        AS ma_50,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW))        AS ma_60,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW))        AS ma_70,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW))        AS ma_80,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW))        AS ma_90,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW))        AS ma_100,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW))        AS ma_110,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW))        AS ma_120,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW))        AS ma_130,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW))        AS ma_140,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW))        AS ma_150,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW))        AS ma_160,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW))        AS ma_170,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW))        AS ma_180,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW))        AS ma_190,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW))        AS ma_200,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW))        AS ma_210,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW))        AS ma_220,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW))        AS ma_230,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW))        AS ma_240,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW))        AS ma_250,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW))        AS ma_260,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW))        AS ma_270,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW))        AS ma_280,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW))        AS ma_290,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW))        AS ma_300,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW))        AS ma_310,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW))        AS ma_320,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW))        AS ma_330,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW))        AS ma_340,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW))        AS ma_350,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW))        AS ma_360,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW))        AS ma_370,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW))        AS ma_380,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW))        AS ma_390,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW))        AS ma_400,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW))        AS ma_410,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW))        AS ma_420,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW))        AS ma_430,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW))        AS ma_440,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW))        AS ma_450,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW))        AS ma_460,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW))        AS ma_470,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW))        AS ma_480,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW))        AS ma_490,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW))        AS ma_500,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW))        AS ma_510,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW))        AS ma_520,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW))        AS ma_530,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW))        AS ma_540,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW))        AS ma_550,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW))        AS ma_560,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW))        AS ma_570,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW))        AS ma_580,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW))        AS ma_590,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW))        AS ma_600,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW))        AS ma_610,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW))        AS ma_620,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW))        AS ma_630,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW))        AS ma_640,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW))        AS ma_650,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW))        AS ma_660,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW))        AS ma_670,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW))        AS ma_680,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW))        AS ma_690,
           AVERAGE(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW))        AS ma_700,

           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 10 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_10,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 20 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_20,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 30 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_30,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 40 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_40,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 50 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_50,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 60 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_60,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 70 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_70,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 80 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_80,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 90 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_90,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 100 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_100,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 110 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_110,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 120 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_120,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 130 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_130,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 140 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_140,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 150 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_150,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 160 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_160,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 170 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_170,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 180 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_180,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 190 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_190,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 200 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_200,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 210 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_210,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 220 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_220,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 230 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_230,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 240 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_240,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 250 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_250,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 260 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_260,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 270 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_270,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 280 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_280,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 290 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_290,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 300 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_300,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 310 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_310,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 320 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_320,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 330 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_330,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 340 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_340,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 350 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_350,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 360 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_360,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 370 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_370,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 380 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_380,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 390 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_390,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 400 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_400,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 410 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_410,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 420 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_420,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 430 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_430,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 440 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_440,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 450 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_450,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 460 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_460,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 470 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_470,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 480 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_480,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 490 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_490,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 500 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_500,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 510 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_510,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 520 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_520,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 530 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_530,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 540 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_540,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 550 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_550,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 560 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_560,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 570 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_570,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 580 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_580,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 590 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_590,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 600 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_600,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 610 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_610,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 620 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_620,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 630 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_630,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 640 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_640,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 650 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_650,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 660 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_660,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 670 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_670,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 680 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_680,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 690 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_690,
           STDDEV(ROLLING(STATS_AGG(close)) OVER (PARTITION BY symbol_id ORDER BY timestamp ROWS BETWEEN 700 - 1 PRECEDING AND CURRENT ROW), 'pop')   AS std_700,

        (close - LAG(close, -1, close) OVER w) / LAG(close, -1, close) OVER w                                                                    AS change_1,
        (close - LAG(close, -2, close) OVER w) / LAG(close, -2, close) OVER w                                                                    AS change_2,
        (close - LAG(close, -5, close) OVER w) / LAG(close, -5, close) OVER w                                                                    AS change_5,
        (close - LAG(close, -7, close) OVER w) / LAG(close, -7, close) OVER w                                                                    AS change_7,
        (close - LAG(close, -10, close) OVER w) / LAG(close, -10, close) OVER w                                                                  AS change_10,
        (close - LAG(close, -30, close) OVER w) / LAG(close, -30, close) OVER w                                                                  AS change_30,
        (close - LAG(close, -60, close) OVER w) / LAG(close, -60, close) OVER w                                                                  AS change_60,
        (close - LAG(close, -90, close) OVER w) / LAG(close, -90, close) OVER w                                                                  AS change_90,
        (close - LAG(close, -100, close) OVER w) / LAG(close, -100, close) OVER w                                                                AS change_100,
        (close - LAG(close, -120, close) OVER w) / LAG(close, -120, close) OVER w                                                                AS change_120,
        (close - LAG(close, -150, close) OVER w) / LAG(close, -150, close) OVER w                                                                AS change_150,
        (close - LAG(close, -200, close) OVER w) / LAG(close, -200, close) OVER w                                                                AS change_200,
        (close - LAG(close, -300, close) OVER w) / LAG(close, -300, close) OVER w                                                                AS change_300,
        (close - LAG(close, -365, close) OVER w) / LAG(close, -365, close) OVER w                                                                AS change_365,
        RANK() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                                 AS count_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                            AS buy_volume_rank,
        RANK() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                                AS volume_rank,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY count DESC)                                                                            AS count_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY buy_volume DESC)                                                                       AS buy_volume_rank_percent,
        CUME_DIST() OVER (PARTITION BY symbol_id ORDER BY volume DESC)                                                                           AS volume_rank_percent

FROM kline.kline_3w
GROUP BY timestamp, symbol_id, open, high, low, close, count, buy_volume, volume
    WINDOW w AS (PARTITION BY symbol_id ORDER BY timestamp ASC)
ORDER BY timestamp ASC;
