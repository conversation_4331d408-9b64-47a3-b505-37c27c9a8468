# 构建阶段
FROM node:22-alpine3.20 AS build-stage

# 设置工作目录
WORKDIR /app

# 设置生产环境变量
ENV NODE_ENV=production

COPY package.json yarn.lock ./

RUN yarn install --frozen-lockfile --production=false && yarn cache clean

COPY . .

RUN yarn build

FROM nginx:stable-alpine3.20-slim AS production-stage

# 安装 envsubst 用于替换环境变量
RUN apk add --no-cache gettext

# 复制构建结果到 Nginx 目录，使用正确的nginx用户
COPY --from=build-stage --chown=nginx:nginx /app/dist /usr/share/nginx/html

# 复制 nginx 配置模板
COPY nginx.conf /etc/nginx/templates/default.conf.template

EXPOSE ${FRONTEND_PORT}

# 使用非root用户运行nginx以提高安全性
# USER nginx

# 启动命令：替换环境变量并启动nginx
CMD ["/bin/sh", "-c", "envsubst '$$FRONTEND_PORT $$BACKEND_HOST $$BACKEND_PORT $$NGINX_WORKER_PROCESSES' < /etc/nginx/templates/default.conf.template > /tmp/nginx.conf && nginx -c /tmp/nginx.conf -g 'daemon off;'"]