"""认证相关模型"""

# 基础模型
from app.models.auth.model_auth_base import (
    UserStatusBase,
    PasswordHistoryBase, AuthProviderBase, LoginHistoryBase
)
# 身份认证提供者模型
from app.models.auth.model_auth_provider import (
    AuthProviderType, ExternalAuthProvider, AuthProviderRegister,
    UserAuthProvider, OAuth2AuthRequest, OAuth2CallbackResponse
)
# 登录模型
from app.models.auth.model_login import (
    LoginRequest, RefreshTokenRequest, LoginResponse,
    LoginHistory, LoginAttempt
)
# 密码模型
from app.models.auth.model_password import (
    PasswordChangeRequest, PasswordResetRequest, PasswordResetConfirm,
    PasswordHistory, PasswordPolicy, PasswordValidationResult
)
# 令牌模型
from app.models.auth.model_token import (
    TokenVerify, VerificationToken, TokenResponse,
    TokenValidationResponse, UserStatusUpdate, UserStatusHistory,
    StatusHistoryResponse, TokenPayload, Token, TokenRefresh
)
# 用户模型
from app.models.auth.model_user import (
    User, UserCreate, UserUpdate, UserResponse,
    UserPasswordUpdate
)

__all__ = [
    'User', 'UserCreate', 'UserUpdate', 'UserResponse', 'UserPasswordUpdate',
    'VerificationToken', 'PasswordHistory', 'UserAuthProvider',
    'AuthProviderType', 'LoginRequest', 'LoginResponse',
    'UserStatusHistory', 'Token', 'TokenRefresh'
]
