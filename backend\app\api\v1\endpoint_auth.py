from datetime import timed<PERSON><PERSON>

import pendulum
from email_validator import validate_email
from fastapi import APIRouter, Response
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from loguru import logger
from pydantic import ValidationError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.core_auth_deps import get_current_user, get_current_active_user
from app.core.core_exceptions import AuthenticationException, BusinessException
from app.core.core_jwt import create_tokens, refresh_tokens
from app.core.core_security import get_password_hash, verify_password
from app.db.db_session import get_async_db
from app.models.auth import User, Token, TokenRefresh, UserCreate, PasswordChangeRequest

router = APIRouter(prefix="/auth", tags=["认证"])


@router.post("/register")
async def register(user_data: UserCreate, db: AsyncSession = get_async_db) -> Token:
    """注册新用户"""
    user = await User.get_by(email=user_data.email, session=db)
    if user:
        raise BusinessException("邮箱已被注册")
    else:
        # 当日限制注册数量
        count = await User.count_by(where=User.create_time > pendulum.now().date(), session=db)
        if count > 10:
            raise BusinessException("系统禁止注册")
        # 创建用户数据
        password_hash, password_salt = get_password_hash(user_data.password)
        user_data = user_data.model_dump(exclude={"password"})
        user_data["password_hash"] = password_hash
        user_data["password_salt"] = password_salt
        user_data["password_last_changed"] = pendulum.now()
        user_data["last_login_at"] = pendulum.now()
        # 创建用户
        user = User(**user_data)
        await user.save(commit=True, refresh=True, session=db)
        # 设置访问令牌和刷新令牌过期时间
        access_token_expires = timedelta(days=7)  # 7天
        refresh_token_expires = timedelta(days=7)  # 7天
        tokens = await create_tokens(user.id, access_token_expires=access_token_expires, refresh_token_expires=refresh_token_expires)
        return tokens


@router.put("/password")
async def change_current_user_password(password_in: PasswordChangeRequest, db: AsyncSession = get_async_db, current_user: User = Depends(get_current_active_user)) -> dict:
    if not verify_password(password_in.current_password, current_user.password_hash, current_user.password_salt):
        raise AuthenticationException("用户名或密码错误")
    password_hash, password_salt = get_password_hash(password_in.new_password)
    current_user.password_hash = password_hash
    current_user.password_salt = password_salt
    # 保存到数据库
    await current_user.merge(db, commit=True)
    return {"message": "密码修改成功"}


@router.post("/login")
async def login(
        form_data: OAuth2PasswordRequestForm = Depends(),
        db: AsyncSession = get_async_db,
) -> Token:
    email = form_data.username
    # 验证邮箱格式
    try:
        validate_email(email, check_deliverability=False)
    except ValidationError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="请使用有效的邮箱地址登录"
        )
    user = await User.get_by(email=email, is_deleted=False, session=db)
    if not user:
        # 用户不存在
        logger.info(f"用户登录失败：{email}，用户不存在")
        raise AuthenticationException("用户名或密码错误")

    if not verify_password(form_data.password, user.password_hash, user.password_salt):
        # 密码错误
        logger.info(f"用户登录失败：{email}，密码错误")
        raise AuthenticationException("用户名或密码错误")

    if not user.is_active:
        # 用户被禁用
        raise AuthenticationException("用户已被禁用")
    # 设置访问令牌和刷新令牌过期时间
    access_token_expires = timedelta(days=7)  # 7天
    refresh_token_expires = timedelta(days=7)  # 7天
    tokens = await create_tokens(user.id, access_token_expires=access_token_expires, refresh_token_expires=refresh_token_expires)
    # 更新登录信息
    user.last_login_at = pendulum.now()
    user.login_attempts = 0
    logger.info(f"用户登录成功：{email}")
    return tokens


@router.post("/refresh")
async def refresh_token(
        refresh_data: TokenRefresh,
        db: AsyncSession = get_async_db,
) -> Token:
    try:
        # 刷新令牌
        tokens = await refresh_tokens(db, refresh_data.refresh_token)

        return tokens

    except AuthenticationException as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/logout")
async def logout(response: Response, current_user: User = Depends(get_current_user)):
    """用户登出"""
    # 在实际实现中，可能需要将token添加到黑名单
    # 清除客户端cookie
    response.delete_cookie(key="access_token")
    response.delete_cookie(key="refresh_token")

    return {"detail": "登出成功"}
