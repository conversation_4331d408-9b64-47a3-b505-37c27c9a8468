# 前端开发环境代理配置说明

## 代理配置简介

在前端开发过程中，我们经常需要解决跨域问题。Vite提供了开发服务器代理功能，使我们能够在本地开发时将API请求转发到实际的后端服务器。

## 配置步骤

### 1. 创建环境变量文件

在项目根目录下创建 `.env.development` 文件，内容如下：

```
# 开发环境配置
NODE_ENV=development

# 应用标题
VITE_APP_TITLE=我的Vue应用（开发环境）

# API基础地址 - 前端代码中使用这个地址
VITE_API_URL=/api

# 实际API服务地址 - 用于代理配置，指向真实后端服务
VITE_API_TARGET=http://localhost:3000

# 是否开启调试工具
VITE_ENABLE_DEVTOOLS=true
```

### 2. 配置Vite代理

我们已经在 `vite.config.ts` 中配置了代理，代码如下：

```typescript
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  
  return {
    // ...其他配置
    server: {
      // ...服务器配置
      proxy: {
        '/api': {
          target: env.VITE_API_TARGET || 'http://localhost:3000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    // ...其他配置
  }
})
```

### 3. 在前端代码中使用

在 `request.ts` 和其他API请求中，使用环境变量中的API基础地址：

```typescript
const service = axios.create({
  baseURL: import.meta.env.VITE_API_URL, // 使用环境变量中的API地址
  timeout: 10000,
  // ...其他配置
})
```

## 工作原理

1. 前端代码中发起请求到 `/api/some-endpoint`
2. Vite开发服务器将该请求代理到 `http://localhost:3000/some-endpoint`
3. 后端服务器返回数据到Vite开发服务器
4. Vite开发服务器将数据返回给前端应用

## 多代理配置示例

如果需要配置多个代理目标，可以在 `vite.config.ts` 中添加多个代理规则：

```typescript
proxy: {
  '/api': {
    target: env.VITE_API_TARGET || 'http://localhost:3000',
    changeOrigin: true,
    rewrite: (path) => path.replace(/^\/api/, '')
  },
  '/auth': {
    target: 'http://auth-service:4000',
    changeOrigin: true
  },
  '/uploads': {
    target: 'http://file-service:5000',
    changeOrigin: true
  }
}
```

## 注意事项

1. `.env.development` 文件不应该提交到版本控制系统中，因为它可能包含敏感信息
2. 在生产环境中，应该使用真实的API地址，而不是相对路径
3. `changeOrigin: true` 是跨域请求所必需的 