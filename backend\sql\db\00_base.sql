-- 基础数据库设置
-- 启用高级扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp"; -- 提供UUID生成功能
CREATE EXTENSION IF NOT EXISTS "pgcrypto"; -- 提供加密功能

CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- 提供B树索引的GIN支持
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements"; -- 提供SQL语句统计功能

-- 创建组织架构
CREATE SCHEMA IF NOT EXISTS auth; -- 认证和用户管理模块
CREATE SCHEMA IF NOT EXISTS audit; -- 审计日志模块
CREATE SCHEMA IF NOT EXISTS notification; -- 通知系统模块
CREATE SCHEMA IF NOT EXISTS rbac; -- 基于角色的访问控制模块

-- 创建公共工具模式
CREATE SCHEMA IF NOT EXISTS utils;

-- 创建timestamp更新函数
CREATE OR REPLACE FUNCTION utils.update_timestamp()
    RETURNS TRIGGER AS
$$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
