from typing import Dict, List, ForwardRef
from uuid import UUID

from sqlalchemy import Column, String, Boolean, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixUUID, MixTime
from app.models.notification.model_notification_base import NotificationTemplateBase

# 避免循环导入
Notification = ForwardRef("Notification")


# 通知模板创建模型
class NotificationTemplateCreate(NotificationTemplateBase):
    """通知模板创建模型
    
    用于创建新的通知模板
    """
    description: str | None = None
    parameters: Dict | None = None


# 通知模板更新模型
class NotificationTemplateUpdate(SQLModel):
    """通知模板更新模型
    
    用于更新通知模板
    """
    name: str | None = None
    description: str | None = None
    subject_template: str | None = None
    content_template: str | None = None
    parameters: Dict | None = None
    is_active: bool | None = None


# 通知模板数据库模型
class NotificationTemplate(NotificationTemplateBase, MixUUID, MixTime, table=True):
    """通知模板表
    
    存储预定义的通知模板
    """
    __tablename__ = "notification_templates"
    __table_args__ = (
        UniqueConstraint("code", name="uq_template_code"),
        {"schema": "notification"}
    )

    # 扩展字段
    description: str | None = Field(None, sa_column=Column(String(255)), title="模板描述")
    parameters: Dict | None = Field(None, sa_column=Column(JSONB), title="模板参数")
    is_active: bool = Field(default=True, sa_column=Column(Boolean, nullable=False, default=True), title="是否启用")
    is_system: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="是否系统模板")

    # 关系
    notifications: List["Notification"] = Relationship(back_populates="template")


# 通知模板响应模型
class NotificationTemplateResponse(NotificationTemplateBase):
    """通知模板响应模型
    
    用于API响应返回通知模板信息
    """
    id: UUID
    description: str | None = None
    parameters: Dict | None = None
    is_active: bool
    is_system: bool
    create_time: str

    class Config:
        from_attributes = True


# 通知模板列表响应模型
class NotificationTemplateListResponse(SQLModel):
    """通知模板列表响应模型"""
    total_count: int
    data: List[NotificationTemplateResponse]
