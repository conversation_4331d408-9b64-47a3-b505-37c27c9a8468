"""通知系统相关模型"""

# 通知渠道模型
from app.models.notification.model_channel import (
    NotificationChannel, NotificationChannelConfig, NotificationChannelCreate, NotificationChannelUpdate,
    NotificationChannelResponse, NotificationChannelListResponse
)
# 通知模型
from app.models.notification.model_notification import (
    Notification, NotificationCreate, NotificationUpdate, NotificationResponse,
    NotificationListResponse, NotificationBatchCreate
)
# 基础模型和枚举
from app.models.notification.model_notification_base import (
    NotificationStatus, NotificationType, NotificationPriority, NotificationChannel as ChannelType,
    NotificationBase, NotificationTemplateBase, NotificationChannelBase, UserPreferenceBase
)
# 用户通知偏好模型
from app.models.notification.model_preference import (
    UserPreference, UserPreferenceCreate, UserPreferenceUpdate,
    UserPreferenceResponse, UserPreferenceListResponse
)
# 通知模板模型
from app.models.notification.model_template import (
    NotificationTemplate, NotificationTemplateCreate, NotificationTemplateUpdate,
    NotificationTemplateResponse, NotificationTemplateListResponse
)

__all__ = [
    'NotificationStatus',
    'NotificationType',
    'NotificationPriority',
    'ChannelType',
    'Notification',
    'NotificationCreate',
    'NotificationResponse',
    'NotificationTemplate',
    'NotificationChannelConfig',
    'NotificationChannel',
    'UserPreference'
]
