from datetime import datetime
from typing import Dict, ForwardRef, Optional
from uuid import UUID

from sqlalchemy import Column, DateTime, String, Boolean, ForeignKey, Text
from sqlalchemy.dialects.postgresql import INET, JSONB, UUID as PGUUID
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixTime
from app.models.auth.model_auth_base import LoginHistoryBase

# 避免循环导入
User = ForwardRef("User")


# 登录请求模型
class LoginRequest(SQLModel):
    """登录请求模型
    
    用于API登录请求
    """
    username: str = Field(..., title="用户名")
    password: str = Field(..., title="密码")
    remember_me: bool = Field(default=False, title="记住我")


# 会话刷新请求模型
class RefreshTokenRequest(SQLModel):
    """会话刷新请求模型
    
    用于刷新访问令牌
    """
    refresh_token: str = Field(..., title="刷新令牌")


# 登录响应模型
class LoginResponse(SQLModel):
    """登录响应模型
    
    用于API登录响应
    """
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user_info: Dict


# 登录历史数据库模型
class LoginHistory(LoginHistoryBase, MixTime, table=True):
    """登录历史表
    
    记录用户登录历史
    """
    __tablename__ = "login_history"
    __table_args__ = {"schema": "auth"}

    # 主键
    id: int | None = Field(default=None, primary_key=True, title="ID")

    # 关联字段
    user_id: UUID | None = Field(sa_column=Column(PGUUID, ForeignKey("auth.users.id")), title="用户ID")

    # 登录详情字段
    ip_address: str | None = Field(sa_column=Column(INET), title="IP地址")
    user_agent: str | None = Field(sa_column=Column(Text), title="用户代理")
    device_info: Dict | None = Field(sa_column=Column(JSONB), title="设备信息")
    location: str | None = Field(sa_column=Column(String(255)), title="位置")
    status_code: int | None = Field(title="状态码")
    failure_reason: str | None = Field(sa_column=Column(String(255)), title="失败原因")

    # 关系
    user: Optional["User"] = Relationship()


class LoginAttempt(SQLModel, table=True):
    """登录尝试记录表
    
    用于检测和防止暴力破解攻击
    """
    __tablename__ = "login_attempts"
    __table_args__ = {"schema": "auth"}

    id: int | None = Field(primary_key=True, title="ID")
    ip_address: str = Field(sa_column=Column(String(45), nullable=False), title="IP地址")
    username: str | None = Field(sa_column=Column(String(255)), title="用户名")
    attempt_time: datetime = Field(sa_column=Column(DateTime, nullable=False, default=datetime.utcnow), title="尝试时间")
    is_successful: bool = Field(sa_column=Column(Boolean, nullable=False), title="是否成功")
    user_agent: str | None = Field(sa_column=Column(Text), title="用户代理")
    extra_data: Dict | None = Field(sa_column=Column(JSONB), title="额外数据")
    create_time: datetime | None = Field(title='创建时间')
    update_time: datetime | None = Field(title='更新时间')
