import time

import uvicorn
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware

from app.api import router
from app.core.core_config import settings
from app.core.core_log import logger
from app.core.core_middlewares import setup_middlewares

app = FastAPI(
    docs_url="/docs",
    redoc_url="/redoc",
    title="Your AI Agent API",
    description="API for Your AI Agent system",
    version="0.1.0"
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 设置自定义中间件
setup_middlewares(app)

# 注册API路由
app.include_router(router)


@app.get("/")
def read_root():
    return {"message": "Welcome to the API"}


@app.get("/health")
def health_check():
    return {"status": "healthy"}


@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    # 调用实际的请求处理函数
    response = await call_next(request)
    # 计算处理时间
    process_time = time.time() - start_time
    # 构建自定义日志消息
    log_message = (
        f"{request.client.host:>16} - "
        f"{request.method:>5} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Process Time: {process_time:.3f}s"
    )
    logger.info(log_message)
    # 将处理时间添加到响应 headers
    response.headers["X-Process-Time"] = str(process_time)
    return response


logger.info(f"FastAPI application started：Debug mode: {settings.debug}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.backend_port,
        reload=settings.debug,
        reload_delay=0.1,  # 文件变更检测和重新加载之间的延迟
        workers=1,  # 开发模式下确保只有一个工作进程
        access_log=False,
    )
