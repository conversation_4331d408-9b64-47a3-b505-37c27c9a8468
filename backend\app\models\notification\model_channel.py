from typing import Dict, List, ForwardRef, TYPE_CHECKING
from uuid import UUID

from sqlalchemy import Column, String, Boolean, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB, UUID as PGUUID
from sqlmodel import Field, SQLModel

from app.db.db_orm_base_model import MixUUID, MixTime
from app.models.notification.model_notification_base import NotificationChannelBase


# 通知渠道关联表模型
class NotificationChannel(SQLModel, table=True):
    """通知渠道关联表
    
    通知与渠道的多对多关联
    """
    __tablename__ = "notification_channel_links"
    __table_args__ = {"schema": "notification"}

    notification_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("notification.notifications.id"), primary_key=True), title="通知ID")
    channel_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("notification.notification_channels.id"), primary_key=True), title="渠道ID")
    status: str = Field(default="pending", sa_column=Column(String(20), nullable=False, default="pending"), title="状态")
    result: Dict | None = Field(None, sa_column=Column(JSONB), title="发送结果")


# 避免循环导入
if TYPE_CHECKING:
    pass
else:
    Notification = ForwardRef("Notification")


# 通知渠道配置创建模型
class NotificationChannelCreate(NotificationChannelBase):
    """通知渠道配置创建模型
    
    用于创建新的通知渠道配置
    """
    description: str | None = None
    config: Dict
    default_template_id: UUID | None = None


# 通知渠道配置更新模型
class NotificationChannelUpdate(SQLModel):
    """通知渠道配置更新模型
    
    用于更新通知渠道配置
    """
    name: str | None = None
    description: str | None = None
    config: Dict | None = None
    is_active: bool | None = None
    default_template_id: UUID | None = None


# 通知渠道配置数据库模型
class NotificationChannelConfig(NotificationChannelBase, MixUUID, MixTime, table=True):
    """通知渠道配置表
    
    存储系统支持的通知渠道配置
    """
    __tablename__ = "notification_channels"
    __table_args__ = (
        UniqueConstraint("name", "channel_type", name="uq_channel_name_type"),
        {"schema": "notification"}
    )

    # 扩展字段
    description: str | None = Field(None, sa_column=Column(String(255)), title="渠道描述")
    config: Dict = Field(..., sa_column=Column(JSONB, nullable=False), title="渠道配置")
    is_default: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="是否默认渠道")
    is_system: bool = Field(default=False, sa_column=Column(Boolean, nullable=False, default=False), title="是否系统渠道")
    default_template_id: UUID | None = Field(None, sa_column=Column(PGUUID), title="默认模板ID")

    # 关系
    # 暂时注释掉notifications关系，解决循环导入问题
    # notifications: List["Notification"] = Relationship(back_populates="channels", link_model=NotificationChannel)


# 通知渠道响应模型
class NotificationChannelResponse(NotificationChannelBase):
    """通知渠道响应模型
    
    用于API响应返回通知渠道信息
    """
    id: UUID
    description: str | None = None
    is_default: bool
    is_system: bool
    default_template_id: UUID | None = None
    create_time: str

    class Config:
        from_attributes = True


# 通知渠道列表响应模型
class NotificationChannelListResponse(SQLModel):
    """通知渠道列表响应模型"""
    total_count: int
    data: List[NotificationChannelResponse]
