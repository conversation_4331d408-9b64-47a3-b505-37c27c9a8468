"""
AI模块配置文件

包含工作流（Workflow）和团队（Team）的相关配置设置
"""

from pydantic import BaseModel


class TeamSettings(BaseModel):
    """团队相关配置"""
    # DeepSeek模型配置
    deepseek_chat: str = "deepseek-chat"
    deepseek_coder: str = "deepseek-coder"

    # 默认模型
    default_model: str = "deepseek-chat"

    # 默认参数
    default_max_completion_tokens: int = 2000
    default_temperature: float = 0.7

    # 存储配置
    storage_table_prefix: str = "ai_"


class WorkflowSettings(BaseModel):
    """工作流相关配置"""
    # DeepSeek模型配置
    deepseek_chat: str = "deepseek-chat"
    deepseek_coder: str = "deepseek-coder"

    # 默认模型
    default_model: str = "deepseek-chat"

    # 默认参数
    default_max_completion_tokens: int = 2000
    default_temperature: float = 0.7

    # 存储配置
    storage_table_prefix: str = "ai_"


# 实例化配置对象
team_settings = TeamSettings()
workflow_settings = WorkflowSettings()
