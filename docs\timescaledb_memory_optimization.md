# TimescaleDB内存优化解决方案

## 问题描述

在使用TimescaleDB进行超表(hypertable)中插入和更新操作时，我们观察到内存使用量持续线性增长，最终可能导致系统OOM(内存不足)。特别是在处理大量时序数据和K线数据时，这个问题更为明显。

## 解决方案

我们通过以下几个方面对TimescaleDB进行了内存使用优化：

### 1. PostgreSQL内存参数优化

在`docker-compose.yml`中，我们调整了以下关键内存相关参数：

- 禁用了JIT编译，减少内存使用：`jit=off`
- 设置了合理的shared_buffers大小：`shared_buffers=2048MB`
- 设置了work_mem以控制每个操作的内存使用：`work_mem=10185kB`
- 配置了idle_in_transaction_session_timeout以自动终止空闲事务：`idle_in_transaction_session_timeout=300000`
- 禁用了函数遥测以避免潜在的内存泄漏：`timescaledb.telemetry_level=no_functions`
- 增加了查询监控来跟踪长时间运行的查询：`log_min_duration_statement=1000`

### 2. 超表压缩优化

在`99_performance_tuning.sql`脚本中，我们设置了以下优化：

- 为`kline.kline`表配置了更合理的压缩策略：
  ```sql
  ALTER TABLE kline.kline SET (
      timescaledb.compress,
      timescaledb.compress_segmentby = 'symbol_id',
      timescaledb.compress_orderby = 'timestamp DESC'
  );
  ```
- 设置了每天一个chunk的分区策略，以保持每个chunk大小合理：
  ```sql
  SELECT set_chunk_time_interval('kline.kline', INTERVAL '1 day');
  ```
- 配置了压缩策略，自动压缩3天前的数据：
  ```sql
  SELECT add_compression_policy('kline.kline', INTERVAL '3 days');
  ```
- 设置保留策略，自动删除90天前的数据：
  ```sql
  SELECT add_retention_policy('kline.kline', INTERVAL '90 days');
  ```

### 3. 维护和监控脚本

我们编写了两个Python脚本来帮助维护和监控系统：

1. **维护脚本** (`backend/scripts/maintenance.py`)：
   - 定期执行VACUUM ANALYZE
   - 手动压缩旧的数据块
   - 刷新连续聚合视图
   - 终止长时间空闲的事务
   - 监控内存使用情况

2. **内存监控脚本** (`backend/scripts/monitor_memory.py`)：
   - 监控数据库后端进程的内存使用
   - 监控超表及其块的大小和状态
   - 检查长时间运行的查询
   - 监控PostgreSQL关键内存设置

## 运行和部署

### 重新启动数据库

修改配置后，需要重新启动数据库：

```bash
docker-compose down
docker-compose up -d
```

### 运行维护脚本

```bash
# 安装必要依赖
pip install psycopg2-binary

# 设置环境变量
export DB_NAME=your_db_name
export DB_USER=postgres
export DB_PASSWORD=your_password
export DB_HOST=localhost
export DB_PORT=5432

# 运行脚本
python backend/scripts/maintenance.py
python backend/scripts/monitor_memory.py
```

也可以将脚本添加到crontab中定期执行。

## 注意事项

1. 内存参数需要根据服务器实际可用内存调整，不要设置过大的值
2. 压缩和保留策略应根据实际数据量和业务需求进行调整
3. 长时间运行的事务会增加内存使用，应尽量避免
4. 定期监控内存使用情况，及时发现问题

## 相关资源

- [TimescaleDB文档](https://docs.timescale.com/)
- [PostgreSQL内存调优指南](https://www.postgresql.org/docs/current/runtime-config-resource.html)
- [TimescaleDB压缩文档](https://docs.timescale.com/timescaledb/latest/how-to-guides/compression/) 