import SwiftUI

struct LoginView: View {
    @EnvironmentObject var userData: UserData
    @EnvironmentObject var themeSettings: ThemeSettings
    @EnvironmentObject var languageManager: LanguageManager
    @Binding var isPresented: Bool
    
    @State private var email = ""
    @State private var password = ""
    @State private var rememberMe = false
    @State private var isLoading = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var showingRegister = false
    @FocusState private var isEmailFocused: Bool
    @FocusState private var isPasswordFocused: Bool
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 标题区域
                    VStack(spacing: 8) {
                        Image(systemName: "person.crop.circle.fill")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 80, height: 80)
                            .foregroundColor(.blue)
                            .padding(.top, 40)
                        
                        Text(languageManager.localizedString("auth.login.title"))
                            .font(.largeTitle)
                            .fontWeight(.bold)
                        
                        Text(languageManager.localizedString("auth.login.subtitle"))
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.horizontal)
                    
                    // 表单区域
                    VStack(spacing: 16) {
                        // 邮箱输入
                        VStack(alignment: .leading, spacing: 8) {
                            Text(languageManager.localizedString("auth.login.fields.email"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            TextField(languageManager.localizedString("auth.login.fields.email"), text: $email)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .keyboardType(.emailAddress)
                                .autocapitalization(.none)
                                .autocorrectionDisabled()
                                .focused($isEmailFocused)
                        }
                        
                        // 密码输入
                        VStack(alignment: .leading, spacing: 8) {
                            Text(languageManager.localizedString("auth.login.fields.password"))
                                .font(.subheadline)
                                .fontWeight(.medium)
                            
                            SecureField(languageManager.localizedString("auth.login.fields.password"), text: $password)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .focused($isPasswordFocused)
                        }
                        
                        // 记住我和忘记密码
                        HStack {
                            Button(action: {
                                rememberMe.toggle()
                            }) {
                                HStack(spacing: 8) {
                                    Image(systemName: rememberMe ? "checkmark.square.fill" : "square")
                                        .foregroundColor(rememberMe ? .blue : .gray)
                                    Text(languageManager.localizedString("auth.login.actions.rememberMe"))
                                        .font(.footnote)
                                        .foregroundColor(.primary)
                                }
                            }
                            
                            Spacer()
                            
                            Button(action: {
                                // TODO: 实现忘记密码功能
                            }) {
                                Text(languageManager.localizedString("auth.login.actions.forgotPassword"))
                                    .font(.footnote)
                                    .foregroundColor(.blue)
                            }
                        }
                        
                        // 登录按钮
                        Button(action: performLogin) {
                            HStack {
                                if isLoading {
                                    ProgressView()
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        .scaleEffect(0.8)
                                }
                                Text(languageManager.localizedString("auth.login.actions.signIn"))
                                    .fontWeight(.semibold)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(isLoading ? Color.gray : Color.blue)
                            )
                        }
                        .disabled(isLoading || email.isEmpty || password.isEmpty)
                        
                        // 注册链接
                        HStack {
                            Text(languageManager.localizedString("auth.login.actions.noAccount"))
                                .font(.footnote)
                                .foregroundColor(.secondary)
                            
                            Button(action: {
                                showingRegister = true
                            }) {
                                Text(languageManager.localizedString("auth.login.actions.register"))
                                    .font(.footnote)
                                    .fontWeight(.medium)
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                    .padding(.horizontal, 24)
                    
                    Spacer()
                }
            }
            .navigationTitle("")
            .navigationBarHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(languageManager.localizedString("common.cancel")) {
                        dismissKeyboard()
                        isPresented = false
                    }
                }
            }
            .alert(isPresented: $showingAlert) {
                Alert(
                    title: Text(languageManager.localizedString("auth.login.error.title")),
                    message: Text(alertMessage),
                    dismissButton: .default(Text(languageManager.localizedString("common.ok")))
                )
            }
            .onTapGesture {
                dismissKeyboard()
            }
        }
        .preferredColorScheme(themeSettings.colorScheme)
        .sheet(isPresented: $showingRegister) {
            RegisterView(isPresented: $showingRegister)
                .environmentObject(userData)
                .environmentObject(themeSettings)
                .environmentObject(languageManager)
        }
    }
    
    private func performLogin() {
        guard !isLoading else { return }
        
        // 验证输入
        guard !email.isEmpty, !password.isEmpty else {
            alertMessage = languageManager.localizedString("auth.login.error.emptyFields")
            showingAlert = true
            return
        }
        
        dismissKeyboard()
        isLoading = true
        
        Task {
            do {
                let success = await userData.login(username: email, password: password)
                
                await MainActor.run {
                    isLoading = false
                    if success {
                        isPresented = false
                    } else {
                        alertMessage = languageManager.localizedString("auth.login.error.invalidCredentials")
                        showingAlert = true
                    }
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    alertMessage = error.localizedDescription
                    showingAlert = true
                }
            }
        }
    }
    
    private func dismissKeyboard() {
        isEmailFocused = false
        isPasswordFocused = false
    }
}

#Preview {
    LoginView(isPresented: .constant(true))
        .environmentObject(UserData())
        .environmentObject(ThemeSettings())
        .environmentObject(LanguageManager())
} 