<template>
  <div class="flex flex-col items-start justify-center gap-1.5">
    <div v-for="(step, index) in reasoning" :key="`${step.title}-${step.action}-${index}`" class="flex items-center gap-1.5 text-gray-800 dark:text-gray-300">
      <div class="flex h-[18px] items-center rounded-md bg-gray-100 dark:bg-dark hover:bg-gray-200 dark:hover:bg-gray-600 px-1.5 transition-colors">
        <p class="text-xs font-medium">步骤 {{ index + 1 }}</p>
      </div>
      <p class="text-xs">{{ step.title }}</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { ReasoningSteps } from '@/types/playground';

interface Props {
  reasoning: ReasoningSteps[];
}

defineProps<Props>();
</script>
