import Foundation

struct User: Codable, Identifiable {
    let id: String
    let email: String
    let username: String?
    let avatar: String?
    let createdAt: String?
    let updatedAt: String?
    
    enum CodingKeys: String, CodingKey {
        case id
        case email
        case username
        case avatar
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

class UserData: ObservableObject {
    @Published var currentUser: User?
    @Published var isLoggedIn: Bool = false
    @Published var token: String?
    
    private let tokenKey = "auth_token"
    private let userKey = "current_user"
    private let apiService = APIService()
    
    init() {
        loadUserData()
    }
    
    private func loadUserData() {
        // 加载token
        if let savedToken = UserDefaults.standard.string(forKey: tokenKey) {
            self.token = savedToken
            self.isLoggedIn = true
        }
        
        // 加载用户信息
        if let userData = UserDefaults.standard.data(forKey: userKey),
           let user = try? JSONDecoder().decode(User.self, from: userData) {
            self.currentUser = user
        }
    }
    
    func login(user: User, token: String) {
        self.currentUser = user
        self.token = token
        self.isLoggedIn = true
        
        // 保存到UserDefaults
        UserDefaults.standard.set(token, forKey: tokenKey)
        if let userData = try? JSONEncoder().encode(user) {
            UserDefaults.standard.set(userData, forKey: userKey)
        }
    }
    
    func login(username: String, password: String) async -> Bool {
        do {
            let response = try await apiService.login(username: username, password: password)
            
            // 登录成功，保存token
            await MainActor.run {
                self.token = response.access_token
                self.isLoggedIn = true
                
                // 保存token到UserDefaults
                UserDefaults.standard.set(response.access_token, forKey: tokenKey)
            }
            
            // 获取用户信息
            do {
                let userInfo = try await apiService.getCurrentUser(token: response.access_token)
                await MainActor.run {
                    self.currentUser = userInfo
                    // 保存用户信息到UserDefaults
                    if let userData = try? JSONEncoder().encode(userInfo) {
                        UserDefaults.standard.set(userData, forKey: userKey)
                    }
                }
            } catch {
                print("获取用户信息失败: \(error)")
                // 即使获取用户信息失败，登录仍然成功
            }
            
            return true
        } catch {
            print("登录失败: \(error)")
            return false
        }
    }
    
    func register(name: String, email: String, password: String) async -> Bool {
        do {
            let response = try await apiService.register(name: name, email: email, password: password)
            
            // 注册成功，直接使用返回的token
            await MainActor.run {
                self.token = response.access_token
                self.isLoggedIn = true
                
                // 保存token到UserDefaults
                UserDefaults.standard.set(response.access_token, forKey: tokenKey)
            }
            
            // 获取用户信息
            do {
                let userInfo = try await apiService.getCurrentUser(token: response.access_token)
                await MainActor.run {
                    self.currentUser = userInfo
                    // 保存用户信息到UserDefaults
                    if let userData = try? JSONEncoder().encode(userInfo) {
                        UserDefaults.standard.set(userData, forKey: userKey)
                    }
                }
            } catch {
                print("获取用户信息失败: \(error)")
                // 即使获取用户信息失败，注册仍然成功
            }
            
            return true
        } catch {
            print("注册失败: \(error)")
            return false
        }
    }
    
    func logout() {
        self.currentUser = nil
        self.token = nil
        self.isLoggedIn = false
        
        // 清除UserDefaults
        UserDefaults.standard.removeObject(forKey: tokenKey)
        UserDefaults.standard.removeObject(forKey: userKey)
    }
    
    func updateUser(_ user: User) {
        self.currentUser = user
        if let userData = try? JSONEncoder().encode(user) {
            UserDefaults.standard.set(userData, forKey: userKey)
        }
    }
} 