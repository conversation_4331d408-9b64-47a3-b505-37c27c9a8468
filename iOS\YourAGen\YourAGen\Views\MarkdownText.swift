import SwiftUI
import MarkdownUI

// 使用MarkdownUI库的增强Markdown文本组件
struct MarkdownText: View {
    let content: String
    let fontSize: CGFloat
    let textColor: Color
    let isInBubble: Bool
    
    @Environment(\.colorScheme) var colorScheme
    
    init(_ content: String, fontSize: CGFloat = 14, textColor: Color = .primary, isInBubble: Bool = false) {
        self.content = content
        self.fontSize = fontSize
        self.textColor = textColor
        self.isInBubble = isInBubble
    }
    
    var body: some View {
        Markdown(content)
            .markdownTheme(customTheme)
            .font(.system(size: fontSize)) // 直接设置字体大小
            .textSelection(.enabled)
            .fixedSize(horizontal: false, vertical: true) // 防止内容截断
    }
    
    // 自定义主题 - 参考前端Vue样式
    private var customTheme: Theme {
        Theme()
            // 基础文本样式 - 设置很小的字体
            .text {
                FontSize(.em(fontSize / 32.0)) // 使用更小的比例
                FontWeight(.regular)
                ForegroundColor(primaryTextColor)
            }
            // 行内代码样式 - 参考前端
            .code {
                FontFamilyVariant(.monospaced)
                FontWeight(.medium)
                ForegroundColor(inlineCodeTextColor)
                BackgroundColor(inlineCodeBackgroundColor)
            }
            // 链接样式 - 无下划线，蓝色
            .link {
                ForegroundColor(linkColor)
                FontWeight(.medium)
                UnderlineStyle(nil) // 明确移除下划线
            }
            // 粗体样式 - 更突出
            .strong {
                FontWeight(.bold)
                ForegroundColor(strongTextColor)
            }
            // 斜体样式
            .emphasis {
                FontStyle(.italic)
                FontWeight(.medium)
                ForegroundColor(emphasisTextColor)
            }
            // 标题样式 - 参考前端层次
            .heading1 { configuration in
                configuration.label
                    .markdownTextStyle {
                        FontWeight(.bold)
                        ForegroundColor(h1Color)
                    }
                    .markdownMargin(top: 14, bottom: 6)
            }
            .heading2 { configuration in
                configuration.label
                    .markdownTextStyle {
                        FontWeight(.bold)
                        ForegroundColor(h2Color)
                    }
                    .markdownMargin(top: 12, bottom: 5)
            }
            .heading3 { configuration in
                configuration.label
                    .markdownTextStyle {
                        FontWeight(.semibold)
                        ForegroundColor(h3Color)
                    }
                    .markdownMargin(top: 10, bottom: 4)
            }
            .heading4 { configuration in
                configuration.label
                    .markdownTextStyle {
                        FontWeight(.semibold)
                        ForegroundColor(h4Color)
                    }
                    .markdownMargin(top: 8, bottom: 3)
            }
            .heading5 { configuration in
                configuration.label
                    .markdownTextStyle {
                        FontWeight(.medium)
                        ForegroundColor(h5Color)
                    }
                    .markdownMargin(top: 6, bottom: 2)
            }
            .heading6 { configuration in
                configuration.label
                    .markdownTextStyle {
                        FontWeight(.medium)
                        ForegroundColor(h6Color)
                    }
                    .markdownMargin(top: 6, bottom: 2)
            }
            // 段落样式 - 参考前端间距
            .paragraph { configuration in
                configuration.label
                    .markdownMargin(top: 0, bottom: 10) // 稍微减小间距
            }
            // 代码块样式 - 更精致
            .codeBlock { configuration in
                configuration.label
                    .markdownTextStyle {
                        FontFamilyVariant(.monospaced)
                        ForegroundColor(codeBlockTextColor)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(codeBlockBackgroundColor)
                    .overlay(
                        RoundedRectangle(cornerRadius: 6)
                            .stroke(codeBlockBorderColor, lineWidth: 1)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 6))
                    .markdownMargin(top: 8, bottom: 12)
            }
            // 列表样式 - 统一间距和缩进
            .list { configuration in
                configuration.label
                    .markdownMargin(top: 6, bottom: 10)
            }
            // 列表项样式 - 统一的列表项间距和样式
            .listItem { configuration in
                configuration.label
                    .markdownMargin(top: 3, bottom: 3)
                    .markdownTextStyle {
                        ForegroundColor(primaryTextColor)
                    }
            }
            // 引用块样式 - 更现代化
            .blockquote { configuration in
                configuration.label
                    .markdownTextStyle {
                        FontStyle(.italic)
                        ForegroundColor(blockquoteTextColor)
                    }
                    .padding(EdgeInsets(top: 12, leading: 16, bottom: 12, trailing: 16))
                    .background(blockquoteBackgroundColor)
                    .overlay(alignment: Alignment.leading) {
                        Rectangle()
                            .fill(blockquoteBorderColor)
                            .frame(width: 4)
                    }
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                    .markdownMargin(top: 12, bottom: 12)
            }
            // 表格样式
            .table { configuration in
                configuration.label
                    .fixedSize(horizontal: false, vertical: true)
                    .background(tableBackgroundColor)
                    .overlay(
                        RoundedRectangle(cornerRadius: 6)
                            .stroke(tableBorderColor, lineWidth: 1)
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 6))
                    .markdownMargin(top: 12, bottom: 12)
            }
            // 分隔线样式 - 更精致
            .thematicBreak {
                Rectangle()
                    .fill(thematicBreakColor)
                    .frame(height: 1)
                    .markdownMargin(top: 16, bottom: 16)
            }
    }
    
    // 颜色系统 - 更丰富的深色模式配色
    private var primaryTextColor: Color {
        colorScheme == .dark ? Color(red: 0.90, green: 0.92, blue: 0.95) : Color(red: 0.15, green: 0.20, blue: 0.25)
    }
    
    // 标题颜色层次 - 增加更多色彩变化
    private var h1Color: Color {
        colorScheme == .dark ? Color(red: 0.40, green: 0.80, blue: 1.0) : Color(red: 0.08, green: 0.12, blue: 0.18)
    }
    
    private var h2Color: Color {
        colorScheme == .dark ? Color(red: 0.60, green: 0.85, blue: 0.95) : Color(red: 0.12, green: 0.16, blue: 0.22)
    }
    
    private var h3Color: Color {
        colorScheme == .dark ? Color(red: 0.75, green: 0.90, blue: 0.85) : Color(red: 0.18, green: 0.22, blue: 0.28)
    }
    
    private var h4Color: Color {
        colorScheme == .dark ? Color(red: 0.95, green: 0.85, blue: 0.70) : Color(red: 0.25, green: 0.29, blue: 0.35)
    }
    
    private var h5Color: Color {
        colorScheme == .dark ? Color(red: 0.90, green: 0.75, blue: 0.85) : Color(red: 0.32, green: 0.36, blue: 0.42)
    }
    
    private var h6Color: Color {
        colorScheme == .dark ? Color(red: 0.85, green: 0.85, blue: 0.85) : Color(red: 0.40, green: 0.44, blue: 0.50)
    }
    
    private var strongTextColor: Color {
        colorScheme == .dark ? Color(red: 1.0, green: 0.85, blue: 0.40) : Color(red: 0.05, green: 0.08, blue: 0.12)
    }
    
    private var emphasisTextColor: Color {
        colorScheme == .dark ? Color(red: 0.70, green: 0.90, blue: 1.0) : Color(red: 0.20, green: 0.24, blue: 0.30)
    }
    
    // 代码相关颜色 - 增强对比度和色彩
    private var inlineCodeTextColor: Color {
        colorScheme == .dark ? Color(red: 1.0, green: 0.80, blue: 0.20) : Color(red: 0.85, green: 0.18, blue: 0.18)
    }
    
    private var inlineCodeBackgroundColor: Color {
        colorScheme == .dark ? Color(red: 0.20, green: 0.25, blue: 0.35).opacity(0.9) : Color(red: 0.96, green: 0.97, blue: 0.98)
    }
    
    private var codeBlockTextColor: Color {
        colorScheme == .dark ? Color(red: 0.85, green: 0.95, blue: 0.85) : Color(red: 0.20, green: 0.24, blue: 0.30)
    }
    
    private var codeBlockBackgroundColor: Color {
        colorScheme == .dark ? Color(red: 0.10, green: 0.15, blue: 0.20) : Color(red: 0.98, green: 0.98, blue: 0.99)
    }
    
    private var codeBlockBorderColor: Color {
        colorScheme == .dark ? Color(red: 0.30, green: 0.40, blue: 0.50) : Color(red: 0.90, green: 0.91, blue: 0.92)
    }
    
    // 链接颜色 - 更鲜明的颜色
    private var linkColor: Color {
        colorScheme == .dark ? Color(red: 0.30, green: 0.75, blue: 1.0) : Color(red: 0.0, green: 0.48, blue: 0.95)
    }
    
    // 引用块颜色 - 更丰富的色彩
    private var blockquoteTextColor: Color {
        colorScheme == .dark ? Color(red: 0.80, green: 0.85, blue: 0.95) : Color(red: 0.35, green: 0.40, blue: 0.45)
    }
    
    private var blockquoteBackgroundColor: Color {
        colorScheme == .dark ? Color(red: 0.15, green: 0.20, blue: 0.30).opacity(0.8) : Color(red: 0.97, green: 0.98, blue: 0.99)
    }
    
    private var blockquoteBorderColor: Color {
        colorScheme == .dark ? Color(red: 0.50, green: 0.80, blue: 1.0) : Color(red: 0.0, green: 0.48, blue: 0.95)
    }
    
    // 表格颜色
    private var tableBackgroundColor: Color {
        colorScheme == .dark ? Color(red: 0.12, green: 0.14, blue: 0.18) : Color(red: 0.99, green: 0.99, blue: 1.0)
    }
    
    private var tableBorderColor: Color {
        colorScheme == .dark ? Color(red: 0.25, green: 0.28, blue: 0.35) : Color(red: 0.88, green: 0.89, blue: 0.90)
    }
    
    // 分隔线颜色
    private var thematicBreakColor: Color {
        colorScheme == .dark ? Color(red: 0.30, green: 0.33, blue: 0.40) : Color(red: 0.85, green: 0.86, blue: 0.88)
    }
}

// MARK: - 预览

#Preview {
    ScrollView {
        VStack(spacing: 20) {
            MarkdownText(
                """
                # 🎨 优雅的Markdown渲染
                
                这是一个**全新升级**的Markdown渲染组件，具有*更加优雅*的视觉设计。
                
                ## ✨ 主要特性
                
                ### 🎯 精美的代码展示
                
                这里有一些行内代码：`print("Hello, Beautiful World!")`
                
                ```swift
                // 优雅的Swift代码示例
                func createBeautifulUI() -> some View {
                    VStack(spacing: 16) {
                        Text("优雅的设计")
                            .font(.title)
                            .fontWeight(.bold)
                        
                        Text("精致的细节")
                            .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                }
                ```
                
                ### 📝 丰富的文本样式
                
                - **粗体文本** - 更加突出
                - *斜体文本* - 优雅的强调
                - `行内代码` - 专业的展示
                - [精美链接](https://example.com) - 现代化设计
                
                > 💡 这是一个精美的引用块
                > 
                > 具有优雅的边框和背景色，让内容更加突出和易读。
                
                ### 📊 表格展示
                
                | 特性 | 描述 | 状态 |
                |------|------|------|
                | 🎨 美观设计 | 精致的视觉效果 | ✅ 完成 |
                | 📱 响应式 | 适配不同屏幕 | ✅ 完成 |
                | 🚀 性能优化 | 流畅的渲染 | ✅ 完成 |
                
                ---
                
                **感谢使用我们的Markdown渲染组件！** 🙏
                """
            )
            
            Spacer()
        }
        .padding()
    }
} 