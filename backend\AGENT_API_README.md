# Agent API 功能说明

本项目已成功集成了 `agent-api` 项目的核心功能，提供了完整的 Agent 服务 API。

## 🚀 功能特性

### 支持的 Agents

1. **Web Search Agent** (`web_agent`)
    - 功能：网络搜索和信息检索
    - 工具：DuckDuckGo 搜索
    - 特点：支持多源信息交叉验证，提供引用支持

2. **Finance Agent** (`finance_agent`)
    - 功能：金融数据分析和市场洞察
    - 工具：YFinance API、DuckDuckGo 搜索
    - 特点：提供股价、财务指标、分析师建议等专业金融信息

3. **Agno Assist** (`agno_assist`)
    - 功能：Agno 框架使用指导和代码示例
    - 工具：知识库搜索、DuckDuckGo 搜索
    - 特点：基于 Agno 官方文档的专业技术支持

4. **Crypto Prediction Agent** (`crypto_prediction_agent`)
    - 功能：加密货币市场分析和预测
    - 工具：专业金融工具
    - 特点：综合技术分析、基本面分析和市场情绪分析，提供专业的加密货币投资建议

### API 端点

#### 1. 获取可用 Agents

```http
GET /v1/agents
```

响应示例：

```json
["web_agent", "agno_assist", "finance_agent", "crypto_prediction_agent"]
```

#### 2. 运行 Agent

```http
POST /v1/agents/{agent_id}/runs
```

请求体：

```json
{
  "message": "你的问题或请求",
  "stream": true,
  "model": "gpt-4.1",
  "user_id": "可选的用户ID",
  "session_id": "可选的会话ID"
}
```

#### 3. 加载知识库（仅适用于 agno_assist）

```http
POST /v1/agents/agno_assist/knowledge/load
```

#### 4. 健康检查

```http
GET /v1/health
```

#### 5. Playground 接口

```http
GET /v1/playground
```

## 🛠️ 安装和配置

### 1. 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

### 2. 环境变量配置

确保以下环境变量已正确配置：

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password

# API 密钥
OPENAI_API_KEY=your_openai_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key  # 可选，用于 DeepSeek 模型
```

### 3. 数据库设置

确保 PostgreSQL 数据库已安装并运行，并且已安装 pgvector 扩展：

```sql
CREATE EXTENSION IF NOT EXISTS vector;
```

### 4. 启动服务

```bash
cd backend
python main.py
```

服务将在 `http://localhost:8000` 启动。

## 📖 使用示例

### Python 客户端示例

```python
import httpx
import asyncio


async def test_agent():
    async with httpx.AsyncClient() as client:
        # 获取可用 agents
        response = await client.get("http://localhost:8000/v1/agents")
        print("可用 Agents:", response.json())

        # 与 Web Search Agent 对话
        response = await client.post(
            "http://localhost:8000/v1/agents/web_agent/runs",
            json={
                "message": "今天的科技新闻有什么？",
                "stream": False,
                "user_id": "test_user"
            }
        )
        print("Web Agent 响应:", response.json())

        # 与 Crypto Prediction Agent 对话
        response = await client.post(
            "http://localhost:8000/v1/agents/crypto_prediction_agent/runs",
            json={
                "message": "分析一下比特币现在的市场情况",
                "stream": False,
                "user_id": "test_user"
            }
        )
        print("Crypto Agent 响应:", response.json())


asyncio.run(test_agent())
```

### cURL 示例

```bash
# 获取可用 agents
curl -X GET "http://localhost:8000/v1/agents"

# 与 Finance Agent 对话
curl -X POST "http://localhost:8000/v1/agents/finance_agent/runs" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "分析一下苹果公司的股票表现",
    "stream": false,
    "user_id": "test_user"
  }'

# 与 Crypto Prediction Agent 对话
curl -X POST "http://localhost:8000/v1/agents/crypto_prediction_agent/runs" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "分析以太坊的投资前景",
    "stream": false,
    "user_id": "test_user"
  }'
```

## 🔧 测试

运行测试脚本验证功能：

```bash
cd backend
python test_agents.py
```

## 📚 API 文档

启动服务后，访问以下地址查看完整的 API 文档：

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 🎯 与 Agno Playground 集成

本 API 完全兼容 Agno Playground：

1. 打开 [Agno Playground](https://app.agno.com/playground)
2. 添加 `http://localhost:8000` 作为新的端点
3. 选择你的端点并开始与 Agents 对话

## 💰 Crypto Prediction Agent 特色功能

### 分析维度

- **技术分析**：K线图、成交量、技术指标分析
- **基本面分析**：项目价值、团队实力、技术创新评估
- **市场情绪**：恐慌贪婪指数、社交媒体情绪分析

### 使用场景

```bash
# 单币种分析
"分析比特币当前的投资机会"

# 市场趋势分析
"当前加密货币市场的整体趋势如何？"

# 投资建议
"我应该现在买入以太坊吗？"

# 风险评估
"投资山寨币需要注意哪些风险？"
```

## 🔍 故障排除

### 常见问题

1. **数据库连接错误**
    - 确保 PostgreSQL 服务正在运行
    - 检查数据库连接配置
    - 确保已安装 pgvector 扩展

2. **API 密钥错误**
    - 检查 OPENAI_API_KEY 是否正确设置
    - 确保 API 密钥有足够的配额

3. **依赖包错误**
    - 运行 `pip install -r requirements.txt` 重新安装依赖
    - 确保 Python 版本兼容（推荐 3.8+）

### 日志查看

服务运行时会输出详细的日志信息，包括：

- API 请求日志
- Agent 执行日志
- 错误信息

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！

## 📄 许可证

本项目遵循原项目的许可证条款。 