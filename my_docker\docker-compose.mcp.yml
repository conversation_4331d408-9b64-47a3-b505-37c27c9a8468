services:
  browser-tools-server:
    image: node:lts-alpine
    container_name: browser-tools-server
    working_dir: /app
    command: sh -c "npx -y @agentdeskai/browser-tools-server@latest"
    ports:
      - "3025:3025"
    restart: unless-stopped
    volumes:
      - browser_tools_data:/app/data  # 使用命名卷替代绑定挂载
    networks:
      - browser-tools-network

networks:
  browser-tools-network:
    driver: bridge

# 定义命名卷
volumes:
  browser_tools_data:
    driver: local