"""市场k线模型

对应数据库中的 crypto.kline 表，遵循SQL文件定义
"""
from datetime import datetime

import sqlalchemy as sa
from sqlalchemy import PrimaryKeyConstraint
from sqlmodel import Field, Relationship

from app.db.db_orm_base import SchemaBase

# 前向引用
Symbol = "Symbol"


# 数据库市场K线模型
class Kline(SchemaBase, table=True):
    """市场K线数据库模型，对应crypto.kline表"""
    __tablename__ = "kline"
    __table_args__ = (
        PrimaryKeyConstraint('symbol_id', 'timestamp'),
        {"schema": "kline"}
    )
    # 主键字段
    symbol_id: int = Field(..., title="关联的交易对ID")
    timestamp: datetime = Field(title='时间', sa_column=sa.Column(sa.DateTime(timezone=True), nullable=False))

    open: float = Field(..., title="开盘价")
    high: float = Field(..., title="最高价")
    low: float = Field(..., title="最低价")
    close: float = Field(..., title="收盘价")
    count: int = Field(0, title="成交笔数")
    volume: float = Field(title='成交量(万)')
    buy_volume: float = Field(title='主动买入成交量(万)')

    # 关系
    symbol: Symbol = Relationship(
        back_populates="klines",
        sa_relationship_kwargs={
            "primaryjoin": "Kline.symbol_id == Symbol.id",
            "foreign_keys": "Kline.symbol_id"
        }
    )
