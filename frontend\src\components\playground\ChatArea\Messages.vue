<template>
  <div>
    <!-- 空状态 -->
    <ChatBlankState
      v-if="messages.length === 0"
      :system-status="systemStatus"
      :selected-agent="selectedAgent"
      :is-streaming="isStreaming"
      @send-suggestion="handleSendSuggestion"
      @open-sidebar="handleOpenSidebar"
    />

    <!-- 消息列表 -->
    <template v-else>
      <template v-for="(message, index) in messages" :key="`${message.role}-${message.created_at}-${index}`">
        <!-- 智能体消息包装器 -->
        <div v-if="message.role === 'agent'" class="flex flex-col gap-y-6">
          <!-- 推理步骤 -->
          <div v-if="message.extra_data?.reasoning_steps && message.extra_data.reasoning_steps.length > 0" class="flex items-start gap-3">
            <n-tooltip trigger="hover" placement="top">
              <template #trigger>
                <div class="flex items-center justify-center h-8 w-8 rounded-md bg-gray-800 dark:bg-gray-800 hover:bg-gray-900 dark:hover:bg-gray-700 transition-colors">
                  <n-icon size="14" class="text-white dark:text-gray-300">
                    <BulbOutline />
                  </n-icon>
                </div>
              </template>
              推理过程
            </n-tooltip>
            <div class="flex flex-col gap-2">
              <p class="text-xs uppercase text-gray-800 dark:text-gray-400">推理过程</p>
              <ReasoningSteps :reasoning="message.extra_data.reasoning_steps" />
            </div>
          </div>

          <!-- 引用 -->
          <div v-if="message.extra_data?.references && message.extra_data.references.length > 0" class="flex items-start gap-3">
            <n-tooltip trigger="hover" placement="top">
              <template #trigger>
                <div class="flex items-center justify-center h-8 w-8 rounded-md bg-gray-800 dark:bg-gray-800 hover:bg-gray-900 dark:hover:bg-gray-700 transition-colors">
                  <n-icon size="14" class="text-white dark:text-gray-300">
                    <BookmarkOutline />
                  </n-icon>
                </div>
              </template>
              参考文献
            </n-tooltip>
            <div class="flex flex-col gap-2">
              <p class="text-xs uppercase text-gray-800 dark:text-gray-400">参考文献</p>
              <References :references="message.extra_data.references" />
            </div>
          </div>

          <!-- 工具调用 -->
          <div v-if="message.tool_calls && message.tool_calls.length > 0" class="flex items-start gap-3">
            <n-tooltip trigger="hover" placement="top">
              <template #trigger>
                <div class="flex items-center justify-center h-8 w-8 rounded-md bg-gray-800 dark:bg-gray-800 hover:bg-gray-900 dark:hover:bg-gray-700 transition-colors">
                  <n-icon size="14" class="text-white dark:text-gray-300">
                    <HammerOutline />
                  </n-icon>
                </div>
              </template>
              工具调用
            </n-tooltip>

            <div class="flex flex-col gap-2">
              <p class="text-xs uppercase text-gray-800 dark:text-gray-400">工具调用</p>
              <div class="flex flex-wrap gap-1.5">
                <ToolComponent
                  v-for="(toolCall, toolIndex) in message.tool_calls"
                  :key="toolCall.tool_call_id || `${toolCall.tool_name}-${toolCall.created_at}-${toolIndex}`"
                  :tool="toolCall"
                />
              </div>
            </div>
          </div>

          <!-- 智能体消息内容 -->
          <AgentMessage :message="message" />
        </div>

        <!-- 用户消息 -->
        <UserMessage v-else :message="message" />
      </template>
    </template>
  </div>
</template>

<script lang="ts" setup>
import type { PlaygroundChatMessage } from '@/types/playground';
import { NIcon, NTooltip } from 'naive-ui';
import { BulbOutline, BookmarkOutline, HammerOutline } from '@vicons/ionicons5';
import ChatBlankState from './ChatBlankState.vue';
import AgentMessage from './AgentMessage.vue';
import UserMessage from './UserMessage.vue';
import ReasoningSteps from './ReasoningSteps.vue';
import References from './References.vue';
import ToolComponent from './ToolComponent.vue';

interface Props {
  messages: PlaygroundChatMessage[];
  systemStatus?: {
    ready: boolean;
  };
  selectedAgent?: {
    label: string;
  } | null;
  isStreaming?: boolean;
}

interface Emits {
  (e: 'send-suggestion', suggestion: string): void;
  (e: 'open-sidebar'): void;
}

const props = withDefaults(defineProps<Props>(), {
  systemStatus: () => ({ ready: false }),
  selectedAgent: null,
  isStreaming: false,
});

const emit = defineEmits<Emits>();

const handleSendSuggestion = (suggestion: string) => {
  emit('send-suggestion', suggestion);
};

const handleOpenSidebar = () => {
  emit('open-sidebar');
};
</script>
