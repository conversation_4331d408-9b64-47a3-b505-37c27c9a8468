from sqlalchemy import Table
from sqlalchemy.orm import declarative_base

from app.db.db_orm_base import SchemaBase
from app.db.db_session import db_manager

Base = declarative_base()
metadata = Base.metadata


class AutoLoadView:

    def __init__(self):
        self._views: dict[str, Table] = {}

    def get_view(self, view_name: str) -> Table:
        view = self._views.get(view_name)
        if view is None:
            if '.' in view_name:
                schema, table_name = view_name.split('.', 1)
                view = Table(
                    table_name,
                    SchemaBase.metadata,
                    schema=schema,
                    autoload_with=db_manager.get_sync_engine()
                )
            else:
                view = Table(
                    view_name,
                    SchemaBase.metadata,
                    autoload_with=db_manager.get_sync_engine()
                )
            self._views[view_name] = view
        return view

    @property
    def v_symbol(self) -> Table:
        return self.get_view('v_symbol')


View = AutoLoadView()
if __name__ == '__main__':
    my_view_name = f"kline.v_kline_indicator_history_30m"
    my_view = View.get_view(my_view_name)
