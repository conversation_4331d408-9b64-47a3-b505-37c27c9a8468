"""
核心常量模块

包含系统中使用的各种常量，特别是API路径前缀等
这样可以在多个模块中重用，避免硬编码
"""

# API路径前缀常量
API_PREFIX = "/api"  # 主API前缀
API_V1_PREFIX = "/v1"  # V1版本前缀

# 模块路径前缀常量
AUTH_PREFIX = "/auth"  # 认证模块前缀
USERS_PREFIX = "/users"  # 用户模块前缀
RBAC_PREFIX = "/rbac"  # 权限管理模块前缀
NOTIFICATIONS_PREFIX = "/notifications"  # 通知模块前缀

# 端点路径常量
LOGIN_ENDPOINT = "/login"  # 登录端点
REGISTER_ENDPOINT = "/register"  # 注册端点
REFRESH_ENDPOINT = "/refresh"  # 刷新令牌端点
LOGOUT_ENDPOINT = "/logout"  # 登出端点
ME_ENDPOINT = "/me"  # 获取当前用户信息端点

# 完整的认证相关路径
TOKEN_URL = f"{API_PREFIX}{API_V1_PREFIX}{AUTH_PREFIX}{LOGIN_ENDPOINT}"  # 完整的令牌获取URL
