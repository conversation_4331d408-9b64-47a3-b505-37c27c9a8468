"""数据模型包

这个包包含了所有的SQLModel数据模型，同时作为数据库模型和API数据验证模型。
"""

# 从子模块导入所有模型
from app.models.auth import *
from app.models.crypto import *
from app.models.notification import *
from app.models.rbac import *

# 统一导出
__all__ = [
    # 认证模块
    'User', 'UserCreate', 'UserUpdate', 'UserResponse', 'UserPasswordUpdate',
    'VerificationToken', 'PasswordHistory', 'UserAuthProvider',
    'AuthProviderType', 'LoginRequest', 'LoginResponse',
    'UserStatusHistory', 'Token', 'TokenRefresh',

    # 角色权限模块
    'Role', 'Permission', 'RolePermission', 'UserRole',

    # 通知模块
    'Notification', 'NotificationCreate', 'NotificationResponse',
    'NotificationTemplate', 'NotificationChannelConfig', 'UserPreference',
    'NotificationStatus', 'NotificationType', 'NotificationChannel',
    'NotificationPriority', 'ChannelType',

    # 加密货币模块
    'Symbol',
    'Ticker',
    'Kline', 'ApiKey'
]
