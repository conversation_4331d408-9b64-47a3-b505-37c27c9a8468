<template>
  <div class="relative language-switcher">
    <button
      @click="toggleDropdown"
      class="flex items-center space-x-1 p-2 rounded-md text-text-light dark:text-white hover:bg-gray-100 dark:hover:bg-secondary transition-colors"
      aria-haspopup="true"
      :aria-expanded="isOpen"
    >
      <span class="flex items-center">
        <span class="mr-2 text-sm font-bold">{{ localeLabels[currentLocale] }}</span>
        {{ currentLangLabel }}
      </span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-4 w-4 transition-transform duration-300"
        :class="{ 'rotate-180': isOpen }"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <div
      v-show="isOpen"
      class="absolute right-0 mt-1 min-w-full bg-white dark:bg-dark-secondary shadow-lg rounded-md overflow-hidden z-50 border border-gray-200 dark:border-gray-700"
    >
      <div
        v-for="(label, locale) in sortedLocales"
        :key="locale"
        @click="changeLanguage(locale as SupportedLocales)"
        class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-secondary transition-colors cursor-pointer text-text-light dark:text-white"
        :class="{ 'bg-gray-100 dark:bg-secondary font-medium': locale === currentLocale }"
      >
        <span class="mr-2 text-sm font-bold">{{ localeLabels[locale] }}</span>
        {{ label }}
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { setLanguage, SupportedLocales } from '@/i18n';

interface LocaleLabels {
  [key: string]: string;
}

interface LocaleMessages {
  [key: string]: string;
}

const { locale, t } = useI18n();
const isOpen = ref(false);

const availableLocales = computed<LocaleMessages>(() => ({
  en: t('language.en'),
  zh: t('language.zh'),
}));

// 定义语言排序顺序和标签
const localesOrder: SupportedLocales[] = ['en', 'zh'];
const localeLabels: LocaleLabels = {
  en: 'EN',
  zh: 'ZH',
};

// 排序后的语言列表
const sortedLocales = computed(() => {
  const sortedObj: Record<string, string> = {};
  localesOrder.forEach((locale: SupportedLocales) => {
    if (availableLocales.value[locale]) {
      sortedObj[locale] = availableLocales.value[locale];
    }
  });
  return sortedObj;
});

const currentLocale = computed<SupportedLocales>(() => locale.value as SupportedLocales);

const currentLangLabel = computed(() => availableLocales.value[currentLocale.value]);

const toggleDropdown = (): void => {
  isOpen.value = !isOpen.value;
};

const changeLanguage = (lang: SupportedLocales): void => {
  setLanguage(lang);
  isOpen.value = false;
};

// 点击外部关闭下拉菜单
const closeDropdown = (event: MouseEvent): void => {
  if (isOpen.value && !(event.target as Element).closest('.language-switcher')) {
    isOpen.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', closeDropdown);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown);
});
</script>
