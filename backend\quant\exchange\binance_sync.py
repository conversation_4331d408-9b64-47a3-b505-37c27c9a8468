"""
币安交易所数据同步模块
用于同步交易对信息到数据库
"""
from decimal import Decimal
from typing import Dict, List, Any

import pendulum
from sqlalchemy.ext.asyncio import AsyncSession
from sqlmodel import Session

from app.core.core_log import get_logger
from app.db.db_session import db_manager
from app.models.crypto.model_kline import Kline
from app.models.crypto.model_symbol import Symbol, ExchangeEnum
from app.models.crypto.model_ticker import Ticker
from app.utils.tz import ShanghaiTimeZone
from quant.exchange import ExchangeApiInterface
from quant.exchange.binance_endpoints import BinanceEndpoints
from quant.exchange.utils import HttpUtils

logger = get_logger("binance_sync")


class ContractType:
    """合约类型常量"""
    SPOT = "SPOT"  # 现货
    PERPETUAL = "PERPETUAL"  # 永续合约
    DELIVERY = "DELIVERY"  # 交割合约


class MarketType:
    """市场类型常量"""
    SPOT = "spot"
    USDT_FUTURE = "usdt_future"
    COIN_FUTURE = "coin_future"


class BinanceSync:
    """币安数据同步类"""
    EXCHANGE = ExchangeEnum.binance
    MarketType = MarketType

    @classmethod
    def get_symbols(cls, market_type: str) -> Dict[str, Any]:
        """获取交易对列表（同步版本）

        注意: 这是BinanceClient.get_symbols的同步版本
        """
        url = BinanceEndpoints.get_market_endpoint("exchange_info", market_type)
        return HttpUtils.sync_public_request("GET", url)

    @classmethod
    def get_ticker_24hr(cls, market_type: str, symbol: str = None) -> List[Dict[str, Any]]:
        url = BinanceEndpoints.get_market_endpoint("ticker_24hr", market_type)
        params = {}
        if symbol:
            params["symbol"] = symbol
        result = HttpUtils.sync_public_request("GET", url, params)
        # 确保结果是列表
        if not isinstance(result, list):
            result = [result]
        return result

    @classmethod
    @db_manager
    def sync_market_symbols(cls, market_type: str, session: Session = None) -> List[Dict[str, Any]]:
        """同步市场交易对信息"""
        # 获取交易所信息
        exchange_info = cls.get_symbols(market_type)
        symbol_list = []

        for d in exchange_info.get('symbols', []):
            # 解析过滤器信息
            amount_precision = 0
            price_precision = 0
            min_amount = 0
            min_cost = 1

            for x in d.get('filters', []):
                if x.get('filterType') == 'LOT_SIZE':
                    amount_precision = -Decimal(x.get('stepSize', '1')).as_tuple().exponent
                    min_amount = float(x.get('minQty', '0'))

                if x.get('filterType') == 'PRICE_FILTER':
                    price_precision = -Decimal(x.get('tickSize', '1')).as_tuple().exponent

                if x.get('filterType') == 'MIN_NOTIONAL':
                    # 最小下单价值
                    min_cost = float(x.get('minNotional') or x.get('notional') or 1)

            # 确定合约类型
            if market_type == "spot":
                contract_type = ContractType.SPOT
            else:
                if d.get('contractType') == ContractType.PERPETUAL:
                    contract_type = ContractType.PERPETUAL
                else:
                    contract_type = ContractType.DELIVERY

            # 记录交易对符号
            symbol_list.append(d.get('symbol'))
            symbol = Symbol(
                exchange=cls.EXCHANGE,
                market_type=market_type,
                symbol=d.get('symbol'),
                base_coin=d.get('baseAsset'),
                quote_coin=d.get('quoteAsset'),
                margin_coin=d.get('marginAsset') or d.get('quoteAsset'),
                trade_status=d.get('status', 'TRADING'),
                amount_precision=amount_precision,
                price_precision=price_precision,
                contract_type=contract_type,
                contract_val=d.get('contractSize', 1),
                min_amount=min_amount,
                min_cost=min_cost,
                list_time=pendulum.from_timestamp(d.get('onboardDate') / (1000 if len(str(d.get('onboardDate', 0))) > 10 else 1)) if d.get('onboardDate') else None,
                delivery_time=pendulum.from_timestamp(d.get('deliveryDate') / (1000 if len(str(d.get('deliveryDate', 0))) > 10 else 1)) if d.get('deliveryDate') else None,
            )
            symbol.merge_sync(session=session)
        logger.info(f"正在更新 {market_type.upper()} Symbol 信息，数量：{len(symbol_list)}，正在入库...")

        count = Symbol.logic_delete_where_sync(session=session, where=Symbol.symbol.notin_(symbol_list), market_type=market_type)
        logger.info(f"删除 {market_type.upper()} 市场中不存在的交易对，数量：{count}")
        session.commit()
        return symbol_list

    @classmethod
    @db_manager
    def sync_market_tickers(cls, market_type: str, session: Session = None):
        # 获取所有交易对的行情数据
        symbols = Symbol.filter_by_sync(
            exchange=cls.EXCHANGE,
            market_type=market_type,
            session=session
        )
        try:
            tickers = cls.get_ticker_24hr(market_type)
        except Exception as e:
            logger.error(f"获取 {market_type.upper()} 行情数据失败: {str(e)}")
            return []

        # 将数据转换为以symbol为键的字典，方便查找
        ticker_dict = {item.get('symbol'): item for item in tickers}
        count = 0
        # 更新每个交易对的行情数据
        for symbol_obj in symbols:
            ticker_info = ticker_dict.get(symbol_obj.symbol)
            if not ticker_info:
                continue
            ticker = Ticker(
                symbol_id=symbol_obj.id,
                open=float(ticker_info.get('openPrice', 0)),
                high=float(ticker_info.get('highPrice', 0)),
                low=float(ticker_info.get('lowPrice', 0)),
                close=float(ticker_info.get('lastPrice', 0)),
                volume=float(ticker_info.get('volume', 0)),
                count=int(ticker_info.get('count', 0))
            )
            session.merge(ticker)
            count += 1
        Ticker.delete_where_sync(session=session, where=Ticker.update_time < pendulum.now().subtract(hours=1))
        # 提交事务
        session.commit()
        logger.info(f"已更新 {market_type.upper()} 市场行情数据，数量: {count}")
        return None

    @classmethod
    @db_manager
    def get_kline(cls, market_type: str, symbol: str, timeframe: str, start_date: str, end_date: str = None, session: Session = None):
        """获取K线数据"""
        granularity = ExchangeApiInterface.parse_timeframe(timeframe)
        start_date = pendulum.parse(str(start_date)).in_timezone(ShanghaiTimeZone)  # 目标开始时间
        end_date = pendulum.parse(str(end_date)).in_timezone(ShanghaiTimeZone) if end_date else pendulum.now(ShanghaiTimeZone)  # 目标结束时间
        tag = f"{market_type.upper()}-{symbol}-{timeframe.upper()}"
        logger.info(f"获取 {tag} K线数据，开始时间：{start_date}，结束时间：{end_date}")
        limit = 1000 if market_type == cls.MarketType.SPOT else 1500
        symbol_obj = Symbol.get_by_sync(
            exchange=cls.EXCHANGE,
            market_type=market_type,
            symbol=symbol,
            session=session
        )
        if not symbol_obj:
            logger.error(f"{tag} 交易对不存在")
            return []
        tag = f"{symbol_obj.id} - {tag}"
        while (start_date < end_date) and (start_date < pendulum.now(ShanghaiTimeZone)):  # 如果开始时间大于结束时间，或者大于当前时间，结束
            next_date = min(start_date.add(seconds=granularity * limit), end_date)  # 计算下一阶段k线日期
            param = {
                'symbol': symbol,
                'startTime': int(start_date.timestamp() * 1000),
                'endTime': int(next_date.timestamp() * 1000),
                'interval': timeframe,
                'limit': limit
            }
            url = f"{BinanceEndpoints.get_market_endpoint("klines", market_type)}?{ExchangeApiInterface.param_to_string(param)}"
            data = HttpUtils.sync_public_request("GET", url)
            if len(data) == 0:
                logger.error(f"{tag} 时间：{start_date} - {next_date} ，没有获取到数据: {url}")
                start_date = next_date
                continue
            logger.info(
                f"成功获取 {tag} K线数据，数量：{len(data):>4}，时间段：{pendulum.from_timestamp(data[0][0] / 1000, tz=ShanghaiTimeZone)} - {pendulum.from_timestamp(data[-1][0] / 1000, tz=ShanghaiTimeZone)} {url if len(data) == 0 else ''}")
            for d in data:
                k = Kline(
                    symbol_id=symbol_obj.id,
                    timestamp=pendulum.from_timestamp(d[0] / 1000, tz=ShanghaiTimeZone),
                    open=d[1],
                    high=d[2],
                    low=d[3],
                    close=d[4],
                    count=d[8],
                    buy_volume=float(d[10]) * float(d[4]) / 10000 if market_type == cls.MarketType.COIN_FUTURE else float(d[10]) / 10000,
                    volume=float(d[7]) * float(d[4]) / 10000 if market_type == cls.MarketType.COIN_FUTURE else float(d[7]) / 10000
                )
                session.merge(k)
            session.commit()
            start_date = next_date
        logger.info(f"{tag} K线数据同步完成")
        return None

    @classmethod
    @db_manager
    def get_symbol_list_time(cls, market_type: str, symbol: str, session: AsyncSession = None):
        """获取交易对的上市时间"""
        logger.info(f"获取 {market_type.upper()}-{symbol} 上市时间")
        param = {
            'symbol': symbol,
            'startTime': int(pendulum.parse('2010-01-01').timestamp() * 1000),
            'endTime': int(pendulum.now().timestamp() * 1000),
            'interval': '1d',
            'limit': 500
        }
        url = f"{BinanceEndpoints.get_market_endpoint("klines", market_type)}?{ExchangeApiInterface.param_to_string(param)}"
        klines = HttpUtils.sync_public_request("GET", url)
        tag = f"{market_type.upper()}-{symbol}-1d"
        logger.info(f"获取 {tag} K线数据，开始时间：{pendulum.from_timestamp(klines[0][0] / 1000, tz=ShanghaiTimeZone)}，结束时间：{pendulum.from_timestamp(klines[-1][0] / 1000, tz=ShanghaiTimeZone)}")
        if not klines:
            logger.error(f'{tag}：没有找到起始时间')
            return None
        else:
            # 创建新的市场符号记录并保存到数据库
            list_time = pendulum.from_timestamp(klines[0][0] / 1000, tz=ShanghaiTimeZone)
            symbol_obj = Symbol(
                exchange=cls.EXCHANGE,
                market_type=market_type,
                symbol=symbol,
                list_time=list_time
            )
            symbol_obj.merge_sync(session=session, commit=True)
            logger.info(f"{tag} 上市时间：{list_time}")
            return list_time

    @classmethod
    @db_manager(autoflush=False)
    def sync_kline(cls, market_type: str, symbol: str, timeframe: str, start_date: str = None, end_date: str = None, restart=False, session: Session = None, **kwargs):
        """
        更新k线
        如果重新更新，仅需设置 restart
        如果部分更新，设置 start_date，如果没设置，将依据数据自动寻找最近的时间更新
        """
        if restart:
            start_date = cls.get_symbol_list_time(market_type, symbol, session=session)
            logger.info(f"重头开始更新 {market_type.upper()}-{symbol} {timeframe.upper()} K线数据，开始时间：{start_date}")
            return None
        else:
            """依次获取传入时间/最后一条数据时间/上市时间"""
            if not start_date:
                # 获取最后更新的K线时间
                symbol_obj = Symbol.get_by_sync(
                    exchange=cls.EXCHANGE,
                    market_type=market_type,
                    symbol=symbol,
                    session=session
                )
                if symbol_obj is None:
                    logger.error(f"{market_type.upper()}-{symbol} 交易对不存在")
                    return None
                klines = Kline.filter_by_sync(
                    symbol_id=symbol_obj.id,
                    order_by=Kline.timestamp.desc(),
                    limit=2,
                    session=session
                )
                if len(klines) == 0:
                    start_date = cls.get_symbol_list_time(market_type, symbol, session=session)
                else:
                    start_date = klines[1].timestamp
                logger.info(f"开始更新 {market_type.upper()}-{symbol} {timeframe.upper()} K线数据，开始时间：{start_date}")
                cls.get_kline(market_type=market_type, symbol=symbol, timeframe=timeframe, start_date=start_date, session=session)
                return start_date
            else:
                logger.info(f"开始更新 {market_type.upper()}-{symbol} {timeframe.upper()} K线数据，开始时间：{start_date}")
                cls.get_kline(market_type=market_type, symbol=symbol, timeframe=timeframe, start_date=start_date, session=session)
                return start_date

    @classmethod
    @db_manager
    def sync_all_symbols(cls, session: Session = None):
        """同步所有市场的交易对信息"""
        cls.sync_market_symbols("usdt_future", session=session)
        cls.sync_market_symbols("coin_future", session=session)
        cls.sync_market_symbols("spot", session=session)

    @classmethod
    @db_manager
    def sync_all_tickers(cls, session: Session = None):
        """同步所有市场的行情数据"""
        cls.sync_market_tickers("spot", session=session)
        cls.sync_market_tickers("usdt_future", session=session)
        cls.sync_market_tickers("coin_future", session=session)


if __name__ == '__main__':
    # 示例：同步现货市场的交易对信息
    binance_sync = BinanceSync()

    # binance_sync.sync_market_symbols(MarketType.SPOT)
    # binance_sync.sync_market_tickers(MarketType.SPOT)
    binance_sync.sync_kline(market_type='spot', symbol='BTCUSDT', timeframe='15m', start_date='2025-05-01')
