<template>
  <div class="flex items-center gap-1">
    <!-- 复制按钮 -->
    <button @click="handleCopy" class="action-btn" title="复制消息">
      <n-icon size="11" class="icon-default"><CopyOutline /></n-icon>
    </button>

    <!-- 编辑/重新生成按钮 -->
    <button v-if="messageType === 'user'" @click="$emit('edit')" class="action-btn" title="编辑消息">
      <n-icon size="11" class="icon-default"><CreateOutline /></n-icon>
    </button>
    <button v-else-if="messageType === 'agent'" @click="$emit('regenerate')" class="action-btn" title="重新生成">
      <n-icon size="11" class="icon-default"><RefreshOutline /></n-icon>
    </button>

    <!-- 删除按钮 -->
    <button @click="handleDelete" class="action-btn action-btn-delete" title="删除消息">
      <n-icon size="11" class="text-red-500"><TrashOutline /></n-icon>
    </button>
  </div>
</template>

<script lang="ts" setup>
import { NIcon } from 'naive-ui';
import { CopyOutline, CreateOutline, RefreshOutline, TrashOutline } from '@vicons/ionicons5';
import type { PlaygroundChatMessage } from '@/types/playground';
import { useMessageActions } from '@/composables/useMessageActions';

interface Props {
  message: PlaygroundChatMessage;
  messageType: 'user' | 'agent';
}

interface Emits {
  (e: 'edit'): void;
  (e: 'regenerate'): void;
}

const props = defineProps<Props>();
defineEmits<Emits>();

const { copyMessage, deleteMessage } = useMessageActions();

const handleCopy = () => copyMessage(props.message.content || '');
const handleDelete = () => deleteMessage(props.message, props.messageType);
</script>

<style scoped>
.action-btn {
  @apply flex h-5 w-5 items-center justify-center rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200;
}
.action-btn-delete {
  @apply hover:bg-red-50 dark:hover:bg-red-900/30;
}
.icon-default {
  @apply text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors;
}
</style>
