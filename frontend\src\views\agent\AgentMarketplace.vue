<template>
  <div class="bg-gray-50 dark:bg-dark transition-colors duration-300 py-16">
    <div class="container">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="agent in agents" :key="agent.id" class="feature-card p-6 rounded-lg bg-slate-100 dark:bg-slate-800/60 dark:shadow-lg transition-colors duration-300 group">
          <!-- 助手头像和标签 -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center">
              <div class="w-12 h-12 rounded-lg flex items-center justify-center mr-3 shadow-sm" :class="agent.avatarBg">
                <n-icon size="24" class="text-white">
                  <component :is="agent.icon" />
                </n-icon>
              </div>
              <div>
                <h3 class="text-lg font-semibold text-slate-900 dark:text-slate-100">{{ $t(`agents.marketplace.list.${agent.key}.name`) }}</h3>
                <p class="text-sm text-slate-600 dark:text-slate-400">{{ $t(`agents.marketplace.list.${agent.key}.category`) }}</p>
              </div>
            </div>
            <div class="flex items-center gap-1 bg-amber-50 dark:bg-amber-900/20 px-2 py-1 rounded-full">
              <n-icon size="14" class="text-amber-500">
                <StarOutline />
              </n-icon>
              <span class="text-sm text-amber-700 dark:text-amber-400 font-medium">{{ agent.rating }}</span>
            </div>
          </div>

          <!-- 助手描述 -->
          <p class="text-slate-700 dark:text-slate-300 mb-4 leading-relaxed">
            {{ $t(`agents.marketplace.list.${agent.key}.description`) }}
          </p>

          <!-- 特性标签 -->
          <div class="flex flex-wrap gap-2 mb-4">
            <span
              v-for="(tag, index) in getAgentTags(agent.key)"
              :key="`${agent.key}-${index}`"
              class="px-3 py-1 bg-slate-200/90 dark:bg-slate-700/50 text-slate-700 dark:text-slate-300 text-xs rounded-full backdrop-blur-sm"
            >
              {{ tag }}
            </span>
          </div>

          <!-- 使用统计 -->
          <div class="flex items-center justify-between text-sm text-slate-600 dark:text-slate-400 mb-4 bg-slate-100/80 dark:bg-slate-700/30 px-3 py-2 rounded-lg">
            <span>{{ agent.users }}+ {{ $t('agents.marketplace.labels.users') }}</span>
            <span>{{ agent.conversations }}+ {{ $t('agents.marketplace.labels.conversations') }}</span>
          </div>

          <!-- 操作按钮 -->
          <div class="flex gap-2">
            <button class="btn btn-primary flex-1 text-sm transition-all" @click="startChat(agent.id)">{{ $t('agents.marketplace.labels.startChat') }}</button>
            <button class="btn btn-outline text-sm px-4 transition-all" @click="viewDetails(agent.id)">{{ $t('agents.marketplace.labels.details') }}</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { NIcon, useMessage } from 'naive-ui';
import { useHead } from '@vueuse/head';
import { useI18n } from 'vue-i18n';
import { StarOutline } from '@vicons/ionicons5';
import { agentsData } from '@/data/agents';

const router = useRouter();
const message = useMessage();
const { t } = useI18n();

// 设置页面元数据
useHead({
  title: computed(() => `${t('agents.marketplace.title')} - YourAGen`),
  meta: [
    {
      name: 'description',
      content: computed(() => t('agents.marketplace.description')),
    },
  ],
});

const agents = agentsData;

// 获取助手标签的函数
const getAgentTags = (agentKey: string) => {
  return [t(`agents.marketplace.list.${agentKey}.tag1`), t(`agents.marketplace.list.${agentKey}.tag2`), t(`agents.marketplace.list.${agentKey}.tag3`)];
};

const startChat = (agentId: number) => {
  router.push(`/playground?agent=${agentId}`);
};

const viewDetails = (agentId: number) => {
  message.info(`查看助手 ${agentId} 的详细信息`);
};
</script>
