------------------------------------------
-- 交易所API密钥管理表
-- 此表存储用户访问各交易所API的凭证信息
------------------------------------------

CREATE TABLE crypto.exchange_apikey
(
    api_id      VARCHAR     NOT NULL
        PRIMARY KEY,                                               -- API标识符，主键
    user_id     VARCHAR,                                           -- 关联的用户ID
    exchange    VARCHAR     NOT NULL,                              -- 交易所名称
    api_key     VARCHAR     NOT NULL,                              -- 交易所提供的API Key
    secret_key  VARCHAR     NOT NULL,                              -- 交易所提供的Secret Key
    passphrase  VARCHAR,                                           -- 部分交易所需要的密码短语
    remark      VARCHAR,                                           -- 备注说明
    params      JSONB                DEFAULT '{}'::JSONB NOT NULL, -- 额外参数，存储为JSON格式
    is_delete   BOOLEAN              DEFAULT FALSE NOT NULL,       -- 是否已删除
    create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,    -- 创建时间
    update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP     -- 更新时间
);

-- 自动更新时间戳触发器
CREATE TRIGGER update_apikey_timestamp
    BEFORE UPDATE
    ON crypto.exchange_apikey
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();
