"""
交易所通用工具函数

提供HTTP请求、签名生成、时间戳获取等通用功能
"""
import hashlib
import hmac
import time
from typing import Dict, Any, Optional

import aiohttp
import requests

from app.core.core_config import settings
from app.core.core_log import get_logger

logger = get_logger("exchange_utils")


class HttpUtils:
    """HTTP请求工具类"""

    REQUEST_TIMEOUT = 15  # 请求超时时间(秒)

    @staticmethod
    def get_proxy_settings(url: str = None) -> Dict[str, str]:
        """从配置获取代理设置
        
        Args:
            url: 请求的URL，用于判断是否需要代理
            
        Returns:
            代理设置字典
        """
        # 检查是否需要跳过代理
        if url and "data-api.binance.vision" in url:
            return {}

        proxy_settings = {}

        # 从settings获取（settings已自动从环境变量加载）
        if settings.HTTP_PROXY:
            proxy_settings['http'] = settings.HTTP_PROXY
        if settings.HTTPS_PROXY:
            proxy_settings['https'] = settings.HTTPS_PROXY

        # 记录代理设置
        if proxy_settings:
            logger.info(f"使用代理设置: {proxy_settings}")
            if settings.NO_PROXY:
                logger.info(f"不使用代理的域名: {settings.NO_PROXY}")

        return proxy_settings

    @staticmethod
    async def public_request(method: str, url: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        proxy_settings = HttpUtils.get_proxy_settings(url)
        proxy = proxy_settings.get('https') if 'https://' in url else proxy_settings.get('http')

        method = method.upper()
        async with aiohttp.ClientSession() as session:
            try:
                if method == "GET":
                    async with session.get(url, params=params, proxy=proxy, timeout=HttpUtils.REQUEST_TIMEOUT) as response:
                        response.raise_for_status()
                        return await response.json()
                elif method == "POST":
                    async with session.post(url, json=params, proxy=proxy, timeout=HttpUtils.REQUEST_TIMEOUT) as response:
                        response.raise_for_status()
                        return await response.json()
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
            except Exception as e:
                logger.error(f"HTTP请求失败: {url}, 错误: {str(e)}")
                raise

    @staticmethod
    def sync_public_request(method: str, url: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        proxies = HttpUtils.get_proxy_settings(url)

        method = method.upper()
        try:
            if method == "GET":
                response = requests.get(url, params=params, proxies=proxies, timeout=HttpUtils.REQUEST_TIMEOUT)
            elif method == "POST":
                response = requests.post(url, json=params, proxies=proxies, timeout=HttpUtils.REQUEST_TIMEOUT)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"同步HTTP请求失败: {url}, 错误: {str(e)}")
            raise

    @staticmethod
    def create_signature(api_secret: str, payload: str, algorithm: str = "sha256") -> str:
        if algorithm.lower() == "sha256":
            return hmac.new(api_secret.encode("utf-8"), payload.encode("utf-8"), hashlib.sha256).hexdigest()
        else:
            raise ValueError(f"不支持的哈希算法: {algorithm}")

    @staticmethod
    def get_timestamp() -> int:
        """获取当前时间戳(毫秒)"""
        return int(time.time() * 1000)
