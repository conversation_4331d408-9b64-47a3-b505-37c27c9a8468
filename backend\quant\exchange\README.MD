# 交易所模块

## 项目结构

```
backend/quant/exchange/
├── __init__.py         # 模块入口，导出主要类
├── README.md           # 文档
├── base.py             # 基础接口定义
├── utils.py            # 工具函数
├── binance.py          # 币安交易所客户端
└── binance_ws.py       # 币安WebSocket客户端
```

## 使用方法

### 初始化客户端

```python
from quant.exchange import BinanceClient
from app.models import ApiKey, Symbol

# 创建客户端
client = BinanceClient(
    api=None,  # 可选，API密钥
    symbol=None,  # 可选，交易对
    market_type="spot"  # 市场类型: spot, usdt_future, coin_future
)
```

### REST API示例

```python
import asyncio


async def example():
    # 获取BTC/USDT行情
    ticker = await client.get_ticker("BTCUSDT")
    print(f"BTC价格: {ticker['price']}")

    # 获取K线数据
    klines = await client.get_klines("BTCUSDT", "1h", 10)
    for kline in klines:
        print(f"时间: {kline['open_time']}, 开盘价: {kline['open']}, 收盘价: {kline['close']}")

    # 获取账户信息（需要API密钥）
    if client.api:
        account = await client.get_account()
        print(f"账户余额: {account['balances']}")

    # 下单（需要API密钥）
    if client.api:
        order = await client.place_order(
            symbol="BTCUSDT",
            side="BUY",
            order_type="LIMIT",
            quantity=0.001,
            price=50000,
            timeInForce="GTC"
        )
        print(f"订单ID: {order['orderId']}")
```

### WebSocket API示例

```python
async def ticker_callback(data):
    print(f"收到行情: {data}")


async def ws_example():
    # 连接WebSocket
    await client.connect_ws()

    # 订阅行情
    await client.subscribe_ticker(["BTCUSDT", "ETHUSDT"], ticker_callback)

    # 等待10秒
    await asyncio.sleep(10)

    # 取消订阅
    await client.unsubscribe_ticker(["BTCUSDT", "ETHUSDT"])

    # 断开连接
    await client.disconnect_ws()
```

## API参考

### REST API

| 方法                                           | 说明     |
|----------------------------------------------|--------|
| `get_ticker(symbol)`                         | 获取行情数据 |
| `get_klines(symbol, interval, limit)`        | 获取K线数据 |
| `get_account()`                              | 获取账户信息 |
| `place_order(symbol, side, order_type, ...)` | 下单     |
| `cancel_order(symbol, order_id)`             | 取消订单   |
| `get_order(symbol, order_id)`                | 查询订单   |

### WebSocket API

| 方法                                    | 说明            |
|---------------------------------------|---------------|
| `connect_ws()`                        | 连接WebSocket   |
| `disconnect_ws()`                     | 断开WebSocket连接 |
| `subscribe_ticker(symbols, callback)` | 订阅行情          |
| `subscribe_kline(symbols, callback)`  | 订阅K线          |
| `subscribe_depth(symbols, callback)`  | 订阅深度          |
| `subscribe_trade(symbols, callback)`  | 订阅成交          |
| `unsubscribe_ticker(symbols)`         | 取消订阅行情        |
| `unsubscribe_kline(symbols)`          | 取消订阅K线        |
| `unsubscribe_depth(symbols)`          | 取消订阅深度        |
| `unsubscribe_trade(symbols)`          | 取消订阅成交        |