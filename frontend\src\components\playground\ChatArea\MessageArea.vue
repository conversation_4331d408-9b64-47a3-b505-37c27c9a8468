<template>
  <div class="relative flex h-full max-h-[calc(100vh-120px)] min-h-0 flex-grow flex-col">
    <!-- 消息容器 -->
    <div ref="scrollContainer" class="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent">
      <div class="mx-auto w-full max-w-4xl px-4 py-6">
        <!-- 消息列表 -->
        <div class="space-y-6">
          <Messages
            :messages="playgroundStore.messages"
            :system-status="{ ready: playgroundStore.isReady }"
            :selected-agent="playgroundStore.selectedAgentInfo"
            :is-streaming="playgroundStore.isStreaming"
            @send-suggestion="handleSendSuggestion"
            @open-sidebar="handleOpenSidebar"
          />
        </div>

        <!-- 流式传输指示器 -->
        <Transition name="streaming-indicator" mode="out-in">
          <div v-if="playgroundStore.isStreaming" class="mt-6 flex items-center justify-center">
            <div class="flex items-center gap-2 rounded-lg px-3 py-1.5 text-sm bg-slate-50 dark:bg-[#2d2d2d] text-gray-800 dark:text-[#e0e0e0]">
              <div class="flex gap-1">
                <div class="w-1.5 h-1.5 rounded-full bg-blue-400 animate-bounce" style="animation-delay: 0ms"></div>
                <div class="w-1.5 h-1.5 rounded-full bg-purple-400 animate-bounce" style="animation-delay: 150ms"></div>
                <div class="w-1.5 h-1.5 rounded-full bg-pink-400 animate-bounce" style="animation-delay: 300ms"></div>
              </div>
              <span class="font-medium">{{ $t('playground.messages.thinking') }}...</span>
            </div>
          </div>
        </Transition>

        <!-- 底部间距 -->
        <div class="h-6"></div>
      </div>
    </div>

    <!-- 回到底部按钮 -->
    <Transition name="scroll-button" mode="out-in">
      <button
        v-if="showScrollButton"
        @click="() => scrollToBottom()"
        class="absolute bottom-4 right-4 z-20 flex h-8 w-8 items-center justify-center rounded-full transition-all duration-200 hover:scale-110 bg-slate-50 dark:bg-[#2d2d2d] text-gray-700 dark:text-white hover:bg-slate-100 dark:hover:bg-[#3a3a3a]"
      >
        <n-icon size="14">
          <ChevronDownOutline />
        </n-icon>
      </button>
    </Transition>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { NIcon } from 'naive-ui';
import { ChevronDownOutline } from '@vicons/ionicons5';
import { useI18n } from 'vue-i18n';
import { usePlaygroundStore } from '@/stores/playground';
import Messages from './Messages.vue';

interface Emits {
  (e: 'send-suggestion', suggestion: string): void;
  (e: 'open-sidebar'): void;
}

const emit = defineEmits<Emits>();
const playgroundStore = usePlaygroundStore();
const scrollContainer = ref<HTMLElement>();
const showScrollButton = ref(false);
const { t } = useI18n();

// 滚动到底部
const scrollToBottom = (smooth = true) => {
  if (scrollContainer.value) {
    scrollContainer.value.scrollTo({
      top: scrollContainer.value.scrollHeight,
      behavior: smooth ? 'smooth' : 'auto',
    });
  }
};

// 检查是否需要显示回到底部按钮
const checkScrollPosition = () => {
  if (!scrollContainer.value) return;

  const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value;
  const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
  showScrollButton.value = !isNearBottom && scrollHeight > clientHeight;
};

// 监听消息变化自动滚动
watch(
  () => playgroundStore.messages,
  () => {
    nextTick(() => {
      // 如果用户在底部附近，自动滚动
      if (!showScrollButton.value) {
        scrollToBottom();
      }
    });
  },
  { deep: true }
);

// 监听流式传输状态
watch(
  () => playgroundStore.isStreaming,
  isStreaming => {
    if (isStreaming) {
      nextTick(() => {
        scrollToBottom();
      });
    }
  }
);

const handleSendSuggestion = (suggestion: string) => {
  emit('send-suggestion', suggestion);
};

const handleOpenSidebar = () => {
  emit('open-sidebar');
};

onMounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', checkScrollPosition);
    // 初始检查
    checkScrollPosition();
  }
});

onUnmounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', checkScrollPosition);
  }
});
</script>

<style scoped>
/* 动画 */
.streaming-indicator-enter-active,
.streaming-indicator-leave-active,
.scroll-button-enter-active,
.scroll-button-leave-active {
  transition: all 0.3s ease-in-out;
}

.streaming-indicator-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.9);
}

.streaming-indicator-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(1.1);
}

.scroll-button-enter-from {
  opacity: 0;
  transform: scale(0.8) translateY(10px);
}

.scroll-button-leave-to {
  opacity: 0;
  transform: scale(1.2) translateY(-10px);
}

/* 自定义滚动条 */
.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}
.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(209 213 219);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}
.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(156 163 175);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(75 85 99);
}
.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(107 114 128);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .max-w-4xl {
    max-width: 100%;
  }
  .px-4 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}
</style>
