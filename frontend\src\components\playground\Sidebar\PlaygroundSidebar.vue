<template>
  <aside
    class="relative flex h-screen shrink-0 grow-0 flex-col overflow-hidden px-3 py-4 transition-all duration-300 ease-in-out backdrop-blur-sm bg-white dark:bg-[#0a0a0a]"
    :class="isCollapsed ? 'w-12' : 'w-68'"
    :style="{
      minWidth: isCollapsed ? '3rem' : '17rem',
      maxWidth: isCollapsed ? '3rem' : '17rem',
    }"
  >
    <!-- 微妙的纹理叠加 -->
    <div
      class="absolute inset-0 opacity-[0.02] pointer-events-none"
      style="background-image: radial-gradient(circle at 1px 1px, rgba(0, 0, 0, 0.1) 1px, transparent 0); background-size: 20px 20px"
    ></div>

    <!-- 头部 - 标题与折叠按钮水平布局 -->
    <div class="flex items-center justify-between mb-6 relative z-10">
      <!-- 标题区域 - 可点击跳转首页 -->
      <div
        v-if="!isCollapsed"
        @click="goToHome"
        class="flex items-center gap-3 cursor-pointer hover:opacity-90 transition-all duration-200 group min-w-0 select-none"
        role="button"
        tabindex="0"
        @keydown.enter="goToHome"
        @keydown.space="goToHome"
        @mousedown.prevent.stop
        @selectstart.prevent
        @dragstart.prevent
        style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
      >
        <div
          class="flex h-6 w-6 items-center justify-center rounded-lg shadow-md group-hover:shadow-lg transition-all duration-200 group-hover:scale-105 relative overflow-hidden shrink-0 bg-gray-800 dark:bg-[#1a1a1a]"
        >
          <n-icon size="14" class="text-white relative z-10 select-none">
            <RocketOutline />
          </n-icon>
        </div>
        <span
          class="text-sm font-bold text-gray-900 dark:text-white tracking-wide truncate select-none"
          style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
          >{{ $t('playground.sidebar.agent') }}</span
        >
      </div>

      <!-- 折叠/展开按钮 -->
      <button
        @click="toggleCollapse"
        class="h-6 w-6 flex items-center justify-center rounded-lg transition-all duration-200 hover:scale-105 shrink-0 group shadow-sm hover:shadow-md relative overflow-hidden bg-gray-100 hover:bg-gray-200 dark:bg-[#1a1a1a] dark:hover:bg-[#2a2a2a]"
        :aria-label="isCollapsed ? $t('playground.sidebar.expandLabel') : $t('playground.sidebar.collapseLabel')"
      >
        <n-icon
          size="14"
          :class="[
            'transition-all duration-300 text-gray-700 group-hover:text-gray-900 dark:text-gray-300 dark:group-hover:text-white relative z-10',
            isCollapsed ? 'rotate-180' : 'rotate-0',
          ]"
        >
          <MenuOutline />
        </n-icon>
      </button>
    </div>

    <!-- 侧边栏内容 -->
    <Transition name="sidebar-content" mode="out-in">
      <div v-if="!isCollapsed" class="w-full space-y-5 transition-all duration-300 flex flex-col h-full relative z-10 min-w-0" style="overflow-x: hidden">
        <!-- 新对话按钮 -->
        <div class="relative min-w-0 mx-1">
          <button
            @click="handleNewChatWithUrl"
            @mousedown.prevent.stop
            @selectstart.prevent
            @dragstart.prevent
            :disabled="!stableHasMessages"
            class="w-full h-8 rounded-lg transition-all duration-200 font-medium disabled:opacity-50 disabled:pointer-events-none select-none relative overflow-hidden"
            :class="
              !stableHasMessages
                ? 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-[#1a1a1a] dark:text-[#888] dark:hover:bg-[#2a2a2a]'
                : 'bg-gray-800 text-white hover:bg-gray-900 dark:bg-white dark:text-gray-800 dark:hover:bg-gray-100'
            "
            style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
          >
            <div class="flex items-center justify-center gap-2">
              <n-icon size="14" :class="stableHasMessages ? 'text-white dark:text-gray-700' : 'text-gray-700 dark:text-gray-300'" class="select-none relative z-10">
                <AddOutline />
              </n-icon>
              <span class="text-xs font-semibold transform-gpu select-none relative z-10 truncate">{{ $t('playground.sidebar.newChat') }}</span>
            </div>
          </button>
        </div>

        <!-- 分隔线 -->
        <div class="relative mx-1">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full h-px bg-gray-200 dark:bg-[#222]"></div>
          </div>
          <div class="relative flex justify-center">
            <div class="px-3 bg-white dark:bg-[#0a0a0a]">
              <div class="w-1.5 h-1.5 rounded-full bg-gray-500 dark:bg-[#444]"></div>
            </div>
          </div>
        </div>

        <!-- 智能体选择 -->
        <Transition name="fade-slide" mode="out-in">
          <div class="space-y-3 py-1 select-none min-w-0" style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none">
            <div
              class="text-xs font-bold uppercase text-gray-900 dark:text-white tracking-wider px-1 select-none flex items-center gap-2 min-w-0"
              style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
            >
              <span class="truncate select-none" style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none">{{
                $t('playground.sidebar.agents')
              }}</span>
              <div class="flex-1 h-px bg-gradient-to-r from-gray-300/60 dark:from-gray-600/30 to-transparent"></div>
            </div>

            <!-- 加载状态 -->
            <div v-if="isEndpointLoading" class="space-y-2">
              <div class="animate-pulse">
                <div class="h-8 rounded-lg shadow-md bg-gray-100 dark:bg-[#2d2d2d]"></div>
              </div>
              <div class="animate-pulse delay-75">
                <div class="h-8 rounded-lg shadow-md bg-gray-100 dark:bg-[#2d2d2d]"></div>
              </div>
            </div>

            <!-- 智能体选择器 -->
            <template v-else>
              <AgentSelector />

              <!-- 智能体描述 -->
              <Transition name="slide-up" mode="out-in">
                <div
                  v-if="selectedAgentId && selectedAgentInfo"
                  class="mx-1 mb-1 px-2 py-1 rounded-md backdrop-blur-sm overflow-hidden min-w-0 select-none session-card bg-white dark:bg-[#2d2d2d] hover:bg-gray-50 dark:hover:bg-[#3a3a3a] transition-all duration-200"
                  style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
                  @mousedown.prevent.stop
                  @selectstart.prevent
                  @dragstart.prevent
                >
                  <n-tooltip trigger="hover" placement="right" :show-arrow="true" :delay="300" :duration="200" :z-index="9999">
                    <template #trigger>
                      <p
                        class="text-xs font-normal leading-tight select-none text-gray-800 dark:text-gray-100 cursor-help"
                        style="
                          user-select: none;
                          -webkit-user-select: none;
                          -moz-user-select: none;
                          -ms-user-select: none;
                          display: -webkit-box;
                          -webkit-line-clamp: 2;
                          -webkit-box-orient: vertical;
                          overflow: hidden;
                          text-overflow: ellipsis;
                        "
                      >
                        {{ selectedAgentInfo.description || 'No description available' }}
                      </p>
                    </template>
                    <div class="max-w-sm p-2 text-xs leading-relaxed">
                      {{ selectedAgentInfo.description || 'No description available' }}
                    </div>
                  </n-tooltip>
                </div>
              </Transition>
            </template>
          </div>
        </Transition>

        <!-- 会话列表 - 占据剩余空间 -->
        <div class="flex-1 flex flex-col min-h-0 min-w-0 overflow-hidden">
          <Transition name="fade-slide" mode="out-in">
            <SessionsList />
          </Transition>
        </div>
      </div>
    </Transition>

    <!-- 折叠状态下的简化显示 -->
    <Transition name="collapsed-content" mode="out-in">
      <div v-if="isCollapsed" class="flex flex-col items-center gap-4 pt-8 relative z-10">
        <div
          @click="goToHome"
          class="flex h-6 w-6 items-center justify-center rounded-lg cursor-pointer transition-all duration-200 hover:scale-105 relative overflow-hidden shadow-md hover:shadow-lg bg-gray-800 hover:bg-gray-900 dark:bg-[#1a1a1a] dark:hover:bg-[#2a2a2a]"
          role="button"
          tabindex="0"
          @keydown.enter="goToHome"
          @keydown.space="goToHome"
          :aria-label="$t('playground.sidebar.homeLabel')"
        >
          <n-icon size="14" class="text-white relative z-10">
            <RocketOutline />
          </n-icon>
        </div>
        <div class="w-1.5 h-1.5 rounded-full bg-green-400 shadow-lg shadow-green-400/50 animate-pulse"></div>
      </div>
    </Transition>
  </aside>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { NIcon, NTooltip } from 'naive-ui';
import { AddOutline, MenuOutline, RocketOutline } from '@vicons/ionicons5';
import { usePlaygroundStore } from '@/stores/playground';
import { useStreamHandler } from '@/composables/useStreamHandler';
import AgentSelector from './AgentSelector.vue';
import SessionsList from './SessionsList.vue';

const router = useRouter();
const playgroundStore = usePlaygroundStore();
const { initializePlayground, handleNewChat } = useStreamHandler();

const isCollapsed = ref(false);
const stableHasMessages = ref(false);

const hasMessages = computed(() => playgroundStore.hasMessages);
const isEndpointLoading = computed(() => playgroundStore.isEndpointLoading);
const selectedAgentId = computed(() => playgroundStore.selectedAgent);

const selectedAgentInfo = computed(() => {
  return playgroundStore.agents.find((a: any) => a.value === selectedAgentId.value);
});

let messageUpdateTimeout: NodeJS.Timeout | null = null;
watch(
  hasMessages,
  newValue => {
    if (messageUpdateTimeout) {
      clearTimeout(messageUpdateTimeout);
    }

    if (newValue) {
      stableHasMessages.value = true;
    } else {
      messageUpdateTimeout = setTimeout(() => {
        stableHasMessages.value = false;
      }, 100);
    }
  },
  { immediate: true }
);

const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

const goToHome = () => {
  router.push({ name: 'Home' });
};

const handleNewChatWithUrl = async () => {
  // 调用原有的handleNewChat逻辑
  handleNewChat();
  
  // 更新URL参数，移除session-id但保留agent-id
  const selectedAgentId = playgroundStore.selectedAgent;
  if (selectedAgentId) {
    try {
      await router.push({
        name: 'Playground',
        query: { 'agent-id': selectedAgentId },
      });
    } catch (error) {
      console.log('Router navigation handled:', error);
    }
  } else {
    // 如果没有选中的智能体，跳转到纯净的playground页面
    try {
      await router.push({ name: 'Playground' });
    } catch (error) {
      console.log('Router navigation handled:', error);
    }
  }
};

onMounted(async () => {
  await initializePlayground();
});

onUnmounted(() => {
  if (messageUpdateTimeout) {
    clearTimeout(messageUpdateTimeout);
  }
});
</script>

<style scoped>
/* 过渡动画统一配置 */
.sidebar-content-enter-active,
.sidebar-content-leave-active,
.collapsed-content-enter-active,
.collapsed-content-leave-active,
.fade-slide-enter-active,
.fade-slide-leave-active,
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 侧边栏内容过渡 */
.sidebar-content-enter-from,
.sidebar-content-leave-to {
  opacity: 0;
  transform: translateX(-30px) scale(0.95);
}

/* 折叠内容过渡 */
.collapsed-content-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.9);
}

.collapsed-content-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.9);
}

/* 滑动过渡 */
.fade-slide-enter-from,
.slide-up-enter-from {
  opacity: 0;
  transform: translateY(25px) scale(0.95);
}

.fade-slide-leave-to,
.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-25px) scale(0.95);
}
</style>
