import asyncio
import json
from datetime import datetime

import websockets
from binance.client import Client

# 初始化客户端
client = Client()

# WebSocket的URL（使用data-api.binance.vision）
SPOT_WS_URL = "wss://data-stream.binance.vision/ws"  # 尝试使用data-api对应的WebSocket域名
FUTURES_WS_URL = "wss://data-stream.binance.vision/futures/um/ws"  # 尝试使用data-api对应的U本位合约WebSocket域名


# 数据处理函数
def process_message(message):
    data = json.loads(message)
    if "e" in data:
        if data["e"] == "24hrTicker":
            print(f"现货 {data['s']} 当前价格: {data['c']}, 24h涨跌幅: {data['P']}%")
        elif data["e"] == "kline":
            k = data["k"]
            print(f"K线 {data['s']}, 时间: {datetime.fromtimestamp(k['t'] / 1000)}, 开: {k['o']}, 高: {k['h']}, 低: {k['l']}, 收: {k['c']}")
        elif data["e"] == "markPriceUpdate":
            print(f"标记价格 {data['s']}: {data['p']}, 资金费率: {data['r']}")
    else:
        print(f"收到数据: {message}")


# 现货WebSocket连接函数
async def connect_spot_websocket():
    print("正在连接现货WebSocket (data-api)...")

    # 设置订阅的Streams
    streams = [
        "btcusdt@ticker",  # 24小时价格变动
        "btcusdt@kline_1m"  # 1分钟K线
    ]
    ws_url = f"{SPOT_WS_URL}/{'/'.join(streams)}"

    try:
        print(f"尝试连接: {ws_url}")
        async with websockets.connect(ws_url) as websocket:
            print("现货WebSocket已连接")
            try:
                while True:
                    message = await websocket.recv()
                    process_message(message)
            except Exception as e:
                print(f"现货WebSocket接收错误: {e}")
    except Exception as e:
        print(f"现货WebSocket连接错误: {e}")
        print("尝试使用标准WebSocket端点...")
        await connect_fallback_spot_websocket()


# 标准现货WebSocket连接作为备用
async def connect_fallback_spot_websocket():
    standard_ws_url = "wss://stream.binance.com:9443/ws"
    print(f"尝试连接标准端点: {standard_ws_url}")

    # 设置订阅的Streams
    streams = [
        "btcusdt@ticker",  # 24小时价格变动
        "btcusdt@kline_1m"  # 1分钟K线
    ]
    ws_url = f"{standard_ws_url}/{'/'.join(streams)}"

    async with websockets.connect(ws_url) as websocket:
        print("现货WebSocket已连接(使用标准端点)")
        try:
            while True:
                message = await websocket.recv()
                process_message(message)
        except Exception as e:
            print(f"现货WebSocket错误: {e}")


# U本位合约WebSocket连接函数
async def connect_futures_websocket():
    print("正在连接U本位合约WebSocket (data-api)...")

    # 设置订阅的Streams
    streams = [
        "btcusdt@ticker",  # 24小时价格变动
        "btcusdt@kline_1m",  # 1分钟K线
        "btcusdt@markPrice@1s"  # 标记价格(1秒更新)
    ]
    ws_url = f"{FUTURES_WS_URL}/{'/'.join(streams)}"

    try:
        print(f"尝试连接: {ws_url}")
        async with websockets.connect(ws_url) as websocket:
            print("U本位合约WebSocket已连接")
            try:
                while True:
                    message = await websocket.recv()
                    process_message(message)
            except Exception as e:
                print(f"U本位合约WebSocket接收错误: {e}")
    except Exception as e:
        print(f"U本位合约WebSocket连接错误: {e}")
        print("尝试使用标准WebSocket端点...")
        await connect_fallback_futures_websocket()


# 标准U本位合约WebSocket连接作为备用
async def connect_fallback_futures_websocket():
    standard_ws_url = "wss://fstream.binance.com/ws"
    print(f"尝试连接标准端点: {standard_ws_url}")

    # 设置订阅的Streams
    streams = [
        "btcusdt@ticker",  # 24小时价格变动
        "btcusdt@kline_1m",  # 1分钟K线
        "btcusdt@markPrice@1s"  # 标记价格(1秒更新)
    ]
    ws_url = f"{standard_ws_url}/{'/'.join(streams)}"

    async with websockets.connect(ws_url) as websocket:
        print("U本位合约WebSocket已连接(使用标准端点)")
        try:
            while True:
                message = await websocket.recv()
                process_message(message)
        except Exception as e:
            print(f"U本位合约WebSocket错误: {e}")


# 组合运行两个WebSocket连接
async def run_websockets():
    # 创建两个任务
    spot_task = asyncio.create_task(connect_spot_websocket())
    futures_task = asyncio.create_task(connect_futures_websocket())

    # 同时运行两个任务
    await asyncio.gather(spot_task, futures_task)


# 主函数
if __name__ == "__main__":
    try:
        # 在Windows上需要使用这个事件循环策略
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        # 运行WebSocket客户端
        asyncio.run(run_websockets())
    except KeyboardInterrupt:
        print("程序已停止")
    except Exception as e:
        print(f"发生错误: {e}")
