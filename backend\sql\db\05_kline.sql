------------------------------------------
-- 创建kline模式
------------------------------------------
CREATE SCHEMA IF NOT EXISTS kline;

------------------------------------------
-- K线数据表定义
------------------------------------------

-- 创建K线数据表：存储各个交易对在不同时间周期下的K线价格数据
CREATE TABLE kline.kline
(
    symbol_id  SMALLINT         NOT NULL,  -- 关联的交易对ID
    timestamp  TIMESTAMPTZ      NOT NULL,  -- K线时间戳
    open       DOUBLE PRECISION NOT NULL,  -- 开盘价
    high       DOUBLE PRECISION NOT NULL,  -- 最高价
    low        DOUBLE PRECISION NOT NULL,  -- 最低价
    close      DOUBLE PRECISION NOT NULL,  -- 收盘价
    volume     DOUBLE PRECISION NOT NULL,  -- 成交量
    count      INTEGER          DEFAULT 0, -- 成交笔数
    buy_volume DOUBLE PRECISION DEFAULT 0, -- 买方成交量
    PRIMARY KEY (symbol_id, timestamp)
) WITH (fillfactor = 90);

-- 创建索引
CREATE INDEX idx_kline_symbol_timestamp ON kline.kline (symbol_id, timestamp DESC);
CREATE INDEX idx_kline_timestamp ON kline.kline (timestamp DESC);

------------------------------------------
-- TimescaleDB设置
------------------------------------------

-- 安装必要的扩展
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;
CREATE EXTENSION IF NOT EXISTS timescaledb_toolkit CASCADE;

-- 配置为时间序列表

SELECT create_hypertable('kline.kline', 'timestamp', 'symbol_id', 3, migrate_data := true);

-- 配置为时间序列表，使用更明确的语法
-- SELECT create_hypertable(
--     'kline.kline',
--     'timestamp',
--     chunk_time_interval => INTERVAL '3 days',
--     partitioning_column => 'symbol_id',
--     number_partitions => 64,
--     create_default_indexes => FALSE,
--     if_not_exists => TRUE,
--     migrate_data => TRUE
-- );

-- -- 设置压缩策略（修复语法）
-- ALTER TABLE kline.kline SET (
--     timescaledb.compress = true,
--     timescaledb.compress_segmentby = 'symbol_id'
-- );
--
-- -- 添加压缩策略
-- SELECT add_compression_policy('kline.kline', INTERVAL '30 days');
--
-- -- 设置数据保留策略
-- SELECT add_retention_policy('kline.kline', INTERVAL '5 year');

------------------------------------------
-- 物化视图创建函数与实现
------------------------------------------
DROP VIEW IF EXISTS kline.kline_15m CASCADE;

-- 创建基础15m视图（基础K线数据周期）
CREATE VIEW kline.kline_15m AS
SELECT *
FROM kline.kline;