from typing import Any, Dict, Sequence, Type, TypeVar, List, Union

from loguru import logger
from sqlalchemy import Table, delete, func, select, update
from sqlalchemy.engine import Row
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from sqlalchemy.sql.expression import ColumnElement
from sqlmodel import SQLModel

from app.db.db_session import db_manager

T = TypeVar('T', bound=SQLModel)


def build_query(statement, filters=None, where=None, order_by=None, limit: int | None = None, offset: int | None = None):
    """构建查询语句"""
    if filters is None:
        filters = {}
    statement = statement.filter_by(**filters)
    if where is None:
        where = ()
    if isinstance(where, tuple):
        statement = statement.where(*where)
    else:
        statement = statement.where(where)

    if order_by is not None:
        if isinstance(order_by, Sequence) and not isinstance(order_by, (str, bytes)):
            statement = statement.order_by(*order_by)
        else:
            statement = statement.order_by(order_by)

    if limit is not None:
        statement = statement.limit(limit)

    if offset is not None:
        statement = statement.offset(offset)

    return statement


class QueryMethods:
    """可用于SQLAlchemy模型的查询方法"""

    @staticmethod
    @db_manager
    async def get(model: Type[T], session: AsyncSession = None, where=None, order_by=None, raise_error: bool = False, debug: bool = False, **filters) -> T | None:
        """获取符合条件的单个记录"""
        statement = select(model)
        statement = build_query(statement, filters, where, order_by)
        if debug:
            compiled_sql = statement.compile(compile_kwargs={"literal_binds": True})
            logger.info(compiled_sql)
        if isinstance(model, Table):
            result = await session.execute(statement)
        else:
            result = await session.scalars(statement)

        return result.one() if raise_error else result.one_or_none()

    @staticmethod
    @db_manager
    def get_sync(model: Type[T], session: Session = None, where=None, order_by=None, raise_error: bool = False, debug: bool = False, **filters) -> T | None:
        """get方法的同步版本"""
        statement = select(model)
        statement = build_query(statement, filters, where, order_by)
        if debug:
            compiled_sql = statement.compile(compile_kwargs={"literal_binds": True})
            logger.info(compiled_sql)

        if isinstance(model, Table):
            result = session.execute(statement)
        else:
            result = session.scalars(statement)

        return result.one() if raise_error else result.one_or_none()

    @staticmethod
    @db_manager
    async def filter(model: Union[Type[T], Table, Sequence[ColumnElement]], session: AsyncSession = None, where=None, order_by=None, limit: int | None = None, offset: int | None = None, debug: bool = False, **filters) -> Union[
        List[T], List[Row]]:
        """获取符合条件的所有记录"""
        if isinstance(model, (tuple, list)):
            statement = select(*model)
        else:
            statement = select(model)
        statement = build_query(statement, filters, where, order_by, limit, offset)
        if debug:
            compiled_sql = statement.compile(compile_kwargs={"literal_binds": True})
            logger.info(compiled_sql)

        if isinstance(model, (Table, tuple, list)):
            result = await session.execute(statement)
        else:
            result = await session.scalars(statement)

        return result.all()

    @staticmethod
    @db_manager
    def filter_sync(model: Union[Type[T], Table, Sequence[ColumnElement]], session: Session = None, where=None, order_by=None, limit: int | None = None, offset: int | None = None, debug: bool = False, **filters) -> Union[
        List[T], List[Row]]:
        """filter方法的同步版本"""
        if isinstance(model, (tuple, list)):
            statement = select(*model)
        else:
            statement = select(model)
        statement = build_query(statement, filters, where, order_by, limit, offset)

        if debug:
            compiled_sql = statement.compile(compile_kwargs={"literal_binds": True})
            logger.info(compiled_sql)

        if isinstance(model, (Table, tuple, list)):
            result = session.execute(statement)
        else:
            result = session.scalars(statement)

        return result.all()

    @staticmethod
    @db_manager
    async def count(model: Type[T], session: AsyncSession = None, where=None, debug: bool = False, **filters) -> int:
        if hasattr(model, '__table__'):
            statement = select(func.count()).select_from(model.__table__)
        else:
            statement = select(func.count()).select_from(model)
        statement = build_query(statement, filters, where)
        if debug:
            compiled_sql = statement.compile(compile_kwargs={"literal_binds": True})
            logger.info(compiled_sql)
        return await session.scalar(statement) or 0

    @staticmethod
    @db_manager
    def count_sync(model: Type[T], session: Session = None, where=None, debug: bool = False, **filters) -> int:
        if hasattr(model, '__table__'):
            statement = select(func.count()).select_from(model.__table__)
        else:
            statement = select(func.count()).select_from(model)
        statement = build_query(statement, filters, where)
        if debug:
            compiled_sql = statement.compile(compile_kwargs={"literal_binds": True})
            logger.info(compiled_sql)
        return session.scalar(statement) or 0

    @staticmethod
    @db_manager
    async def page_query(model: Type[T], session: AsyncSession = None, where=None, order_by=None, limit: int = None, offset: int = None, **filters) -> Dict[str, Any]:
        """带总数和数据的分页查询"""
        return {"total_count": await QueryMethods.count(model, session=session, where=where, **filters),
                "data": await QueryMethods.filter(model, session=session, where=where, order_by=order_by, limit=limit, offset=offset, **filters)}

    @staticmethod
    @db_manager
    def page_query_sync(model: Type[T], session: Session = None, where=None, order_by=None, limit: int = None, offset: int = None, **filters) -> Dict[str, Any]:
        """page_query方法的同步版本"""
        return {"total_count": QueryMethods.count_sync(model, session=session, where=where, **filters),
                "data": QueryMethods.filter_sync(model, session=session, where=where, order_by=order_by, limit=limit, offset=offset, **filters)}


class SchemaBase(SQLModel):
    """具有常见CRUD操作的基本SQLModel类"""

    class Config:
        arbitrary_types_allowed = True
        use_enum_values = True
        exclude_unset = True
        from_attributes = True

    def del_unset_attrs(self):
        """删除未设置的属性"""
        for key, value in self.model_dump().items():
            if value is None:
                delattr(self, key)
        return self

    @db_manager
    async def save(self: T, commit=False, refresh=False, session: AsyncSession = None) -> T:
        self.del_unset_attrs()
        session.add(self)
        if commit:
            await session.commit()
        if refresh:
            await session.refresh(self)
        return self

    @db_manager
    def save_sync(self: T, commit=False, refresh=False, session: Session = None) -> T:
        self.del_unset_attrs()
        session.add(self)
        if commit:
            session.commit()
        if refresh:
            session.refresh(self)
        return self

    @db_manager
    async def merge(self: T, commit=False, refresh=False, session: AsyncSession = None) -> T:
        self.del_unset_attrs()
        await session.merge(self)
        if commit:
            await session.commit()
        if refresh:
            await session.refresh(self)
        return self

    @db_manager
    def merge_sync(self: T, commit=False, refresh=False, session: Session = None) -> T:
        self.del_unset_attrs()
        session.merge(self)
        if commit:
            session.commit()
        if refresh:
            session.refresh(self)
        return self

    @classmethod
    @db_manager
    async def delete_where(cls: Type[T], session: AsyncSession = None, where=None, **filters) -> int:
        """删除符合条件的记录"""
        if not filters and where is None:
            raise ValueError("删除条件不能为空")

        statement = delete(cls)
        statement = build_query(statement, filters, where)
        result = await session.execute(statement)
        return result.rowcount()

    @classmethod
    @db_manager
    def delete_where_sync(cls: Type[T], session: Session = None, where=None, **filters) -> int:
        """删除符合条件的记录（同步版）"""
        if not filters and where is None:
            raise ValueError("删除条件不能为空")

        statement = delete(cls)
        statement = build_query(statement, filters, where)
        result = session.execute(statement)
        return result.rowcount

    @classmethod
    @db_manager
    async def update_where(cls: Type[T], values: Dict[str, Any], session: AsyncSession = None, where=None, **filters) -> int:
        """更新符合条件的记录"""
        if not filters and where is None:
            raise ValueError("更新条件不能为空")

        statement = update(cls)
        statement = build_query(statement, filters, where)
        statement = statement.values(**values)
        result = await session.execute(statement)
        return result.rowcount()

    @classmethod
    @db_manager
    def update_where_sync(cls: Type[T], values: Dict[str, Any], session: Session = None, where=None, **filters) -> int:
        """更新符合条件的记录（同步版）"""
        if not filters and where is None:
            raise ValueError("更新条件不能为空")

        statement = update(cls)
        statement = build_query(statement, filters, where)
        statement = statement.values(**values)
        result = session.execute(statement)
        return result.rowcount

    # 查询快捷方式
    @classmethod
    async def get_by(cls: Type[T], **kwargs) -> T | None:
        """按条件获取单个记录"""
        return await QueryMethods.get(cls, **kwargs)

    @classmethod
    def get_by_sync(cls: Type[T], **kwargs) -> T | None:
        """按条件获取单个记录（同步版）"""
        return QueryMethods.get_sync(cls, **kwargs)

    @classmethod
    async def filter_by(cls: Type[T], **kwargs) -> List[T] | Sequence[T]:
        """按条件查找记录"""
        return await QueryMethods.filter(cls, **kwargs)

    @classmethod
    def filter_by_sync(cls: Type[T], **kwargs) -> List[T] | Sequence[T]:
        """按条件查找记录（同步版）"""
        return QueryMethods.filter_sync(cls, **kwargs)

    @classmethod
    async def count_by(cls: Type[T], **kwargs) -> int:
        """按条件计数记录"""
        return await QueryMethods.count(cls, **kwargs)

    @classmethod
    def count_by_sync(cls: Type[T], **kwargs) -> int:
        """按条件计数记录（同步版）"""
        return QueryMethods.count_sync(cls, **kwargs)

    @classmethod
    async def page_by(cls: Type[T], **kwargs) -> Dict[str, Any]:
        """按条件分页查询"""
        return await QueryMethods.page_query(cls, **kwargs)

    @classmethod
    def page_by_sync(cls: Type[T], **kwargs) -> Dict[str, Any]:
        """按条件分页查询（同步版）"""
        return QueryMethods.page_query_sync(cls, **kwargs)

    @classmethod
    @db_manager
    async def logic_delete_where(cls: Type[T], session: AsyncSession = None, where=None, **filters):
        """逻辑删除符合条件的记录（异步版）"""
        if not filters and where is None:
            raise ValueError("删除条件不能为空")

        statement = update(cls)
        statement = build_query(statement, filters, where)
        statement = statement.values({'is_deleted': True})
        result = await session.execute(statement)
        return result.rowcount()

    @classmethod
    @db_manager
    def logic_delete_where_sync(cls: Type[T], session: Session = None, where=None, **filters):
        """逻辑删除符合条件的记录（同步版）"""
        if not filters and where is None:
            raise ValueError("删除条件不能为空")

        statement = update(cls)
        statement = build_query(statement, filters, where)
        statement = statement.values({'is_deleted': True})
        result = session.execute(statement)
        return result.rowcount
