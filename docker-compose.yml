services:
  db:
    image: timescale/timescaledb-ha:pg17
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      PGPORT: ${DB_PORT}
      POSTGRES_INITDB_ARGS: "--data-checksums"
    command: [
      "postgres",
      "-c", "autovacuum=on",
      "-c", "autovacuum_max_workers=5",
      "-c", "autovacuum_naptime=10",
      "-c", "autovacuum_vacuum_threshold=50",
      "-c", "checkpoint_completion_target=0.9",
      "-c", "default_statistics_target=500",
      "-c", "dynamic_shared_memory_type=posix",
      "-c", "effective_cache_size=4GB",
      "-c", "effective_io_concurrency=256",
      "-c", "maintenance_work_mem=1GB",
      "-c", "max_connections=500",
      "-c", "max_locks_per_transaction=64",
      "-c", "max_parallel_workers=2",
      "-c", "max_parallel_workers_per_gather=2",
      "-c", "max_wal_size=1GB",
      "-c", "max_worker_processes=24",
      "-c", "min_wal_size=0.5GB",
      "-c", "random_page_cost=1.1",
      "-c", "shared_buffers=2GB",
      "-c", "timescaledb.max_background_workers=8",
      "-c", "timescaledb.telemetry_level=off",
      "-c", "wal_buffers=16MB",
      "-c", "work_mem=10MB"
    ]
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: "4g"
    env_file:
      - .env
    volumes:
      - postgres_data:/home/<USER>/pgdata
      - ./backend/sql/db:/docker-entrypoint-initdb.d
    ports:
      - "${DB_PORT}:${DB_PORT}"
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "5"
    networks:
      - ai_agent_network
    labels:
      - "traefik.enable=false"  # 数据库不需要直接暴露

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - DOCKER_ENV=true
      - DEEPSEEK_API_KEY=***********************************
    volumes:
      - app_logs:/app/logs
    ports:
      - "${BACKEND_PORT}:${BACKEND_PORT}"
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: "2g"
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "5"
    networks:
      - ai_agent_network
      - proxy-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`api.youragen.com`)"
      - "traefik.http.routers.backend.entrypoints=websecure"
      - "traefik.http.routers.backend.tls=true"
      - "traefik.http.routers.backend.tls.certresolver=letsencryptResolver"
      - "traefik.http.services.backend.loadbalancer.server.port=${BACKEND_PORT}"
      - "traefik.http.middlewares.backend-compress.compress=true"
      - "traefik.http.routers.backend.middlewares=default-headers@file,backend-compress@docker"

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - BACKEND_HOST=backend
    ports:
      - "${FRONTEND_PORT}:${FRONTEND_PORT}"
    deploy:
      resources:
        limits:
          cpus: "0.3"
          memory: "384m"
    logging:
      driver: "json-file"
      options:
        max-size: "200m"
        max-file: "5"
    networks:
      - ai_agent_network
      - proxy-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`youragen.com`) || Host(`www.youragen.com`)"
      - "traefik.http.routers.frontend.entrypoints=websecure"
      - "traefik.http.routers.frontend.tls=true"
      - "traefik.http.routers.frontend.tls.certresolver=letsencryptResolver"
      - "traefik.http.routers.frontend.tls.options=default"
      - "traefik.http.routers.frontend.tls.domains[0].main=youragen.com"
      - "traefik.http.routers.frontend.tls.domains[0].sans=*.youragen.com"
      - "traefik.http.services.frontend.loadbalancer.server.port=${FRONTEND_PORT}"
      - "traefik.http.middlewares.frontend-compress.compress=true"
      - "traefik.http.routers.frontend.middlewares=default-headers@file,frontend-compress@docker"
      # 为流式响应端点创建专门的路由，禁用压缩
#      - "traefik.http.routers.frontend-stream.rule=Host(`youragen.com`) && PathRegexp(`^/api/v1/agents/[^/]+/runs`)"
#      - "traefik.http.routers.frontend-stream.entrypoints=websecure"
#      - "traefik.http.routers.frontend-stream.tls=true"
#      - "traefik.http.routers.frontend-stream.tls.certresolver=letsencryptResolver"
#      - "traefik.http.routers.frontend-stream.priority=100"  # 更高优先级
#      - "traefik.http.services.frontend-stream.loadbalancer.server.port=${FRONTEND_PORT}"
#      - "traefik.http.routers.frontend-stream.middlewares=stream-headers@file"  # 使用流式响应专用中间件

networks:
  ai_agent_network:
    driver: bridge
  proxy-network:
    external: true

volumes:
  postgres_data:
    name: ai_agent_postgres_data
  app_logs:
    name: ai_agent_logs
