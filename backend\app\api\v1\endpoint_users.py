from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.core_auth_deps import get_current_active_user, get_current_superuser
from app.db.db_session import get_async_db
from app.models.auth import User, UserResponse, UserUpdate

router = APIRouter(prefix="/users", tags=["用户"])


@router.get("/me")
async def get_current_user_info(current_user: User = Depends(get_current_active_user)) -> UserResponse:
    return current_user


@router.patch("/me")
async def update_current_user(user_in: UserUpdate, db: AsyncSession = get_async_db, current_user: User = Depends(get_current_active_user)) -> UserResponse:
    current_user.sqlmodel_update(user_in)
    await current_user.merge(db, commit=True, refresh=True)
    return current_user


# 管理员接口

@router.get("/")
async def get_users(db: AsyncSession = get_async_db, current_user: User = Depends(get_current_superuser)) -> List[User]:
    return await User.filter_by(session=db)


@router.get("/{user_id}", )
async def get_user(user_id: str, db: AsyncSession = get_async_db, current_user: User = Depends(get_current_superuser)) -> User:
    user = await User.get_by(session=db, id=user_id)
    return user


@router.patch("/{user_id}", response_model=UserResponse)
async def update_user(user_id: str, user_in: UserUpdate, db: AsyncSession = get_async_db, current_user: User = Depends(get_current_superuser)) -> UserResponse:
    user = await User.get_by(session=db, id=user_id)
    user.sqlmodel_update(user_in)
    await user.merge(db, commit=True, refresh=True)
    return user


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(user_id: str, db: AsyncSession = get_async_db, current_user: User = Depends(get_current_superuser)) -> None:
    if user_id == str(current_user.id):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="无法删除当前登录用户")

    success = await User.delete_where(session=db, id=user_id)
    if not success:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在")
