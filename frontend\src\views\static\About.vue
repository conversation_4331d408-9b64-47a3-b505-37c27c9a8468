<template>
  <div class="bg-gray-50 dark:bg-dark transition-colors duration-300">
    <!-- 侧边导航 -->
    <div class="fixed left-4 top-1/2 transform -translate-y-1/2 z-10 hidden lg:block">
      <nav class="nav-container">
        <ul class="space-y-2">
          <li v-for="item in navigationItems" :key="item.id">
            <a :href="`#${item.id}`" class="nav-link" @click.prevent="scrollTo(item.id)">
              {{ $t(`about.navigation.${item.id}`) }}
            </a>
          </li>
        </ul>
      </nav>
    </div>

    <!-- 关于我们 Section -->
    <section id="about" class="section-container">
      <div class="container">
        <div class="content-card">
          <h1 class="page-title">{{ $t('about.title') }}</h1>

          <div class="content-grid">
            <div class="content-block">
              <h2 class="section-subtitle">{{ $t('about.mission.title') }}</h2>
              <p class="text-content mb-4">{{ $t('about.mission.description1') }}</p>
              <p class="text-content">{{ $t('about.mission.description2') }}</p>
            </div>

            <div class="content-block">
              <h2 class="section-subtitle mb-6">{{ $t('about.timeline.title') }}</h2>
              <div class="timeline-container">
                <div v-for="milestone in timelineMilestones" :key="milestone" class="timeline-item">
                  <h3 class="item-title">{{ $t(`about.timeline.milestone${milestone}.title`) }}</h3>
                  <p class="item-description">{{ $t(`about.timeline.milestone${milestone}.description`) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 最新动态 Section -->
    <section id="news" class="section-container section-alt">
      <div class="container">
        <div class="content-card-alt">
          <h1 class="page-title">{{ $t('about.news.title') }}</h1>

          <div class="items-grid">
            <div v-for="news in newsItems" :key="news.id" class="interactive-card">
              <div class="card-header">
                <span class="badge" :class="getBadgeClass(news.type)">
                  {{ $t(`about.news.types.${news.type}`) }}
                </span>
                <span class="date-text">{{ news.date }}</span>
              </div>
              <h2 class="card-title">{{ $t(`about.news.items.${news.id}.title`) }}</h2>
              <p class="card-description">{{ $t(`about.news.items.${news.id}.summary`) }}</p>
              <button class="action-button">{{ $t('about.buttons.viewDetails') }}</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 产品特色 Section -->
    <section id="features" class="section-container">
      <div class="container">
        <div class="content-card">
          <h1 class="page-title">{{ $t('about.features.title') }}</h1>
          <div class="features-grid">
            <div v-for="feature in productFeatures" :key="feature" class="feature-card">
              <h3 class="feature-title">{{ $t(`about.features.items.${feature}.title`) }}</h3>
              <p class="feature-description">{{ $t(`about.features.items.${feature}.description`) }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 帮助中心 Section -->
    <section id="help" class="section-container section-alt">
      <div class="container">
        <div class="content-card-alt">
          <h1 class="page-title">{{ $t('about.help.title') }}</h1>

          <!-- 导航标签 -->
          <div class="tab-container">
            <button v-for="section in helpSections" :key="section" @click="activeSection = section" class="tab-button" :class="{ 'tab-active': activeSection === section }">
              {{ $t(`about.help.sections.${section}`) }}
            </button>
          </div>

          <div class="tab-content">
            <!-- 快速开始 -->
            <div v-if="activeSection === 'quickStart'" class="content-section">
              <h2 class="section-subtitle mb-6">{{ $t('about.help.quickStart.title') }}</h2>
              <div class="steps-container">
                <div v-for="(step, index) in quickStartSteps" :key="step" class="step-item">
                  <div class="step-number">{{ index + 1 }}</div>
                  <div class="step-content">
                    <h3 class="step-title">{{ $t(`about.help.quickStart.step${index + 1}.title`) }}</h3>
                    <p class="step-description">{{ $t(`about.help.quickStart.step${index + 1}.description`) }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 用户指南 -->
            <div v-if="activeSection === 'userGuide'" class="content-section">
              <h2 class="section-subtitle mb-6">{{ $t('about.help.userGuide.title') }}</h2>
              <div class="guide-container">
                <div class="guide-intro">
                  <h3 class="guide-title">{{ $t('about.help.userGuide.basicUsage') }}</h3>
                  <p class="guide-description">{{ $t('about.help.userGuide.basicDescription') }}</p>
                </div>

                <div class="agents-grid">
                  <div v-for="agent in availableAgents" :key="agent" class="agent-item">
                    <h4 class="agent-title">{{ $t(`about.help.userGuide.agents.${agent}.title`) }}</h4>
                    <p class="agent-description">{{ $t(`about.help.userGuide.agents.${agent}.description`) }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 常见问题 -->
            <div v-if="activeSection === 'faq'" class="content-section">
              <h2 class="section-subtitle mb-6">{{ $t('about.help.faq.title') }}</h2>
              <div class="faq-container">
                <div v-for="faq in faqs" :key="faq" class="faq-item">
                  <button @click="toggleFaq(faq)" class="faq-question">
                    <span class="faq-text">{{ $t(`about.help.faq.questions.${faq}`) }}</span>
                    <svg class="faq-icon" :class="{ 'rotate-180': expandedFaq === faq }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </button>
                  <div v-if="expandedFaq === faq" class="faq-answer">
                    <p class="answer-text">{{ $t(`about.help.faq.answers.${faq}`) }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 服务条款 Section -->
    <section id="terms" class="section-container">
      <div class="container">
        <div class="content-card">
          <h1 class="page-title mb-6">{{ $t('about.terms.title') }}</h1>
          <p class="update-date">{{ $t('about.terms.lastUpdated') }}</p>
          <div class="legal-content">
            <section v-for="section in termsSections" :key="section" class="legal-section">
              <h2 class="section-subtitle">{{ $t(`about.terms.${section}.title`) }}</h2>
              <p class="text-content">{{ $t(`about.terms.${section}.content`) }}</p>
            </section>
          </div>
        </div>
      </div>
    </section>

    <!-- 隐私政策 Section -->
    <section id="privacy" class="section-container section-alt">
      <div class="container">
        <div class="content-card-alt">
          <h1 class="page-title mb-6">{{ $t('about.privacy.title') }}</h1>
          <p class="update-date">{{ $t('about.privacy.lastUpdated') }}</p>
          <div class="legal-content">
            <section v-for="section in privacySections" :key="section" class="legal-section">
              <h2 class="section-subtitle">{{ $t(`about.privacy.${section}.title`) }}</h2>
              <p class="text-content" v-html="$t(`about.privacy.${section}.content`)"></p>
            </section>
          </div>
        </div>
      </div>
    </section>

    <!-- 联系我们 Section -->
    <section id="contact" class="section-container">
      <div class="container">
        <div class="content-card">
          <h1 class="page-title text-center">{{ $t('about.contact.title') }}</h1>

          <div class="contact-grid">
            <!-- 技术支持 -->
            <div class="contact-card contact-card-primary">
              <h3 class="contact-title">{{ $t('about.contact.support.title') }}</h3>
              <p class="contact-description">{{ $t('about.contact.support.description') }}</p>
              <div class="contact-details">
                <div class="contact-item">
                  <svg class="contact-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    ></path>
                  </svg>
                  <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                </div>
                <div class="contact-item">
                  <svg class="contact-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="contact-info">{{ $t('about.contact.support.responseTime') }}</span>
                </div>
              </div>
            </div>

            <!-- 商务合作 -->
            <div class="contact-card contact-card-secondary">
              <h3 class="contact-title">{{ $t('about.contact.business.title') }}</h3>
              <p class="contact-description">{{ $t('about.contact.business.description') }}</p>
              <div class="contact-details">
                <div class="contact-item">
                  <svg class="contact-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    ></path>
                  </svg>
                  <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                </div>
                <div class="contact-item">
                  <svg class="contact-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="contact-info">{{ $t('about.contact.business.availability') }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部说明 -->
          <div class="contact-note">
            <div class="note-container">
              <p class="note-text">{{ $t('about.contact.note') }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch } from 'vue';
import { useHead } from '@vueuse/head';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';

const route = useRoute();
const { t } = useI18n();

useHead({
  title: '关于我们 - YourAGen',
  meta: [
    {
      name: 'description',
      content: '了解YourAGen - 专业的智能助手平台，提供技术支持、帮助文档和联系方式。',
    },
  ],
});

// 导航项目
const navigationItems = [{ id: 'about' }, { id: 'news' }, { id: 'features' }, { id: 'help' }, { id: 'terms' }, { id: 'privacy' }, { id: 'contact' }];

// 统一数据配置
const CONFIG = {
  timeline: [1, 2, 3],
  features: [1, 2, 3, 4, 5, 6],
  helpSections: ['quickStart', 'userGuide', 'faq'],
  quickStart: [1, 2, 3],
  agents: ['web', 'finance', 'crypto', 'general'],
  faq: ['whatisaiassistant', 'howtouse', 'pricing', 'datasecurity', 'features', 'support'],
  terms: ['serviceUsage', 'userResponsibility', 'dataUsage', 'limitation'],
  privacy: ['dataCollection', 'dataProtection', 'dataSharing', 'userRights', 'contact'],
} as const;

// 解构配置
const {
  timeline: timelineMilestones,
  features: productFeatures,
  helpSections,
  quickStart: quickStartSteps,
  agents: availableAgents,
  faq: faqs,
  terms: termsSections,
  privacy: privacySections,
} = CONFIG;

// 新闻数据
const newsItems = ref([
  { id: 1, type: 'performance', date: '2025年5月15日' },
  { id: 2, type: 'feature', date: '2025年3月10日' },
  { id: 3, type: 'tech', date: '2025年2月5日' },
  { id: 4, type: 'tech', date: '2025年1月30日' },
  { id: 5, type: 'feature', date: '2024年11月25日' },
]);

// 状态管理
const activeSection = ref('quickStart');
const expandedFaq = ref<string | null>(null);

// 工具方法
const scrollTo = (elementId: string) => {
  const element = document.getElementById(elementId);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

const toggleFaq = (faqId: string) => {
  expandedFaq.value = expandedFaq.value === faqId ? null : faqId;
};

const getBadgeClass = (type: string) => {
  const badgeMap: Record<string, string> = {
    tech: 'badge-blue',
    feature: 'badge-green',
    performance: 'badge-purple',
  };
  return badgeMap[type] || badgeMap.tech;
};

// 生命周期
onMounted(() => {
  if (route.hash) {
    setTimeout(() => scrollTo(route.hash.substring(1)), 100);
  }
});

// 监听路由变化
watch(
  () => route.hash,
  newHash => {
    if (newHash) {
      setTimeout(() => scrollTo(newHash.substring(1)), 100);
    }
  },
  { immediate: true }
);
</script>

<style scoped>
/* 基础变量 */
:root {
  --border-radius: 0.75rem;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --transition: all 0.3s ease;
  --card-padding: 1.5rem;
  --card-padding-sm: 1rem;
}

/* 基础布局 */
.section-container {
  @apply py-16;
}

.section-alt {
  @apply bg-white dark:bg-dark-secondary;
}

.container {
  @apply max-w-4xl mx-auto px-4;
}

/* 统一卡片系统 */
.card-base {
  @apply rounded-xl transition-all duration-300;
}

.card-shadow {
  @apply shadow-md hover:shadow-lg;
}

.card-padding {
  @apply p-6;
}

.card-padding-lg {
  @apply p-8;
}

/* 主要卡片样式 */
.content-card,
.content-card-alt {
  @apply card-base card-shadow card-padding-lg;
}

.content-card {
  @apply bg-white dark:bg-dark-secondary;
}

.content-card-alt {
  @apply bg-gray-50 dark:bg-dark;
}

/* 交互式卡片 */
.interactive-card {
  @apply card-base card-shadow card-padding bg-white dark:bg-dark-secondary hover:bg-gray-50 dark:hover:bg-gray-800/50;
}

/* 特色功能卡片 */
.feature-card,
.agent-item {
  @apply card-base card-shadow card-padding bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700;
}

/* 步骤卡片 */
.step-item {
  @apply card-base shadow-sm card-padding bg-gray-50 dark:bg-dark flex items-start;
}

/* 指南卡片 */
.guide-intro {
  @apply card-base shadow-sm card-padding bg-gray-50 dark:bg-dark;
}

/* FAQ卡片 */
.faq-item {
  @apply card-base border border-gray-200 dark:border-gray-700;
}

/* 标签页内容卡片 */
.tab-content {
  @apply card-base card-padding bg-white dark:bg-dark-secondary;
}

/* 联系我们卡片 */
.contact-card {
  @apply card-base card-padding transition-all duration-300 hover:shadow-lg;
}

.contact-card-primary {
  @apply bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30;
}

.contact-card-secondary {
  @apply bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30;
}

/* 底部说明卡片 */
.note-container {
  @apply card-base card-padding bg-gray-50 dark:bg-gray-800/50;
}

/* 导航卡片 */
.nav-container {
  @apply card-base shadow-lg p-2 bg-white dark:bg-dark-secondary;
}

/* 通用文字样式 */
.page-title {
  @apply text-4xl font-bold text-gray-900 dark:text-white mb-8;
}

.section-subtitle {
  @apply text-2xl font-semibold text-gray-900 dark:text-white mb-4;
}

.text-content {
  @apply text-gray-600 dark:text-gray-300 leading-relaxed;
}

.update-date {
  @apply text-sm text-gray-500 dark:text-gray-400 mb-6;
}

/* 导航样式 */
.nav-link {
  @apply block px-3 py-2 text-sm text-gray-600 dark:text-gray-300 hover:text-primary hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-300;
}

/* 布局网格 */
.content-grid,
.items-grid,
.timeline-container,
.steps-container,
.guide-container,
.faq-container,
.legal-content {
  @apply space-y-6;
}

.content-block,
.legal-section {
  @apply space-y-4;
}

.features-grid {
  @apply grid md:grid-cols-2 gap-6;
}

.agents-grid {
  @apply grid md:grid-cols-2 gap-4;
}

/* 时间线样式 */
.timeline-item {
  @apply border-l-4 border-primary pl-4;
}

.item-title {
  @apply font-semibold text-gray-900 dark:text-white mb-2;
}

.item-description {
  @apply text-sm text-gray-600 dark:text-gray-300;
}

/* 新闻卡片内容样式 */
.card-header {
  @apply flex items-center gap-3 mb-3;
}

.badge {
  @apply px-3 py-1 rounded-full text-xs font-medium;
}

.badge-blue {
  @apply bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400;
}

.badge-green {
  @apply bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400;
}

.badge-purple {
  @apply bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-400;
}

.date-text {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.card-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-2;
}

.card-description {
  @apply text-gray-600 dark:text-gray-300 mb-3;
}

.action-button {
  @apply px-4 py-2 bg-transparent border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-300 text-sm;
}

/* 卡片内容标题样式 */
.feature-title,
.agent-title {
  @apply text-lg font-medium text-gray-900 dark:text-white mb-3;
}

.feature-description,
.agent-description {
  @apply text-gray-600 dark:text-gray-300 text-sm;
}

/* 标签页样式 */
.tab-container {
  @apply flex flex-wrap gap-2 mb-8;
}

.tab-button {
  @apply px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300;
}

.tab-active {
  @apply bg-primary text-white;
}

.tab-button:not(.tab-active) {
  @apply bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600;
}

/* 步骤内容样式 */
.step-number {
  @apply w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 flex-shrink-0;
}

.step-content {
  @apply flex-1;
}

.step-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-2;
}

.step-description {
  @apply text-gray-600 dark:text-gray-300 text-sm;
}

/* 指南内容样式 */
.guide-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-3;
}

.guide-description {
  @apply text-gray-600 dark:text-gray-300 mb-4;
}

/* FAQ内容样式 */
.faq-question {
  @apply w-full px-4 py-3 text-left flex justify-between items-center hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-all duration-300;
}

.faq-text {
  @apply font-medium text-gray-900 dark:text-white;
}

.faq-icon {
  @apply w-5 h-5 text-gray-500 dark:text-gray-400 transform transition-transform duration-300;
}

.faq-answer {
  @apply px-4 pb-3;
}

.answer-text {
  @apply text-gray-600 dark:text-gray-300 text-sm;
}

/* 联系我们内容样式 */
.contact-grid {
  @apply grid md:grid-cols-2 gap-6 mb-8;
}

.contact-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-4;
}

.contact-description {
  @apply text-gray-600 dark:text-gray-300 mb-4;
}

.contact-details {
  @apply space-y-3;
}

.contact-item {
  @apply flex items-center;
}

.contact-icon {
  @apply w-4 h-4 text-gray-600 dark:text-gray-400 mr-3 flex-shrink-0;
}

.contact-link {
  @apply text-gray-900 dark:text-white hover:underline font-medium;
}

.contact-info {
  @apply text-gray-600 dark:text-gray-300;
}

.contact-note {
  @apply text-center;
}

.note-text {
  @apply text-gray-600 dark:text-gray-300 text-sm;
}
</style>
