"""
币安WebSocket客户端实现
"""
import asyncio
import json
from typing import Dict, Any, List, Callable

import websockets

from app.core.core_log import get_logger
from quant.exchange.base import ExchangeWsInterface, WsChannelType
from quant.exchange.binance_client import BinanceEndpoints

logger = get_logger("binance_ws")


class BinanceWebSocket(ExchangeWsInterface):
    """币安WebSocket客户端"""

    def __init__(self, market_type: str = "spot"):
        self.market_type = market_type
        self.ws_url = BinanceEndpoints.get_ws_url(market_type)
        self.ws = None
        self.subscriptions = {}
        self.callbacks = {}
        self.running = False
        self.ping_interval = 30  # 30秒发送一次ping
        self.ping_task = None

    async def connect(self) -> None:
        """建立WebSocket连接"""
        if self.ws:
            return

        logger.info(f"正在连接币安WebSocket: {self.ws_url}")
        self.ws = await websockets.connect(self.ws_url)
        self.running = True

        # 启动接收消息的任务
        asyncio.create_task(self._message_handler())

        # 启动ping任务
        self.ping_task = asyncio.create_task(self._ping_loop())

        logger.info("币安WebSocket连接成功")

    async def disconnect(self) -> None:
        """断开WebSocket连接"""
        if not self.ws:
            return

        logger.info("正在断开币安WebSocket连接")
        self.running = False

        # 取消ping任务
        if self.ping_task:
            self.ping_task.cancel()

        # 关闭WebSocket连接
        await self.ws.close()
        self.ws = None
        self.subscriptions = {}
        self.callbacks = {}

        logger.info("币安WebSocket连接已断开")

    async def subscribe(self, channel: str, symbols: List[str], callback: Callable) -> None:
        """
        订阅频道
        
        Args:
            channel: 频道类型
            symbols: 交易对列表
            callback: 回调函数
        """
        if not self.ws:
            await self.connect()

        # 获取频道格式
        channel_format = self._get_channel_format(channel)

        # 构建订阅消息
        streams = []
        for symbol in symbols:
            symbol = symbol.lower()
            stream = f"{symbol}@{channel_format}"
            streams.append(stream)

            # 记录订阅和回调
            if channel not in self.subscriptions:
                self.subscriptions[channel] = []
                self.callbacks[channel] = {}

            if symbol not in self.subscriptions[channel]:
                self.subscriptions[channel].append(symbol)

            self.callbacks[channel][symbol] = callback

        # 发送订阅消息
        subscribe_msg = {
            "method": "SUBSCRIBE",
            "params": streams,
            "id": 1
        }

        await self.ws.send(json.dumps(subscribe_msg))
        logger.info(f"订阅频道: {channel}, 交易对: {symbols}")

    async def unsubscribe(self, channel: str, symbols: List[str]) -> None:
        """
        取消订阅
        
        Args:
            channel: 频道类型
            symbols: 交易对列表
        """
        if not self.ws:
            return

        # 获取频道格式
        channel_format = self._get_channel_format(channel)

        # 构建取消订阅消息
        streams = []
        for symbol in symbols:
            symbol = symbol.lower()
            stream = f"{symbol}@{channel_format}"
            streams.append(stream)

            # 删除订阅记录
            if channel in self.subscriptions and symbol in self.subscriptions[channel]:
                self.subscriptions[channel].remove(symbol)
                if symbol in self.callbacks.get(channel, {}):
                    del self.callbacks[channel][symbol]

        # 发送取消订阅消息
        unsubscribe_msg = {
            "method": "UNSUBSCRIBE",
            "params": streams,
            "id": 2
        }

        await self.ws.send(json.dumps(unsubscribe_msg))
        logger.info(f"取消订阅频道: {channel}, 交易对: {symbols}")

    async def _message_handler(self) -> None:
        """处理WebSocket消息"""
        while self.running:
            try:
                message = await self.ws.recv()
                await self._process_message(message)
            except websockets.exceptions.ConnectionClosed:
                logger.error("WebSocket连接已关闭，尝试重新连接")
                self.ws = None
                await asyncio.sleep(3)
                await self.connect()
                await self._resubscribe()
            except Exception as e:
                logger.exception(f"处理WebSocket消息时出错: {str(e)}")
                await asyncio.sleep(1)

    async def _process_message(self, message: str) -> None:
        """处理收到的消息"""
        try:
            data = json.loads(message)

            # 处理订阅确认或错误消息
            if "id" in data:
                if "result" in data:
                    logger.debug(f"订阅确认: {data}")
                elif "error" in data:
                    logger.error(f"订阅错误: {data}")
                return

            # 处理ping响应
            if "ping" in data:
                await self._send_pong()
                return

            # 处理数据消息
            if "stream" in data:
                stream = data["stream"]
                payload = data["data"]

                # 解析流名称，格式为 symbol@channel
                parts = stream.split("@")
                if len(parts) != 2:
                    return

                symbol = parts[0].upper()
                channel = self._get_channel_type(parts[1])

                # 调用回调函数
                if channel in self.callbacks and symbol in self.callbacks[channel]:
                    callback = self.callbacks[channel][symbol]
                    asyncio.create_task(self._call_callback(callback, payload))
        except Exception as e:
            logger.exception(f"解析WebSocket消息时出错: {str(e)}, 消息: {message}")

    async def _call_callback(self, callback: Callable, data: Dict[str, Any]) -> None:
        """安全调用回调函数"""
        try:
            await callback(data)
        except Exception as e:
            logger.exception(f"执行回调函数时出错: {str(e)}")

    async def _ping_loop(self) -> None:
        """定期发送ping消息"""
        while self.running:
            try:
                await asyncio.sleep(self.ping_interval)
                if self.ws:
                    await self.ws.send(json.dumps({"method": "ping"}))
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.exception(f"发送ping消息时出错: {str(e)}")

    async def _send_pong(self) -> None:
        """发送pong响应"""
        try:
            if self.ws:
                await self.ws.send(json.dumps({"method": "pong"}))
        except Exception as e:
            logger.exception(f"发送pong响应时出错: {str(e)}")

    async def _resubscribe(self) -> None:
        """重新订阅所有频道"""
        for channel, symbols in self.subscriptions.items():
            if symbols:
                callback = next(iter(self.callbacks[channel].values()))
                await self.subscribe(channel, symbols, callback)

    def _get_channel_format(self, channel: str) -> str:
        """获取频道格式"""
        if isinstance(channel, WsChannelType):
            channel = channel.value

        if channel == "ticker":
            return "ticker"
        elif channel == "kline":
            return "kline_1m"  # 默认使用1分钟K线
        elif channel == "depth":
            return "depth20"  # 默认20档深度
        elif channel == "trade":
            return "trade"
        else:
            return channel

    def _get_channel_type(self, channel_format: str) -> str:
        """
        从频道格式中获取频道类型
        例如: kline_1m -> kline
        """
        if channel_format.startswith("kline"):
            return "kline"
        elif channel_format.startswith("depth"):
            return "depth"
        else:
            return channel_format
