<template>
  <!-- 消息容器 - 使用固定的布局结构 -->
  <div class="message-container" :class="{ 'user-message': isUser, 'agent-message': !isUser }">
    <!-- AI消息布局：头像在左，内容在右 -->
    <template v-if="!isUser">
      <!-- AI头像 -->
      <div class="avatar-container">
        <div class="avatar ai-avatar">
          <n-icon :size="16" class="text-white">
            <RocketOutline />
          </n-icon>
        </div>
      </div>

      <!-- AI消息内容 -->
      <div class="message-content-wrapper ai-content">
        <div class="message-bubble ai-bubble">
          <!-- AI消息头部 -->
          <div class="ai-header">
            <div class="ai-status">
              <div class="status-dot"></div>
              <span class="status-text">AI</span>
            </div>
            <MessageActionButtons :message="message" :message-type="messageType" @edit="$emit('edit')" @regenerate="$emit('regenerate')" />
          </div>

          <!-- AI消息内容 -->
          <div class="message-content ai-message-content">
            <div v-if="message.content?.trim()" class="content-text">
              <MarkdownRenderer :content="message.content" />
            </div>
            <div v-else class="thinking-state">
              <div class="thinking-indicator">
                <div class="flex gap-1">
                  <div class="w-1 h-1 rounded-full bg-blue-400 animate-bounce" style="animation-delay: 0ms"></div>
                  <div class="w-1 h-1 rounded-full bg-purple-400 animate-bounce" style="animation-delay: 150ms"></div>
                  <div class="w-1 h-1 rounded-full bg-pink-400 animate-bounce" style="animation-delay: 300ms"></div>
                </div>
              </div>
            </div>

            <!-- 多媒体内容 -->
            <div v-if="hasMultimedia" class="multimedia-content">
              <!-- 图片 -->
              <div v-if="message.images?.length" class="media-section">
                <div class="media-header">
                  <n-icon size="14" class="media-icon"><ImageOutline /></n-icon>
                  生成的图片
                </div>
                <div class="image-grid">
                  <div v-for="(image, index) in message.images" :key="index" class="image-item" @click="openImageModal(image)">
                    <img :src="image.url" :alt="image.revised_prompt || '生成的图片'" class="image" />
                    <div class="image-overlay">
                      <n-icon size="28" class="overlay-icon"><EyeOutline /></n-icon>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 视频 -->
              <div v-if="message.videos?.length" class="media-section">
                <div class="media-header">
                  <n-icon size="14" class="media-icon"><VideocamOutline /></n-icon>
                  视频内容
                </div>
                <div class="video-list">
                  <div v-for="(video, index) in message.videos" :key="index" class="video-item">
                    <video controls class="video" preload="metadata">
                      <source :src="video.url" type="video/mp4" />
                      您的浏览器不支持视频播放。
                    </video>
                  </div>
                </div>
              </div>

              <!-- 音频 -->
              <div v-if="message.audio?.length" class="media-section">
                <div class="media-header">
                  <n-icon size="14" class="media-icon"><MusicalNotesOutline /></n-icon>
                  音频内容
                </div>
                <div class="audio-list">
                  <div v-for="(audio, index) in message.audio" :key="index" class="audio-item">
                    <audio controls class="audio" preload="metadata">
                      <source :src="audio.url || `data:${audio.mime_type};base64,${audio.base64_audio}`" :type="audio.mime_type || 'audio/mpeg'" />
                      您的浏览器不支持音频播放。
                    </audio>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- AI时间戳 -->
          <div class="timestamp ai-timestamp">
            <div class="flex flex-col gap-1">
              <span>{{ formatTimestamp(message.created_at) }}</span>
              <TokenUsage :message="message" />
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 用户消息布局：内容在左，头像在右 -->
    <template v-else>
      <!-- 用户消息内容 -->
      <div class="message-content-wrapper user-content">
        <div class="message-bubble user-bubble">
          <!-- 用户消息操作按钮 -->
          <div class="user-actions">
            <MessageActionButtons :message="message" :message-type="messageType" @edit="$emit('edit')" @regenerate="$emit('regenerate')" />
          </div>

          <!-- 用户消息内容 -->
          <div class="message-content user-message-content">
            {{ message.content || '消息内容为空' }}
          </div>

          <!-- 用户时间戳 -->
          <div class="timestamp user-timestamp">
            {{ formatTimestamp(message.created_at) }}
          </div>
        </div>
      </div>

      <!-- 用户头像 -->
      <div class="avatar-container">
        <div class="avatar user-avatar">
          <n-icon :size="16" class="text-white">
            <PersonOutline />
          </n-icon>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { NIcon } from 'naive-ui';
import { PersonOutline, RocketOutline, ImageOutline, VideocamOutline, MusicalNotesOutline, EyeOutline } from '@vicons/ionicons5';
import type { PlaygroundChatMessage, ImageData } from '@/types/playground';
import { formatTimestamp } from '@/utils/time';
import MarkdownRenderer from './MarkdownRenderer.vue';
import MessageActionButtons from './MessageActionButtons.vue';
import TokenUsage from './TokenUsage.vue';

interface Props {
  message: PlaygroundChatMessage;
  messageType: 'user' | 'agent';
}

interface Emits {
  (e: 'edit'): void;
  (e: 'regenerate'): void;
}

const props = defineProps<Props>();
defineEmits<Emits>();

// 简化的计算属性
const isUser = computed(() => props.messageType === 'user');
const hasMultimedia = computed(() => !!(props.message.images?.length || props.message.videos?.length || props.message.audio?.length));

// 多媒体方法
const openImageModal = (image: ImageData) => window.open(image.url, '_blank');
</script>

<style scoped>
/* 消息容器 */
.message-container {
  @apply flex items-start gap-3 mb-4 w-full;
}

.user-message {
  @apply justify-end;
}

.agent-message {
  @apply justify-start;
}

/* 头像 */
.avatar-container {
  @apply flex-shrink-0;
}

.avatar {
  @apply flex h-8 w-8 items-center justify-center rounded-full transition-all duration-300 hover:scale-110;
}

.ai-avatar {
  @apply bg-gray-800 text-white dark:bg-gray-700 dark:text-white;
}

.user-avatar {
  @apply bg-gray-700 text-white dark:bg-gray-700 dark:text-white;
}

/* 消息内容包装器 */
.message-content-wrapper {
  @apply flex-shrink min-w-0;
}

.ai-content {
  @apply max-w-[85%] sm:max-w-[80%] lg:max-w-[75%];
}

.user-content {
  @apply max-w-[75%] sm:max-w-[65%] lg:max-w-[55%];
}

/* 消息气泡 */
.message-bubble {
  @apply relative transition-shadow duration-300 overflow-hidden;
}

.ai-bubble {
  @apply rounded-2xl rounded-bl-md bg-white dark:bg-gray-800;
}

.user-bubble {
  @apply rounded-2xl rounded-br-md bg-white text-gray-900 dark:bg-gray-700 dark:text-white;
}

/* 消息头部和操作区域 */
.ai-header,
.user-actions {
  @apply flex items-center p-3 pb-1;
}

.ai-header {
  @apply justify-between;
}

.user-actions {
  @apply justify-end;
}

.ai-status {
  @apply flex items-center gap-2;
}

.status-dot {
  @apply w-1.5 h-1.5 rounded-full animate-pulse bg-green-500;
}

.status-text {
  @apply text-xs font-medium text-gray-700 dark:text-gray-400;
}

/* 消息内容 */
.message-content {
  @apply w-full overflow-hidden;
}

.ai-message-content {
  @apply px-3 pb-1;
}

.user-message-content {
  @apply px-3 py-1 font-normal;
  font-size: 12px;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: anywhere;
  white-space: pre-wrap;
  color: rgb(31, 41, 55);
}

.dark .user-message-content {
  color: rgb(248, 250, 252);
}

.content-text {
  @apply w-full overflow-hidden break-words;
}

/* 思考状态 */
.thinking-state {
  @apply text-gray-500 dark:text-gray-400 italic text-sm my-4;
}

.thinking-indicator {
  @apply flex items-center gap-2;
}

/* 时间戳 */
.timestamp {
  @apply text-[10px];
}

.ai-timestamp {
  @apply px-3 pb-2 text-gray-600 dark:text-gray-500;
}

.ai-timestamp > div {
  @apply items-start;
}

.ai-timestamp span {
  @apply text-[10px];
}

.user-timestamp {
  @apply px-3 pb-2 text-gray-600 dark:text-gray-500 opacity-70 font-normal text-right;
}

/* 多媒体内容 */
.multimedia-content {
  @apply mt-3 space-y-3;
}

.media-section {
  @apply space-y-2;
}

.media-header {
  @apply flex items-center gap-2 text-sm font-medium text-gray-800 dark:text-gray-300;
}

.media-icon {
  @apply text-gray-700 dark:text-gray-400;
}

/* 媒体元素 */
.image-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-2;
}

.video-list,
.audio-list {
  @apply space-y-2;
}

.image-item,
.video-item,
.audio-item {
  @apply rounded-lg overflow-hidden;
}

.image-item {
  @apply relative cursor-pointer;
}

.audio-item {
  @apply bg-slate-50 dark:bg-gray-800 p-3;
}

.image,
.video,
.audio {
  @apply w-full;
}

.image {
  @apply h-auto object-cover transition-transform duration-300;
}

.video {
  @apply h-auto;
}

.image-item:hover .image {
  @apply scale-105;
}

.image-overlay {
  @apply absolute inset-0 bg-black bg-opacity-0 transition-all duration-300 flex items-center justify-center;
}

.image-item:hover .image-overlay {
  @apply bg-opacity-20;
}

.overlay-icon {
  @apply text-white opacity-0 transition-opacity;
}

.image-item:hover .overlay-icon {
  @apply opacity-100;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-content {
    max-width: calc(100vw - 4rem);
  }

  .user-content {
    max-width: calc(100vw - 5rem);
  }
}

@media (max-width: 480px) {
  .ai-content,
  .user-content {
    max-width: calc(100vw - 3.5rem);
  }

  .avatar {
    @apply w-7 h-7;
  }

  .avatar :deep(n-icon) {
    font-size: 14px !important;
  }

  .image-grid {
    @apply grid-cols-1;
  }
}
</style>
