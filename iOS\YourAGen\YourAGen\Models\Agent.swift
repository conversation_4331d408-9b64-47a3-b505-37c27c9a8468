import Foundation

struct AgentModel: Codable {
    let name: String
    let model: String
    let provider: String
}

struct Agent: Codable, Identifiable {
    let id: String
    let agentId: String
    let name: String
    let description: String
    let model: AgentModel
    let storage: Bool?
    
    // UI相关属性（本地计算）
    var icon: String {
        // 根据agent_id或provider返回对应的图标
        switch agentId {
        case "web_agent":
            return "globe"
        case "agno_assist":
            return "questionmark.circle"
        case "finance_agent":
            return "chart.line.uptrend.xyaxis"
        case "crypto_prediction_agent":
            return "bitcoinsign.circle"
        default:
            return "brain.head.profile"
        }
    }
    
    enum CodingKeys: String, CodingKey {
        case agentId = "agent_id"
        case name
        case description
        case model
        case storage
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        agentId = try container.decode(String.self, forKey: .agentId)
        name = try container.decode(String.self, forKey: .name)
        description = try container.decode(String.self, forKey: .description)
        model = try container.decode(AgentModel.self, forKey: .model)
        
        // 尝试解码storage字段，可能是Bool、字典或null
        if let storageValue = try? container.decode(Bool.self, forKey: .storage) {
            storage = storageValue
        } else if container.contains(.storage) {
            // 如果storage字段存在但不是Bool（比如是字典），默认为true
            storage = true
        } else {
            storage = nil
        }
        
        // id使用agentId
        id = agentId
    }
}

// Agent响应数据结构
struct AgentResponse: Codable {
    let success: Bool
    let data: [Agent]
    let message: String?
    let code: Int?
}

 