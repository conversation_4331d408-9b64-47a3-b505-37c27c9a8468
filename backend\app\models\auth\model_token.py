from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional
from uuid import UUID

from pydantic import BaseModel
from sqlalchemy import Column, DateTime, ForeignKey, String
from sqlalchemy.dialects.postgresql import JSONB, UUID as PGUUID
from sqlmodel import Field, Relationship, SQLModel

from app.db.db_orm_base_model import MixTime

# 避免循环导入
User = Any


# 公共枚举
class TokenType(str, Enum):
    """验证令牌类型"""
    EMAIL = "email"
    PHONE = "phone"
    PASSWORD_RESET = "password_reset"
    INVITE = "invite"


# 令牌基础模型
class TokenBase(SQLModel):
    """令牌基础信息模型
    
    包含验证令牌的基本字段
    """
    token_type: TokenType = Field(..., title="令牌类型")
    expires_at: datetime = Field(..., title="过期时间")
    is_used: bool = Field(default=False, title="是否已使用")


# 令牌创建模型
class TokenCreate(TokenBase):
    """令牌创建模型
    
    用于创建新的验证令牌
    """
    user_id: UUID
    token_value: str | None = None
    extra_data: Dict | None = None


# 令牌验证模型
class TokenVerify(SQLModel):
    """令牌验证模型
    
    用于验证令牌
    """
    token: str = Field(..., title="令牌值")
    token_type: TokenType | None = None


# 验证令牌数据库模型
class VerificationToken(TokenBase, MixTime, table=True):
    """验证令牌表
    
    用于处理电子邮件验证、手机验证、密码重置等功能
    """
    __tablename__ = "verification_tokens"
    __table_args__ = {"schema": "auth"}

    # 主键
    id: int | None = Field(default=None, primary_key=True, title="ID")

    # 关联字段
    user_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("auth.users.id"), nullable=False), title="用户ID")

    # 令牌相关字段
    token_hash: str = Field(sa_column=Column(String(255), nullable=False), title="令牌哈希")
    token_value: str | None = Field(sa_column=Column(String(64)), title="令牌值")
    used_at: datetime | None = Field(sa_column=Column(DateTime), title="使用时间")
    extra_data: Dict | None = Field(sa_column=Column(JSONB), title="额外数据")

    # 关系
    user: "User" = Relationship(back_populates="verification_tokens")


# 令牌响应模型
class TokenResponse(SQLModel):
    """令牌响应模型
    
    用于返回登录认证结果
    """
    access_token: str
    refresh_token: str
    token_type: str = "bearer"


# 令牌验证响应模型
class TokenValidationResponse(SQLModel):
    """令牌验证响应模型
    
    用于返回验证结果
    """
    success: bool = Field(..., title="是否成功")
    message: str = Field(..., title="消息")
    data: Dict | None = Field(None, title="数据")


# 用户状态历史基础模型
class UserStatusBase(SQLModel):
    """用户状态历史基础模型
    
    包含用户状态变更的基本信息
    """
    status: str = Field(..., title="状态")
    reason: str | None = Field(None, title="原因")


# 用户状态更新模型
class UserStatusUpdate(UserStatusBase):
    """用户状态更新模型
    
    用于更新用户状态
    """
    user_id: UUID
    actor_id: UUID | None = None


# 用户状态历史数据库模型
class UserStatusHistory(UserStatusBase, MixTime, table=True):
    """用户状态历史表
    
    记录用户状态变更历史，提供审计追踪
    """
    __tablename__ = "user_status_history"
    __table_args__ = {"schema": "auth"}

    # 主键
    id: int | None = Field(default=None, primary_key=True, title="ID")

    # 关联字段
    user_id: UUID = Field(sa_column=Column(PGUUID, ForeignKey("auth.users.id"), nullable=False), title="用户ID")
    actor_id: UUID | None = Field(sa_column=Column(PGUUID, ForeignKey("auth.users.id")), title="操作者ID")

    # 关系
    user: "User" = Relationship(back_populates="status_history", sa_relationship_kwargs={"primaryjoin": "UserStatusHistory.user_id == User.id"})
    actor: Optional["User"] = Relationship(sa_relationship_kwargs={"primaryjoin": "UserStatusHistory.actor_id == User.id"})


# 状态历史响应模型
class StatusHistoryResponse(UserStatusBase):
    """状态历史响应模型
    
    用于返回状态历史记录
    """
    id: int
    user_id: UUID
    actor_id: UUID | None = None
    create_time: datetime

    class Config:
        from_attributes = True


class TokenPayload(BaseModel):
    """令牌载荷模型"""
    sub: str  # 主题（通常是用户ID）
    exp: float  # 过期时间（Unix时间戳）
    type: str  # 令牌类型（access或refresh）


class Token(BaseModel):
    """令牌响应模型"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"


class TokenRefresh(BaseModel):
    """刷新令牌请求模型"""
    refresh_token: str
