import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import Home from '@/views/Home.vue';
import PageLogin from '@/views/auth/PageLogin.vue';
import PageRegister from '@/views/auth/PageRegister.vue';
import { useUserStore } from '@/stores/user';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: Home,
  },
  {
    path: '/login',
    name: 'Login',
    component: PageLogin,
    meta: {
      showHeader: false,
      showFooter: false,
    },
  },
  {
    path: '/register',
    name: 'Register',
    component: PageRegister,
    meta: {
      showHeader: false,
      showFooter: false,
    },
  },
  {
    path: '/agents',
    name: 'AgentMarketplace',
    component: () => import('@/views/agent/AgentMarketplace.vue'),
  },
  {
    path: '/playground',
    name: 'Playground',
    component: () => import('@/views/playground/PlaygroundPage.vue'),
    meta: {
      showHeader: false,
      showFooter: false,
    },
  },
  {
    path: '/user/profile',
    name: 'UserProfile',
    component: () => import('@/views/user/PageMe.vue'),
    meta: {
      requiresAuth: true,
    },
  },
  {
    path: '/pricing',
    name: 'Pricing',
    component: () => import('@/views/Pricing.vue'),
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('@/views/static/About.vue'),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的位置（如浏览器前进/后退），使用保存的位置
    if (savedPosition) {
      return savedPosition;
    }

    // 如果目标路由有hash锚点
    if (to.hash) {
      return new Promise(resolve => {
        // 等待页面渲染完成后再滚动
        setTimeout(() => {
          const element = document.querySelector(to.hash);
          if (element) {
            resolve({
              el: to.hash,
              behavior: 'smooth',
              top: 80, // 留出一些顶部空间，避免被固定头部遮挡
            });
          } else {
            // 如果元素不存在，滚动到顶部
            resolve({ top: 0, behavior: 'smooth' });
          }
        }, 100);
      });
    }

    // 默认滚动到页面顶部
    return { top: 0, behavior: 'smooth' };
  },
});

// 路由守卫，处理需要登录权限的页面
router.beforeEach((to, from, next) => {
  const userStore = useUserStore();

  // 需要认证的路由
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!userStore.isLoggedIn) {
      // 用户未登录，重定向到登录页
      next({
        path: '/login',
        query: { redirect: to.fullPath },
      });
    } else {
      // 用户已登录，允许访问
      next();
    }
  }
  // 仅限游客的路由（如登录页，已登录用户应该重定向走）
  else if (to.matched.some(record => record.meta.requiresGuest)) {
    if (userStore.isLoggedIn) {
      next({ name: 'UserProfile' });
    } else {
      // 游客可以访问
      next();
    }
  }
  // 其他页面
  else {
    next();
  }
});

export default router;
