import { usePlaygroundStore } from '@/stores/playground';
import { request } from '@/utils/request';
import type { Agent, ComboboxAgent, PlaygroundChatMessage, RunResponseContent, SessionEntry } from '@/types/playground';

export function useStreamHandler() {
  const playgroundStore = usePlaygroundStore();

  // 统一使用agents API路径（所有实体类型都通过agents接口访问）
  const getAgentApiPath = (agentId: string): string => {
    return `/api/v1/agents/${agentId}`;
  };

  const addMessage = (message: PlaygroundChatMessage) => {
    playgroundStore.addMessage(message);
  };

  const updateLastMessage = (updater: (_message: PlaygroundChatMessage) => void) => {
    const messages = playgroundStore.messages;
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      updater(lastMessage);

      if (lastMessage.content === undefined || lastMessage.content === null) {
        lastMessage.content = '';
      }

      playgroundStore.setMessages([...messages]);
    }
  };

  // 通用错误处理
  const handleStreamError = (error: unknown, errorMessage = '未知错误') => {
    console.error('Stream error:', error);
    updateLastMessage(message => {
      message.streamingError = true;
    });
    playgroundStore.setStreamingErrorMessage(error instanceof Error ? error.message : errorMessage);
  };

  // 清理错误消息
  const cleanupErrorMessages = () => {
    const messages = playgroundStore.messages;
    if (messages.length >= 2) {
      const lastMessage = messages[messages.length - 1];
      const secondLastMessage = messages[messages.length - 2];
      if (lastMessage.role === 'agent' && lastMessage.streamingError && secondLastMessage.role === 'user') {
        playgroundStore.setMessages(messages.slice(0, -2));
      }
    }
  };

  const handleStreamResponse = async (input: string | FormData) => {
    // 首先检查是否选择了智能体
    const selectedAgentId = playgroundStore.selectedAgent;
    if (!selectedAgentId) {
      playgroundStore.setStreamingErrorMessage('请先选择智能体');
      return;
    }

    playgroundStore.setIsStreaming(true);
    playgroundStore.setStreamingErrorMessage('');

    const formData = input instanceof FormData ? input : new FormData();
    if (typeof input === 'string') {
      formData.append('message', input);
    }

    cleanupErrorMessages();

    const userMessage = {
      role: 'user' as const,
      content: formData.get('message') as string,
      created_at: Math.floor(Date.now() / 1000),
    };

    addMessage(userMessage);

    const agentMessage = {
      role: 'agent' as const,
      content: '',
      tool_calls: [],
      streamingError: false,
      created_at: Math.floor(Date.now() / 1000) + 1,
    };
    addMessage(agentMessage);

    try {
      await streamChatResponse(formData, selectedAgentId);
    } catch (error) {
      handleStreamError(error);
    } finally {
      playgroundStore.setIsStreaming(false);
    }
  };

  const streamChatResponse = async (formData: FormData, selectedAgentId: string) => {
    try {
      let requestData: FormData | object;
      let contentType: string | undefined;

      // 统一使用FormData格式，后端会根据实体类型自动处理
      formData.append('stream', 'true');
      formData.append('session_id', playgroundStore.currentSessionId || '');
      requestData = formData;
      contentType = undefined; // 让浏览器自动设置 multipart/form-data

      await request.stream({
        url: `${getAgentApiPath(selectedAgentId)}/runs`,
        method: 'POST',
        data: requestData,
        headers: contentType ? { 'Content-Type': contentType } : undefined,
        onChunk: chunk => {
          processStreamChunk(chunk);
        },
        onError: error => {
          console.error('流式响应错误:', error);
          throw error;
        },
        onComplete: () => {},
      });
    } catch (error) {
      console.error('流式请求失败:', error);
      throw error;
    }
  };

  // 统一的会话处理函数
  const handleSession = (sessionId: string, title?: string) => {
    playgroundStore.setCurrentSessionId(sessionId);

    const finalTitle =
      title ||
      (() => {
        const messages = playgroundStore.messages;
        const userMessage = messages[messages.length - 2];
        if (userMessage?.role === 'user' && userMessage.content) {
          return userMessage.content.slice(0, 50);
        }

        for (let i = messages.length - 1; i >= 0; i--) {
          if (messages[i].role === 'user' && messages[i].content) {
            return (messages[i].content || '').slice(0, 50);
          }
        }
        return '新对话';
      })();

    const sessionData = {
      session_id: sessionId,
      title: finalTitle,
      created_at: Math.floor(Date.now() / 1000),
    };

    const currentSessions = playgroundStore.sessionsData || [];
    const sessionExists = currentSessions.some(session => session.session_id === sessionId);

    if (!sessionExists) {
      playgroundStore.setSessionsData([sessionData, ...currentSessions]);
    }
  };

  // 统一的消息更新函数
  const updateMessage = (updates: {
    content?: unknown;
    createdAt?: number;
    properties?: RunResponseContent;
    metrics?: any;
    toolCalls?: any[];
    reasoningSteps?: any;
    error?: boolean;
  }) => {
    updateLastMessage(message => {
      // 内容更新
      if (updates.content !== undefined) {
        if (typeof updates.content === 'string') {
          message.content = (message.content || '') + updates.content;
        } else if (updates.content !== null) {
          message.content = JSON.stringify(updates.content);
        }
      }

      // 确保content不为空
      if (message.content === undefined || message.content === null) {
        message.content = '';
      }

      // 时间更新
      if (updates.createdAt) {
        message.created_at = updates.createdAt;
      }

      // 属性更新
      if (updates.properties) {
        const props = updates.properties;
        if (props.tools) message.tool_calls = props.tools;
        if (props.images) message.images = props.images;
        if (props.audio) message.audio = props.audio;
        if (props.videos) message.videos = props.videos;
        if (props.response_audio) message.response_audio = props.response_audio;

        if (props.extra_data?.reasoning_steps || props.extra_data?.references) {
          message.extra_data = {
            ...(message.extra_data || {}),
            ...(props.extra_data?.reasoning_steps && { reasoning_steps: props.extra_data.reasoning_steps }),
            ...(props.extra_data?.references && { references: props.extra_data.references }),
          };
        }
      }

      // Metrics更新
      if (updates.metrics) {
        const m = updates.metrics;
        message.metrics = {
          time: m.time || 0,
          prompt_tokens: m.prompt_tokens || m.input_tokens || 0,
          completion_tokens: m.completion_tokens || m.output_tokens || 0,
          total_tokens: (m.prompt_tokens || m.input_tokens || 0) + (m.completion_tokens || m.output_tokens || 0),
        };
      }

      // 工具调用更新
      if (updates.toolCalls) {
        message.tool_calls = updates.toolCalls;
      }

      // 推理步骤更新
      if (updates.reasoningSteps) {
        message.extra_data = {
          ...(message.extra_data || {}),
          reasoning_steps: updates.reasoningSteps,
        };
      }

      // 错误状态
      if (updates.error) {
        message.streamingError = true;
      }
    });
  };

  const processStreamChunk = (chunk: RunResponseContent) => {
    const selectedAgentId = playgroundStore.selectedAgent;
    const agent = playgroundStore.agents.find(a => a.value === selectedAgentId);
    const agentType = agent?.agent_type || 'Agent';

    // 事件处理映射
    const eventHandlers: Record<string, () => void> = {
      // 会话开始事件（统一处理）
      TeamRunStarted: () => {
        if (chunk.session_id) handleSession(chunk.session_id);
      },

      RunStarted: () => {
        if (chunk.session_id) handleSession(chunk.session_id);
      },

      WorkflowStarted: () => {
        if (chunk.session_id) handleSession(chunk.session_id);
      },

      // 心跳事件（保持连接活跃，无需特殊处理）
      Heartbeat: () => {
        const heartbeatChunk = chunk as any;
        console.debug('收到心跳信号', heartbeatChunk.timestamp);
      },

      // 内容更新事件（统一处理）
      RunResponseEvent: () => {
        updateMessage({ content: chunk.content, createdAt: chunk.created_at });
      },

      TeamRunResponseContent: () => {
        updateMessage({ content: chunk.content, createdAt: chunk.created_at });
      },

      RunResponseContent: () => {
        // Workflow会话创建
        if (agentType === 'Workflow' && chunk.session_id && !playgroundStore.currentSessionId) {
          handleSession(chunk.session_id);
        }
        updateMessage({
          content: chunk.content,
          createdAt: chunk.created_at,
          properties: chunk,
        });
      },

      // 完成事件（统一处理）
      WorkflowCompleted: () => {
        playgroundStore.setIsStreaming(false);
        updateMessage({ content: chunk.content, createdAt: chunk.created_at });
      },

      TeamRunCompleted: () => {
        playgroundStore.setIsStreaming(false);
        updateMessage({ content: chunk.content, createdAt: chunk.created_at });
      },

      RunCompleted: () => {
        const assistantMessage = chunk.messages?.find((msg: any) => msg.role === 'assistant');
        if (assistantMessage?.metrics) {
          updateMessage({ metrics: assistantMessage.metrics });
        }
        playgroundStore.setIsStreaming(false);
      },

      // 工具调用事件
      ToolCallStarted: () => updateMessage({ toolCalls: chunk.tools }),
      ToolCallCompleted: () => updateMessage({ toolCalls: chunk.tools }),

      // 推理事件
      ReasoningStarted: () => updateMessage({ reasoningSteps: chunk.extra_data?.reasoning_steps }),
      ReasoningStep: () => updateMessage({ reasoningSteps: chunk.extra_data?.reasoning_steps }),
      ReasoningCompleted: () => updateMessage({ reasoningSteps: chunk.extra_data?.reasoning_steps }),

      // 错误事件
      RunError: () => {
        updateMessage({ error: true });
        playgroundStore.setStreamingErrorMessage((chunk.content as string) || '运行出错');
      },
    };

    // 执行对应的事件处理器
    const handler = eventHandlers[chunk.event];
    if (handler) {
      handler();
    } else {
      console.warn('未处理的事件类型:', chunk.event);
    }
  };

  // 通用API调用包装器
  const withErrorHandling = async <T>(apiCall: () => Promise<T>, onError?: (_error: unknown) => void, defaultValue?: T): Promise<T | undefined> => {
    try {
      return await apiCall();
    } catch (error) {
      console.error('API调用失败:', error);
      if (onError) {
        onError(error);
      }
      return defaultValue;
    }
  };

  const fetchAgents = async () => {
    playgroundStore.setIsLoading(true);

    const rawAgents = await withErrorHandling(
      () => request.get<Agent[]>('/api/v1/agents'),
      () => playgroundStore.setAgents([]),
      []
    );

    if (rawAgents) {
      const agents: ComboboxAgent[] = rawAgents.map((agent: Agent) => ({
        value: agent.entity_id,
        label: agent.name,
        description: agent.description,
        storage: false,
        agent_type: agent.agent_type || 'Agent',
      }));

      playgroundStore.setAgents(agents);

      const hasStorageSupport = agents.some(agent => agent.storage);
      playgroundStore.setHasStorage(hasStorageSupport);
    }

    playgroundStore.setIsLoading(false);
  };

  const fetchSessions = async () => {
    const selectedAgentId = playgroundStore.selectedAgent;
    if (!selectedAgentId) {
      playgroundStore.setSessionsData([]);
      return;
    }

    playgroundStore.setIsSessionsLoading(true);

    const sessions = await withErrorHandling(
      () => request.get<SessionEntry[]>(`${getAgentApiPath(selectedAgentId)}/sessions`),
      () => playgroundStore.setSessionsData([]),
      []
    );

    playgroundStore.setSessionsData(sessions || []);
    playgroundStore.setIsSessionsLoading(false);
  };

  const fetchSessionData = async (sessionId: string, agentId: string): Promise<PlaygroundChatMessage[]> => {
    const response = await withErrorHandling(() => request.get(`${getAgentApiPath(agentId)}/sessions/${sessionId}`), undefined, null);

    if (!response) {
      return [];
    }

    const sessionHistory = response.runs ? response.runs : response.memory?.runs;

    if (!sessionHistory || !Array.isArray(sessionHistory)) {
      console.warn('会话历史数据格式不正确:', sessionHistory);
      return [];
    }

    const messagesForPlayground = sessionHistory.flatMap((run: any) => {
      const filteredMessages: PlaygroundChatMessage[] = [];

      if (run.message) {
        filteredMessages.push({
          role: 'user',
          content: run.message.content || '',
          created_at: run.message.created_at,
        });
      }

      if (run.response) {
        const toolCalls = [
          ...(run.response.tools ?? []),
          ...(run.response.extra_data?.reasoning_messages || []).reduce((acc: any[], msg: any) => {
            if (msg.role === 'tool') {
              acc.push({
                role: msg.role,
                content: msg.content,
                tool_call_id: msg.tool_call_id ?? '',
                tool_name: msg.tool_name ?? '',
                tool_args: msg.tool_args ?? {},
                tool_call_error: msg.tool_call_error ?? false,
                metrics: msg.metrics ?? { time: 0 },
                created_at: msg.created_at ?? Math.floor(Date.now() / 1000),
              });
            }
            return acc;
          }, []),
        ];

        // 从response的messages中提取metrics信息
        const responseMetrics = run.response.messages?.find((msg: any) => msg.role === 'assistant')?.metrics;

        filteredMessages.push({
          role: 'agent',
          content: (run.response.content as string) || '',
          tool_calls: toolCalls.length > 0 ? toolCalls : undefined,
          extra_data: run.response.extra_data,
          images: run.response.images,
          videos: run.response.videos,
          audio: run.response.audio,
          response_audio: run.response.response_audio,
          created_at: run.response.created_at,
          metrics: responseMetrics
            ? {
                time: responseMetrics.time,
                prompt_tokens: responseMetrics.prompt_tokens || responseMetrics.input_tokens,
                completion_tokens: responseMetrics.completion_tokens || responseMetrics.output_tokens,
                total_tokens: responseMetrics.prompt_tokens + responseMetrics.completion_tokens || responseMetrics.input_tokens + responseMetrics.output_tokens,
              }
            : undefined,
        });
      }

      return filteredMessages;
    });

    return messagesForPlayground.map((message: PlaygroundChatMessage) => {
      if (Array.isArray(message.content)) {
        const textContent = message.content
          .filter((item: { type: string }) => item.type === 'text')
          .map((item: any) => item.text)
          .join(' ');

        return {
          ...message,
          content: textContent || '',
        };
      }

      if (typeof message.content !== 'string') {
        return {
          ...message,
          content: message.content ? JSON.stringify(message.content, null, 2) : '',
        };
      }

      return {
        ...message,
        content: message.content || '',
      };
    });
  };

  // 初始化 playground
  const initializePlayground = async () => {
    playgroundStore.setIsEndpointLoading(true);

    try {
      // 设置为已加载状态
      playgroundStore.setHydrated(true);

      // 加载智能体列表
      await fetchAgents();

      // 如果有智能体且已选择智能体，则加载会话
      if (playgroundStore.agents.length > 0 && playgroundStore.selectedAgent) {
        await fetchSessions();
      }
    } catch (error) {
      console.error('初始化 playground 失败:', error);
    } finally {
      playgroundStore.setIsEndpointLoading(false);
    }
  };

  // 停止流式传输
  const stopStreaming = () => {
    playgroundStore.setIsStreaming(false);
  };

  const handleNewChat = async () => {
    // 先设置会话ID为null，但不立即清空消息，避免按钮闪烁
    playgroundStore.setCurrentSessionId(null);

    // 延迟清空消息，让UI有时间适应
    setTimeout(() => {
      playgroundStore.clearMessages();
    }, 50);

    if (playgroundStore.chatInputRef) {
      playgroundStore.chatInputRef.focus();
    }
  };

  return {
    handleStreamResponse,
    processStreamChunk,
    addMessage,
    updateLastMessage,
    fetchAgents,
    fetchSessions,
    fetchSessionData,
    initializePlayground,
    stopStreaming,
    handleNewChat,
    getAgentApiPath,
  };
}
