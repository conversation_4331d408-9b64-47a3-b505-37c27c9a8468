<template>
  <div class="flex h-screen bg-gray-200 dark:bg-[#1a1a1a] transition-colors duration-300">
    <!-- 侧边栏 -->
    <div class="relative inset-y-0 left-0">
      <PlaygroundSidebar />
    </div>

    <!-- 聊天区域 -->
    <div class="flex-1 flex flex-col min-w-0">
      <ChatArea />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, watch } from 'vue';
import { useHead } from '@vueuse/head';
import { useI18n } from 'vue-i18n';
import { useRouter, useRoute } from 'vue-router';
import { usePlaygroundStore } from '@/stores/playground';
import PlaygroundSidebar from '@/components/playground/Sidebar/PlaygroundSidebar.vue';
import ChatArea from '@/components/playground/ChatArea/ChatArea.vue';
import { useStreamHandler } from '@/composables/useStreamHandler';

const playgroundStore = usePlaygroundStore();
const { handleNewChat } = useStreamHandler();
const { t } = useI18n();
const router = useRouter();
const route = useRoute();

// 计算属性
computed(() => playgroundStore.hasMessages);
// 监听currentSessionId变化，实现优雅的URL同步
watch(
  () => playgroundStore.currentSessionId,
  (newSessionId, oldSessionId) => {
    // 当session从null变为有值时（新对话创建），且当前URL中没有该session_id时，更新URL
    if (newSessionId && !oldSessionId && playgroundStore.selectedAgent) {
      const currentSessionIdInUrl = route.query['session-id'];
      if (currentSessionIdInUrl !== newSessionId) {
        router
          .push({
            name: 'Playground',
            query: {
              'agent-id': playgroundStore.selectedAgent,
              'session-id': newSessionId,
            },
          })
          .catch(error => {
            // 忽略导航错误（比如已经在目标路由）
            console.log('Router navigation handled:', error);
          });
      }
    }
  },
  { immediate: false }
);

// 快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
    event.preventDefault();
    handleNewChat();
  }
};

// 设置页面元数据
useHead({
  title: t('playground.title'),
  meta: [
    {
      name: 'description',
      content: t('playground.description'),
    },
  ],
});

onMounted(async () => {
  playgroundStore.setHydrated(true);
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  // 移除快捷键监听
  document.removeEventListener('keydown', handleKeydown);
});
</script>
