<template>
  <main class="relative flex flex-grow flex-col transition-all duration-500 bg-gray-200 dark:bg-[#1a1a1a]">
    <!-- 消息区域 -->
    <div class="relative z-10 flex-1 overflow-hidden">
      <MessageArea @send-suggestion="handleSendSuggestion" @open-sidebar="handleOpenSidebar" />
    </div>

    <!-- 输入区域 -->
    <div class="relative z-10 backdrop-blur-lg transition-all duration-500 bg-gray-100 dark:bg-[#121212]">
      <!-- 高级动效分割线 -->
      <div class="absolute top-0 left-0 right-0 h-[3px] overflow-hidden">
        <!-- 深度阴影层 -->
        <div class="absolute top-[1px] left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300/20 dark:via-gray-600/10 to-transparent"></div>

        <!-- 基础渐变线 -->
        <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-gray-300/25 dark:via-gray-400/20 to-transparent"></div>

        <!-- 微妙的流动光效 -->
        <div class="absolute top-0 left-0 right-0 h-px">
          <div class="subtle-flow absolute top-0 h-px w-32 bg-gradient-to-r from-transparent via-blue-400/30 to-transparent"></div>
        </div>

        <!-- 轻柔的中心光晕 -->
        <div class="absolute top-0 left-1/2 transform -translate-x-1/2 w-20 h-px bg-gradient-to-r from-transparent via-blue-400/25 to-transparent gentle-pulse"></div>

        <!-- 边缘渐变增强 -->
        <div class="absolute top-0 left-0 w-12 h-px bg-gradient-to-r from-gray-400/15 dark:from-gray-600/10 to-transparent edge-fade-left"></div>
        <div class="absolute top-0 right-0 w-12 h-px bg-gradient-to-l from-gray-400/15 dark:from-gray-600/10 to-transparent edge-fade-right"></div>

        <!-- 微光点缀 -->
        <div class="absolute top-0 left-1/3 w-1 h-px bg-blue-400/20 micro-sparkle" style="animation-delay: 2s"></div>
        <div class="absolute top-0 right-1/3 w-1 h-px bg-blue-400/20 micro-sparkle" style="animation-delay: 5s"></div>

        <!-- 反光质感层 -->
        <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/8 dark:via-white/5 to-transparent reflection-sweep"></div>
      </div>

      <div class="mx-auto max-w-4xl px-6 py-4">
        <ChatInput />

        <!-- 底部提示信息 -->
        <div class="mt-3 flex items-center justify-center gap-4 text-xs transition-all duration-300">
          <div class="flex items-center gap-2 text-slate-700 dark:text-gray-300">
            <div class="relative">
              <div class="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
              <div class="absolute inset-0 w-2 h-2 rounded-full bg-green-400 animate-ping opacity-20"></div>
            </div>
            <span class="font-medium text-gray-900 dark:text-white"> {{ $t('playground.status.ready') }} </span>
          </div>

          <!-- 分隔符 -->
          <div class="w-px h-3 bg-gradient-to-b from-transparent via-gray-500 dark:via-gray-400 to-transparent opacity-60"></div>

          <div class="flex items-center gap-2">
            <div class="flex gap-1">
              <div class="w-1 h-1 rounded-full bg-blue-400 animate-bounce" style="animation-delay: 0ms"></div>
              <div class="w-1 h-1 rounded-full bg-purple-400 animate-bounce" style="animation-delay: 150ms"></div>
              <div class="w-1 h-1 rounded-full bg-pink-400 animate-bounce" style="animation-delay: 300ms"></div>
            </div>
            <span class="text-gray-700 dark:text-gray-300"> {{ $t('playground.status.textSupport') }} </span>
          </div>

          <!-- Token统计 -->
          <template v-if="playgroundStore.messages.length > 0">
            <!-- 分隔符 -->
            <div class="w-px h-3 bg-gradient-to-b from-transparent via-gray-500 dark:via-gray-400 to-transparent opacity-60"></div>

            <div class="flex items-center gap-2">
              <TokenUsage :messages="playgroundStore.messages" show-details />
            </div>
          </template>
        </div>
      </div>
    </div>
  </main>
</template>

<script lang="ts" setup>
import { usePlaygroundStore } from '@/stores/playground';
import { useStreamHandler } from '@/composables/useStreamHandler';
import { useMessage } from 'naive-ui';
import { useI18n } from 'vue-i18n';
import MessageArea from './MessageArea.vue';
import ChatInput from './ChatInput.vue';
import TokenUsage from './TokenUsage.vue';

interface Emits {
  (e: 'open-sidebar'): void;
}

const emit = defineEmits<Emits>();
const playgroundStore = usePlaygroundStore();
const { handleStreamResponse } = useStreamHandler();
const message = useMessage();
const { t } = useI18n();

// 处理建议发送
const handleSendSuggestion = async (suggestion: string) => {
  try {
    await handleStreamResponse(suggestion);
  } catch (error) {
    message.error(t('playground.chat.sendFailed', { error: error instanceof Error ? error.message : String(error) }));
  }
};

// 处理打开侧边栏
const handleOpenSidebar = () => {
  emit('open-sidebar');
};
</script>

<style scoped>
/* 低调沉稳的分割线动效 */

/* 微妙的流动效果 */
.subtle-flow {
  animation: subtleFlow 10s ease-in-out infinite;
  left: -128px;
}

@keyframes subtleFlow {
  0% {
    left: -128px;
    opacity: 0;
  }
  20% {
    opacity: 0.3;
  }
  80% {
    opacity: 0.3;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

/* 轻柔的脉冲效果 */
.gentle-pulse {
  animation: gentlePulse 6s ease-in-out infinite;
}

@keyframes gentlePulse {
  0%,
  100% {
    opacity: 0.25;
    transform: translateX(-50%) scaleX(1);
  }
  50% {
    opacity: 0.4;
    transform: translateX(-50%) scaleX(1.15);
  }
}

/* 边缘渐变动效 */
.edge-fade-left {
  animation: edgeFadeLeft 8s ease-in-out infinite;
}

.edge-fade-right {
  animation: edgeFadeRight 8s ease-in-out infinite;
}

@keyframes edgeFadeLeft {
  0%,
  100% {
    opacity: 0.1;
    transform: scaleX(1);
  }
  50% {
    opacity: 0.3;
    transform: scaleX(1.2);
  }
}

@keyframes edgeFadeRight {
  0%,
  100% {
    opacity: 0.1;
    transform: scaleX(1);
  }
  50% {
    opacity: 0.3;
    transform: scaleX(1.2);
  }
}

/* 微光点缀效果 */
.micro-sparkle {
  animation: microSparkle 12s ease-in-out infinite;
}

@keyframes microSparkle {
  0%,
  90%,
  100% {
    opacity: 0;
    transform: scaleX(1);
  }
  5%,
  15% {
    opacity: 0.6;
    transform: scaleX(3);
  }
  10% {
    opacity: 0.8;
    transform: scaleX(4);
  }
}

/* 反光质感效果 */
.reflection-sweep {
  animation: reflectionSweep 15s ease-in-out infinite;
}

@keyframes reflectionSweep {
  0%,
  95%,
  100% {
    opacity: 0;
    transform: translateX(-100%) skewX(-20deg);
  }
  2% {
    opacity: 0.05;
    transform: translateX(-50%) skewX(-20deg);
  }
  5% {
    opacity: 0.1;
    transform: translateX(100%) skewX(-20deg);
  }
  7% {
    opacity: 0;
    transform: translateX(150%) skewX(-20deg);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .max-w-4xl {
    max-width: 100%;
  }
  .px-6 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (max-width: 480px) {
  .px-6 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
}

/* 减弱动画效果（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .subtle-flow,
  .gentle-pulse,
  .edge-fade-left,
  .edge-fade-right,
  .micro-sparkle,
  .reflection-sweep {
    animation: none;
  }
}
</style>
