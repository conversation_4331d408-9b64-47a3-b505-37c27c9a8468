from datetime import datetime
from uuid import UUID, uuid4

from sqlmodel import Field


# 混入类
class MixID:
    """整数ID主键混入类"""
    id: int = Field(primary_key=True, title='ID')


class MixUUID:
    """UUID主键混入类"""
    id: UUID | None = Field(default_factory=uuid4, primary_key=True, title='UUID')


class MixDeleted:
    """软删除混入类"""
    is_deleted: bool = Field(default=False, title='是否删除')


class MixRemark:
    """备注混入类"""
    remark: str | None = Field(default=None, title='备注')


class MixIdentity:
    """自增ID混入类"""
    id: int = Field(title='ID')


class MixCreateTime:
    """创建时间混入类"""
    create_time: datetime | None = Field(title='创建时间', sa_column_kwargs={"server_default": "CURRENT_TIMESTAMP"})


class MixUpdateTime:
    """更新时间混入类"""
    update_time: datetime | None = Field(title='更新时间', sa_column_kwargs={"server_default": "CURRENT_TIMESTAMP"})


class MixTime(MixCreateTime, MixUpdateTime):
    """时间戳混入类"""
    create_time: datetime | None = Field(title='创建时间', sa_column_kwargs={"server_default": "CURRENT_TIMESTAMP"})
    update_time: datetime | None = Field(title='更新时间', sa_column_kwargs={"server_default": "CURRENT_TIMESTAMP"})
