------------------------------------------
-- RBAC: 角色和权限表
-- 基于角色的访问控制系统，管理用户角色和权限
------------------------------------------
-- 角色表
-- 定义系统中的不同角色及其权限级别
CREATE TABLE rbac.roles
(
    id          SERIAL PRIMARY KEY,                                         -- 角色ID
    name        VARCHAR(50) UNIQUE NOT NULL CHECK (LENGTH(TRIM(name)) > 0), -- 角色名称
    description VARCHAR(255),                                               -- 角色描述
    is_system   BOOLEAN            NOT NULL DEFAULT FALSE,                  -- 是否为系统角色
    editable    BOOLEAN            NOT NULL DEFAULT TRUE,                   -- 是否可编辑
    priority    SMALLINT           NOT NULL DEFAULT 0,                      -- 角色优先级
    create_time TIMESTAMPTZ        NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
    update_time TIMESTAMPTZ        NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 更新时间
    is_deleted   BOOLEAN            NOT NULL DEFAULT FALSE                   -- 是否已删除
);

CREATE INDEX idx_roles_name ON rbac.roles (name) WHERE is_deleted = FALSE;
CREATE INDEX idx_roles_priority ON rbac.roles (priority) WHERE is_deleted = FALSE;

-- 权限表
-- 定义系统中所有可用的权限
CREATE TABLE rbac.permissions
(
    id          SERIAL PRIMARY KEY,                                         -- 权限ID
    name        VARCHAR(50) UNIQUE NOT NULL CHECK (LENGTH(TRIM(name)) > 0), -- 权限名称
    category    VARCHAR(50)        NOT NULL,                                -- 权限类别
    resource    VARCHAR(50)        NOT NULL,                                -- 权限资源
    action      VARCHAR(50)        NOT NULL,                                -- 权限动作
    description VARCHAR(255),                                               -- 权限描述
    is_system   BOOLEAN            NOT NULL DEFAULT FALSE,                  -- 是否为系统权限
    conditions  JSONB,                                                      -- 权限条件
    create_time TIMESTAMPTZ        NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
    update_time TIMESTAMPTZ        NOT NULL DEFAULT CURRENT_TIMESTAMP,      -- 更新时间
    is_deleted   BOOLEAN            NOT NULL DEFAULT FALSE,                  -- 是否已删除
    UNIQUE (resource, action)
);

CREATE INDEX idx_permissions_name ON rbac.permissions (name) WHERE is_deleted = FALSE;
CREATE INDEX idx_permissions_category ON rbac.permissions (category) WHERE is_deleted = FALSE;
CREATE INDEX idx_permissions_resource_action ON rbac.permissions (resource, action) WHERE is_deleted = FALSE;

-- 角色-权限关联表
-- 定义角色与权限之间的多对多关系
CREATE TABLE rbac.role_permissions
(
    role_id       INTEGER     NOT NULL,                           -- 角色ID
    permission_id INTEGER     NOT NULL,                           -- 权限ID
    conditions    JSONB,                                          -- 特定条件
    create_time   TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time   TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    is_deleted     BOOLEAN     NOT NULL DEFAULT FALSE,             -- 是否已删除
    PRIMARY KEY (role_id, permission_id)
);

CREATE INDEX idx_role_permissions_role ON rbac.role_permissions (role_id) WHERE is_deleted = FALSE;
CREATE INDEX idx_role_permissions_permission ON rbac.role_permissions (permission_id) WHERE is_deleted = FALSE;

-- 用户-角色关联表
-- 定义用户与角色之间的多对多关系
CREATE TABLE rbac.user_roles
(
    user_id     UUID        NOT NULL,                           -- 用户ID
    role_id     INTEGER     NOT NULL,                           -- 角色ID
    conditions  JSONB,                                          -- 特定条件
    create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    is_deleted   BOOLEAN     NOT NULL DEFAULT FALSE,             -- 是否已删除
    PRIMARY KEY (user_id, role_id)
);

CREATE INDEX idx_user_roles_user ON rbac.user_roles (user_id) WHERE is_deleted = FALSE;
CREATE INDEX idx_user_roles_role ON rbac.user_roles (role_id) WHERE is_deleted = FALSE;

-- 清理用户数据函数
-- 安全地删除用户及其相关数据
CREATE OR REPLACE FUNCTION rbac.cleanup_user_data(user_uuid UUID)
    RETURNS VOID AS
$$
BEGIN
    -- 标记为删除而不是实际删除
    UPDATE auth.users
    SET is_deleted      = TRUE,
        account_status = 'is_deleted',
        update_time    = CURRENT_TIMESTAMP
    WHERE id = user_uuid;

    -- 标记相关数据为删除
    UPDATE auth.user_auth_providers
    SET is_deleted   = TRUE,
        update_time = CURRENT_TIMESTAMP
    WHERE user_id = user_uuid;

    UPDATE rbac.user_roles
    SET is_deleted   = TRUE,
        update_time = CURRENT_TIMESTAMP
    WHERE user_id = user_uuid;

    -- 记录状态变更
    INSERT INTO auth.user_status_history (user_id, status, reason)
    VALUES (user_uuid, 'is_deleted', '用户账户已删除');
END;
$$ LANGUAGE plpgsql;

-- 角色权限验证函数
-- 验证角色-权限关联的有效性
CREATE OR REPLACE FUNCTION rbac.validate_role_permission()
    RETURNS TRIGGER AS
$$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM rbac.roles WHERE id = NEW.role_id AND is_deleted = FALSE) THEN
        RAISE EXCEPTION '无效的角色ID: %', NEW.role_id;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM rbac.permissions WHERE id = NEW.permission_id AND is_deleted = FALSE) THEN
        RAISE EXCEPTION '无效的权限ID: %', NEW.permission_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 用户角色验证函数
-- 验证用户-角色关联的有效性
CREATE OR REPLACE FUNCTION rbac.validate_user_role()
    RETURNS TRIGGER AS
$$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM auth.users WHERE id = NEW.user_id AND is_deleted = FALSE) THEN
        RAISE EXCEPTION '无效的用户ID: %', NEW.user_id;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM rbac.roles WHERE id = NEW.role_id AND is_deleted = FALSE) THEN
        RAISE EXCEPTION '无效的角色ID: %', NEW.role_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- 应用验证触发器
-- 在插入或更新时验证数据完整性
CREATE TRIGGER validate_role_permission_trigger
    BEFORE INSERT OR UPDATE
    ON rbac.role_permissions
    FOR EACH ROW
EXECUTE FUNCTION rbac.validate_role_permission();

CREATE TRIGGER validate_user_role_trigger
    BEFORE INSERT OR UPDATE
    ON rbac.user_roles
    FOR EACH ROW
EXECUTE FUNCTION rbac.validate_user_role();


-- 更新时间戳触发器
-- 自动更新记录的更新时间
CREATE TRIGGER update_roles_timestamp
    BEFORE UPDATE
    ON rbac.roles
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();

CREATE TRIGGER update_permissions_timestamp
    BEFORE UPDATE
    ON rbac.permissions
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();

CREATE TRIGGER update_role_permissions_timestamp
    BEFORE UPDATE
    ON rbac.role_permissions
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();

CREATE TRIGGER update_user_roles_timestamp
    BEFORE UPDATE
    ON rbac.user_roles
    FOR EACH ROW
EXECUTE FUNCTION utils.update_timestamp();


-- 初始数据
-- 插入系统默认角色
INSERT INTO rbac.roles (name, description, is_system, priority)
VALUES ('super_admin', '超级管理员，拥有所有权限', true, 100),
       ('admin', '系统管理员，拥有管理访问权限', true, 90),
       ('manager', '组织管理者，拥有提升的权限', true, 50),
       ('user', '具有标准权限的普通用户', true, 10),
       ('guest', '受限访问的访客用户', true, 0);

-- 插入系统默认权限
INSERT INTO rbac.permissions (name, category, resource, action, description, is_system, conditions)
VALUES
    -- 用户权限
    ('user:read', 'user', 'user', 'read', '查看用户信息', true, null),
    ('user:create', 'user', 'user', 'create', '创建用户', true, null),
    ('user:update', 'user', 'user', 'update', '更新用户信息', true, null),
    ('user:delete', 'user', 'user', 'delete', '删除用户', true, null),
    -- 角色权限
    ('role:read', 'role', 'role', 'read', '查看角色', true, null),
    ('role:create', 'role', 'role', 'create', '创建角色', true, null),
    ('role:update', 'role', 'role', 'update', '更新角色', true, null),
    -- 更细粒度的角色删除权限及条件
    ('role:delete:custom', 'role', 'role', 'delete_custom', '删除自定义（非系统）角色', true, '{
      "only": [
        "custom_roles"
      ]
    }'::jsonb),
    ('role:delete:any', 'role', 'role', 'delete_any', '删除任何角色（包括系统角色）', true, null),
    -- 设置权限
    ('settings:read', 'settings', 'settings', 'read', '查看系统设置', true, null),
    ('settings:update', 'settings', 'settings', 'update', '更新系统设置', true, null);

-- 为各角色分配初始权限
-- 分配所有权限给超级管理员
INSERT INTO rbac.role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM rbac.roles r
         CROSS JOIN rbac.permissions p
WHERE r.name = 'super_admin';

-- 分配适当的权限给管理员（除了'role:delete:any'之外的所有权限）
INSERT INTO rbac.role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM rbac.roles r
         CROSS JOIN rbac.permissions p
WHERE r.name = 'admin'
  AND p.name != 'role:delete:any';

-- 管理者获得用户管理和读取权限
INSERT INTO rbac.role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM rbac.roles r
         CROSS JOIN rbac.permissions p
WHERE r.name = 'manager'
  AND p.name IN ('user:read', 'user:create', 'user:update', 'role:read', 'settings:read');

-- 普通用户获得基本读取权限
INSERT INTO rbac.role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM rbac.roles r
         CROSS JOIN rbac.permissions p
WHERE r.name = 'user'
  AND p.name IN ('user:read', 'settings:read');
