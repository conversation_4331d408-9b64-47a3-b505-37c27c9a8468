{"mcpServers": {"sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "@21st-dev/magic": {"command": "cmd", "args": ["/c", "npx", "-y", "@21st-dev/magic@latest", "API_KEY=\"e3c9cf59dcb0ecd2674c5ec8783015f663db0f297414c1ad4f774fc1d4c0f63d\""]}, "mcp-doc-agno": {"url": "https://gitmcp.io/agno-agi/agno"}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 60000, "env": {"FORCE_WEB": "true", "MCP_DEBUG": "false"}, "autoApprove": ["interactive_feedback"]}, "Playwright": {"command": "npx", "args": ["@playwright/mcp@latest"], "env": {}}, "browsermcp": {"command": "npx", "args": ["@browsermcp/mcp@latest"]}}}