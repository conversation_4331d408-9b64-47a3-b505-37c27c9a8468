# 全局性能优化
worker_processes auto;
worker_rlimit_nofile 65535;
events {
    worker_connections 4096;
    multi_accept on;
}

# HTTP 配置块
http {
    # 基础设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # 缓冲区优化
    client_max_body_size 16M;
    client_body_buffer_size 128k;

    # MIME 类型
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    # 统一的 SSL 配置
    ssl_certificate /etc/nginx/cert/aiquant.io.pem;
    ssl_certificate_key /etc/nginx/cert/aiquant.io.key;

    # SSL 优化
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers EECDH+AESGCM:EDH+AESGCM;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_buffer_size 4k;

    # HSTS 头部
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 限速配置 - 移动到此处，http 块内
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;

    # HTTP 重定向到 HTTPS
    server {
        listen 80;
        server_name .aiquant.io;
        return 301 https://$host$request_uri;
    }

    # 主站点配置
    server {
        listen 443 ssl; # managed by Certbot
        server_name aiquant.io www.aiquant.io;

        # 静态文件缓存
        location /coin/img/ {
            alias /root/PycharmProjects/aiquant2022/coin_img/;
            expires 30d; # 缓存静态文件
            add_header Cache-Control "public, max-age=2592000";
        }

        location /image/ {
            alias /root/PycharmProjects/aiquant2022/image/;
            expires 30d; # 缓存静态文件
            add_header Cache-Control "public, max-age=2592000";
        }

        location / {
            root /home/<USER>/aiquant-frontend/dist;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
    }

    # API 相关配置
    map $host $upstream {
        default http://127.0.0.1:8888; # api.aiquant.io

        testapi.aiquant.io http://127.0.0.1:8889;
        supervisor.aiquant.io http://127.0.0.1:9001;
        airflow.aiquant.io http://127.0.0.1:9081;
        grafana.aiquant.io http://127.0.0.1:3000;
        mq.aiquant.io http://127.0.0.1:15672;

        registry.aiquant.io http://127.0.0.1:5002; # docker镜像仓库UI
        portainer.aiquant.io http://127.0.0.1:5003; # docker管理工具

        bark.aiquant.io http://127.0.0.1:5006; # bark服务
        v.aiquant.io http://127.0.0.1:5007; # 科学上网
        rabbitmq.aiquant.io http://127.0.0.1:5008; # rabbitmq

    }

    server {
        listen 443 ssl; # managed by Certbot
        server_name ~^(.*\.)?aiquant\.io$;  # 匹配所有以 aiquant.io 结尾的子域名

        location / {
            proxy_pass $upstream;

            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # 处理 WebSocket
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # 超时设置
            proxy_read_timeout 300;
            proxy_connect_timeout 300;
            proxy_send_timeout 300;
        }
    }

}