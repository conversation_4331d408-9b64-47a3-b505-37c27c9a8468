services:
  vmess:
    image: teddysun/xray:latest
    container_name: vmess
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
    expose:
      - "8072"
    volumes:
      - ./vmess_config.json:/etc/xray/config.json:ro
    command: xray -c /etc/xray/config.json
    networks:
      - proxy-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.vmess-router.rule=Host(`v.aiquant.io`) && PathPrefix(`/ws`)"
      - "traefik.http.routers.vmess-router.entrypoints=websecure"
      - "traefik.http.routers.vmess-router.tls=true"
      - "traefik.http.routers.vmess-router.tls.certresolver=letsencryptResolver"
      - "traefik.http.services.vmess-service.loadbalancer.server.port=8072" # 修正服务名称

  vless:
    image: teddysun/xray:latest
    container_name: vless
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
    expose:
      - "8073"
    volumes:
      - ./vless_config.json:/etc/xray/config.json:ro
    command: xray -c /etc/xray/config.json
    networks:
      - proxy-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.vless-router.rule=Host(`v2.aiquant.io`) && PathPrefix(`/ws`)"
      - "traefik.http.routers.vless-router.entrypoints=websecure"
      - "traefik.http.routers.vless-router.tls=true"
      - "traefik.http.routers.vless-router.tls.certresolver=letsencryptResolver"
      - "traefik.http.services.vless-service.loadbalancer.server.port=8073"

networks:
  proxy-network:
    external: true