# 通用
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.log
logs/
tmp/
.vscode/
.idea/

# Python / 后端
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
venv/
.venv/

# Node.js / 前端
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
coverage/
frontend/build/
frontend/dist/

# iOS / Swift
xcuserdata/
*.xcscmblueprint
*.xccheckout
DerivedData/
*.moved-aside
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
*.hmap
*.ipa
*.dSYM.zip
*.dSYM
.build/
Pods/
Carthage/

# Docker
.docker-volumes/ 
# Private individual user cursor rules
.cursor/rules/_*.mdc
/agent-ui/
your-ai-agent.code-workspace
