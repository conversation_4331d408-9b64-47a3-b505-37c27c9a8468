from typing import List, Any

import typer

from app.db.db_orm_base import QueryMethods
from app.db.db_view import View
from quant.exchange.binance_sync import MarketType, BinanceSync, logger

app = typer.Typer()


@app.command()
def run(
        action: int = typer.Argument(1, help='可选：1,2,3,4'),
        market_type: str = typer.Option(MarketType.SPOT, help='市场类型'),
        timeframe: str = typer.Option('15m', help='K线时间周期'),
):
    if action == 1:
        binance_sync = BinanceSync()
        binance_sync.sync_market_symbols(market_type)
        binance_sync.sync_market_tickers(market_type)
        view = View.get_view(view_name='crypto.v_symbol_spot_volume_rank')
        symbols: List[Any] = QueryMethods.filter_sync(view, market_type=market_type, symbol='BTCUSDT', exchange='binance')
        logger.info(f'查询到 {len(symbols)} 个交易对')
        for symbol in symbols:
            logger.info(f'{symbol.symbol} 开始同步')
            binance_sync.sync_kline(market_type=market_type, symbol=symbol.symbol, timeframe=timeframe, start_date='2025-05-01')
            logger.info(f'{symbol.symbol} 同步完成')


if __name__ == '__main__':
    app()
