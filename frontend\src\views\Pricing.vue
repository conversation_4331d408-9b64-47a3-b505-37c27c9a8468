<template>
  <div class="bg-gray-50 dark:bg-dark transition-colors duration-300 py-16">
    <div class="container">
      <!-- 计费周期切换 -->
      <div class="flex items-center justify-center gap-4 mb-8">
        <span class="text-gray-700 dark:text-gray-300">月付</span>
        <n-switch v-model:value="isYearly" @update:value="toggleBillingCycle" size="large" class="mx-2">
          <template #checked>年付</template>
          <template #unchecked>月付</template>
        </n-switch>
        <span class="text-gray-700 dark:text-gray-300">年付</span>
        <span class="text-sm bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-3 py-1 rounded-full ml-2">省20%</span>
      </div>

      <!-- 价格卡片 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 max-w-5xl mx-auto">
        <!-- 免费计划 -->
        <div class="feature-card p-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-secondary shadow-sm hover:shadow-md transition-all duration-300">
          <div class="text-center mb-6">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">免费版</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">个人体验使用</p>
            <div class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              ¥0
              <span class="text-base text-gray-500 dark:text-gray-400 font-normal">/月</span>
            </div>
          </div>

          <ul class="space-y-3 mb-6">
            <li v-for="feature in freeFeatures" :key="feature" class="flex items-center gap-3">
              <n-icon size="16" class="text-green-500">
                <CheckmarkCircleOutline />
              </n-icon>
              <span class="text-gray-700 dark:text-gray-300 text-sm">{{ feature }}</span>
            </li>
          </ul>

          <button class="btn btn-outline w-full transition-all" @click="selectPlan('free')">开始使用</button>
        </div>

        <!-- 专业计划 -->
        <div class="feature-card p-6 rounded-lg border-2 border-primary bg-white dark:bg-dark-secondary shadow-lg relative hover:shadow-xl transition-all duration-300">
          <!-- 推荐标签 -->
          <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <div class="bg-primary text-white px-3 py-1 rounded-full text-xs font-medium shadow-md">推荐</div>
          </div>

          <div class="text-center mb-6">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">专业版</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">适合个人和小团队</p>
            <div class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              ¥{{ isYearly ? '79' : '99' }}
              <span class="text-base text-gray-500 dark:text-gray-400 font-normal">/月</span>
            </div>
          </div>

          <ul class="space-y-3 mb-6">
            <li v-for="feature in proFeatures" :key="feature" class="flex items-center gap-3">
              <n-icon size="16" class="text-green-500">
                <CheckmarkCircleOutline />
              </n-icon>
              <span class="text-gray-700 dark:text-gray-300 text-sm">{{ feature }}</span>
            </li>
          </ul>

          <button class="btn btn-primary w-full transition-all shadow-md hover:shadow-lg" @click="selectPlan('pro')">立即升级</button>
        </div>

        <!-- 企业计划 -->
        <div class="feature-card p-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-dark-secondary shadow-sm hover:shadow-md transition-all duration-300">
          <div class="text-center mb-6">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">企业版</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">大型团队和企业</p>
            <div class="text-3xl font-bold text-gray-900 dark:text-white mb-2">自定义</div>
          </div>

          <ul class="space-y-3 mb-6">
            <li v-for="feature in enterpriseFeatures" :key="feature" class="flex items-center gap-3">
              <n-icon size="16" class="text-green-500">
                <CheckmarkCircleOutline />
              </n-icon>
              <span class="text-gray-700 dark:text-gray-300 text-sm">{{ feature }}</span>
            </li>
          </ul>

          <button class="btn btn-outline w-full transition-all" @click="contactSales">联系销售</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import { NSwitch, NIcon } from 'naive-ui';
import { CheckmarkCircleOutline } from '@vicons/ionicons5';

const router = useRouter();
const message = useMessage();

const isYearly = ref(false);

// 功能列表
const freeFeatures = ['每月1,000次对话', '基础AI模型', '标准响应速度', '社区支持'];

const proFeatures = ['每月50,000次对话', '高级AI模型', '优先响应速度', '邮件技术支持', '对话历史保存'];

const enterpriseFeatures = ['无限制对话次数', '全部AI模型', '最快响应速度', '24/7专属支持', '私有部署选项'];

// 切换计费周期
const toggleBillingCycle = (value: boolean) => {
  // 计费周期逻辑
};

// 选择计划
const selectPlan = (plan: string) => {
  if (plan === 'free') {
    router.push('/register');
  } else {
    message.info(`正在跳转到${plan === 'pro' ? '专业版' : '企业版'}订阅页面...`);
  }
};

// 联系销售
const contactSales = () => {
  message.info('正在跳转到企业咨询页面...');
};
</script>

<style scoped>
.animate-fade-in {
  opacity: 0;
  transform: translateY(10px);
  animation: fadeIn 0.6s ease-out forwards;
}

.animation-delay-100 {
  animation-delay: 0.1s;
}

.animation-delay-200 {
  animation-delay: 0.2s;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scale-105 {
  transform: scale(1.02);
}
</style>
