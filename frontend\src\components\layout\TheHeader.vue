<template>
  <header class="sticky top-0 z-50 bg-white dark:bg-dark-secondary text-text-light dark:text-white py-4 shadow-sm transition-colors duration-200">
    <div class="container flex justify-between items-center">
      <!-- Logo -->
      <div class="flex items-center space-x-2">
        <router-link to="/" class="no-underline">
          <h1 class="text-2xl font-bold relative group">
            <span class="text-text-light dark:text-white">YourAGen</span>
            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-200"></span>
          </h1>
        </router-link>
      </div>

      <!-- 导航菜单 -->
      <nav class="hidden md:flex space-x-6 items-center text-base">
        <router-link
          :to="{ name: 'AgentMarketplace' }"
          class="text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors relative group py-1"
        >
          {{ $t('header.navItem1') }}
          <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary dark:bg-light-secondary group-hover:w-full transition-all duration-200"></span>
        </router-link>
        <router-link
          :to="{ name: 'About', hash: '#news' }"
          class="text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors relative group py-1"
        >
          {{ $t('header.navItem2') }}
          <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary dark:bg-light-secondary group-hover:w-full transition-all duration-200"></span>
        </router-link>
        <router-link
          :to="{ name: 'About', hash: '#help' }"
          class="text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors relative group py-1"
        >
          {{ $t('header.navItem3') }}
          <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary dark:bg-light-secondary group-hover:w-full transition-all duration-200"></span>
        </router-link>
        <router-link :to="{ name: 'Pricing' }" class="text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors relative group py-1">
          {{ $t('header.navItem4') }}
          <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary dark:bg-light-secondary group-hover:w-full transition-all duration-200"></span>
        </router-link>

        <!-- 语言选择器 -->
        <LanguageSwitcher class="language-switcher" />

        <!-- 主题切换按钮 -->
        <button
          @click="toggleTheme"
          class="p-2 rounded-full text-text-light dark:text-white hover:bg-gray-100 dark:hover:bg-primary transition-all duration-200"
          :aria-label="$t('header.theme')"
        >
          <svg v-if="isDark" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
            />
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
          </svg>
        </button>

        <!-- 用户头像或登录注册按钮 -->
        <div v-if="userStore.isLoggedIn" class="flex items-center">
          <div class="relative" id="user-menu-container">
            <button class="flex items-center focus:outline-none" @click.stop="isUserMenuOpen = !isUserMenuOpen">
              <template v-if="userStore.avatar_url">
                <img :src="userStore.avatar_url" alt="用户头像" class="w-8 h-8 rounded-full object-cover" />
              </template>
              <span v-else class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center">
                {{ userStore.userInitials }}
              </span>
            </button>

            <!-- 用户下拉菜单 -->
            <div
              v-if="isUserMenuOpen"
              class="absolute right-0 mt-2 w-48 bg-white dark:bg-dark-secondary rounded-md shadow-lg py-1 z-50 border border-gray-200 dark:border-gray-700"
            >
              <router-link
                :to="{ name: 'UserProfile' }"
                class="block px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700"
                @click="isUserMenuOpen = false"
              >
                个人中心
              </router-link>
              <button @click="handleLogout" class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700">
                退出登录
              </button>
            </div>
          </div>
        </div>

        <div v-else class="flex items-center space-x-4">
          <router-link :to="{ name: 'Login' }" class="text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors">
            {{ $t('header.login') }}
          </router-link>
          <router-link :to="{ name: 'Register' }" class="px-4 py-2 rounded-md bg-primary text-white hover:bg-accent dark:bg-primary-light dark:hover:bg-primary transition-colors">
            {{ $t('header.register') }}
          </router-link>
        </div>
      </nav>

      <!-- 移动端菜单按钮 -->
      <button
        @click="toggleMobileMenu"
        class="md:hidden p-2 rounded-md text-text-light dark:text-white hover:bg-gray-100 dark:hover:bg-primary transition-all duration-200"
        aria-label="菜单"
      >
        <svg v-if="!isMobileMenuOpen" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- 移动端菜单遮罩层 -->
    <Transition name="fade">
      <div
        v-if="isMobileMenuOpen"
        :style="`top: ${headerHeight}px`"
        class="fixed inset-x-0 bottom-0 bg-black bg-opacity-50 backdrop-blur-sm z-10 md:hidden"
        @click="closeMobileMenu"
      ></div>
    </Transition>

    <!-- 移动端菜单 -->
    <Transition name="mobile-menu">
      <div
        v-show="isMobileMenuOpen"
        :style="`top: ${headerHeight}px`"
        class="fixed inset-x-0 md:hidden bg-white dark:bg-dark-secondary border-t border-gray-200 dark:border-gray-800 shadow-sm z-20"
      >
        <nav class="container py-4 space-y-3 text-base">
          <router-link
            @click="closeMobileMenu"
            :to="{ name: 'AgentMarketplace' }"
            class="block text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors py-2 pl-3 border-l border-transparent hover:border-primary dark:hover:border-light-secondary"
            >{{ $t('header.navItem1') }}</router-link
          >
          <router-link
            @click="closeMobileMenu"
            :to="{ name: 'About', hash: '#news' }"
            class="block text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors py-2 pl-3 border-l border-transparent hover:border-primary dark:hover:border-light-secondary"
            >{{ $t('header.navItem2') }}</router-link
          >
          <router-link
            @click="closeMobileMenu"
            :to="{ name: 'About', hash: '#help' }"
            class="block text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors py-2 pl-3 border-l border-transparent hover:border-primary dark:hover:border-light-secondary"
            >{{ $t('header.navItem3') }}</router-link
          >
          <router-link
            @click="closeMobileMenu"
            :to="{ name: 'Pricing' }"
            class="block text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors py-2 pl-3 border-l border-transparent hover:border-primary dark:hover:border-light-secondary"
            >{{ $t('header.navItem4') }}</router-link
          >

          <!-- 移动端语言切换 -->
          <div class="flex items-center justify-between py-3 border-t border-gray-200 dark:border-gray-800 mt-2 text-sm">
            <span class="text-text-light dark:text-white">{{ currentLanguageLabel }}</span>
            <div class="flex space-x-3">
              <div
                v-for="(label, locale) in sortedLocales"
                :key="locale"
                @click="changeLanguage(locale as SupportedLocales)"
                class="px-3 py-1 rounded transition-colors flex items-center cursor-pointer"
                :class="{
                  'bg-primary text-white': locale === currentLocale,
                  'hover:bg-gray-100 dark:hover:bg-primary text-text-light dark:text-gray-200': locale !== currentLocale,
                }"
              >
                <span class="mr-1.5 text-sm">{{ localeFlags[locale] }}</span>
                {{ label }}
              </div>
            </div>
          </div>

          <!-- 移动端主题切换 -->
          <div class="flex items-center justify-between py-3 border-t border-gray-200 dark:border-gray-800 mt-2 text-sm">
            <span class="text-text-light dark:text-white">{{ $t('header.theme') }}</span>
            <button @click="toggleTheme" class="p-2 rounded-full text-text-light dark:text-white hover:bg-gray-100 dark:hover:bg-primary transition-all duration-200">
              <svg v-if="isDark" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            </button>
          </div>

          <!-- 移动端用户信息/登录注册按钮 -->
          <div v-if="userStore.isLoggedIn" class="flex flex-col space-y-2 py-3 border-t border-gray-200 dark:border-gray-800 mt-2">
            <div class="flex items-center space-x-3 px-3 py-2">
              <template v-if="userStore.avatar_url">
                <img :src="userStore.avatar_url" alt="用户头像" class="w-8 h-8 rounded-full object-cover" />
              </template>
              <span v-else class="w-8 h-8 rounded-full bg-primary text-white flex items-center justify-center">
                {{ userStore.userInitials }}
              </span>
              <span class="text-text-light dark:text-white">{{ userStore.fullName }}</span>
            </div>
            <router-link
              :to="{ name: 'UserProfile' }"
              @click="closeMobileMenu"
              class="block text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors py-2 pl-3 border-l border-transparent hover:border-primary dark:hover:border-light-secondary"
            >
              个人中心
            </router-link>
            <button
              @click="handleLogout"
              class="block text-left w-full text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors py-2 pl-3 border-l border-transparent hover:border-primary dark:hover:border-light-secondary"
            >
              退出登录
            </button>
          </div>
          <div v-else class="flex flex-col space-y-2 py-3 border-t border-gray-200 dark:border-gray-800 mt-2">
            <router-link
              to="/login"
              @click="closeMobileMenu"
              class="block text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors py-2 pl-3 border-l border-transparent hover:border-primary dark:hover:border-light-secondary"
            >
              {{ $t('header.login') }}
            </router-link>
            <router-link
              to="/register"
              @click="closeMobileMenu"
              class="block text-text-light dark:text-white hover:text-primary dark:hover:text-light-secondary transition-colors py-2 pl-3 border-l border-transparent hover:border-primary dark:hover:border-light-secondary"
            >
              {{ $t('header.register') }}
            </router-link>
          </div>
        </nav>
      </div>
    </Transition>
  </header>
</template>

<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useThemeStore } from '@/stores/theme';
import { useUserStore } from '@/stores/user';
import { useRouter } from 'vue-router';
import LanguageSwitcher from '@/components/LanguageSwitcher.vue';
import type { SupportedLocales } from '@/i18n';
import { setLanguage } from '@/i18n';

const themeStore = useThemeStore();
const isDark = computed(() => themeStore.isDarkMode);
const toggleTheme = () => {
  themeStore.setMode(isDark.value ? 'light' : 'dark');
};

const userStore = useUserStore();
const router = useRouter();

// 用户菜单状态
const isUserMenuOpen = ref(false);

// 移动菜单状态
const isMobileMenuOpen = ref(false);
const headerHeight = ref(0);

// 处理退出登录
const handleLogout = () => {
  userStore.$reset();
  isUserMenuOpen.value = false;
  isMobileMenuOpen.value = false;
  router.push('/login');
};

// 移动菜单处理
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
  // 如果打开移动菜单，则关闭用户菜单
  if (isMobileMenuOpen.value) {
    isUserMenuOpen.value = false;
  }
};

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false;
};

// 点击外部关闭用户菜单
const closeUserMenuOnOutsideClick = (event: MouseEvent) => {
  const container = document.getElementById('user-menu-container');
  if (isUserMenuOpen.value && container && !container.contains(event.target as Node)) {
    isUserMenuOpen.value = false;
  }
};

// i18n 相关
const { locale, t, tm } = useI18n();
const currentLocale = computed<SupportedLocales>(() => locale.value as SupportedLocales);

const availableLocales = computed(() => ({
  en: t('language.en'),
  zh: t('language.zh'),
}));

// 定义语言排序顺序和标签
const localesOrder: SupportedLocales[] = ['en', 'zh'];
const localeFlags: Record<string, string> = {
  en: '🇺🇸',
  zh: '🇨🇳',
};

// 当前语言标签
const currentLanguageLabel = computed(() => availableLocales.value[currentLocale.value]);

// 排序后的语言列表
const sortedLocales = computed(() => {
  const sortedObj: Record<string, string> = {};
  localesOrder.forEach((locale: SupportedLocales) => {
    if (availableLocales.value[locale]) {
      sortedObj[locale] = availableLocales.value[locale];
    }
  });
  return sortedObj;
});

// 切换语言
const changeLanguage = (lang: SupportedLocales): void => {
  setLanguage(lang);
  closeMobileMenu();
};

// 生命周期钩子
onMounted(() => {
  document.addEventListener('click', closeUserMenuOnOutsideClick);

  // 获取header高度以正确定位移动菜单
  const header = document.querySelector('header');
  if (header) {
    headerHeight.value = header.getBoundingClientRect().height;
  }
});

onUnmounted(() => {
  document.removeEventListener('click', closeUserMenuOnOutsideClick);
});
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
