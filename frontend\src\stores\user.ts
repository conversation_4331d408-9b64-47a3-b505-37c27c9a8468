import { defineStore } from 'pinia';
import type { LoginParams } from '@/apis/user';
import { getUserInfo, login as apiLogin, type NullableUserInfo } from '@/apis/user';

// 扩展NullableUserInfo接口，添加store需要的额外字段
interface UserState extends NullableUserInfo {
  token: string | null;
  refresh_token: string | null;
  isLoggedIn: boolean;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    id: null,
    username: null,
    email: null,
    avatar_url: null,
    token: null,
    refresh_token: null,
    roles: [],
    isLoggedIn: false,
    is_active: false,
    is_locked: false,
    account_status: null,
    status_reason: null,
    create_time: null,
    bio: null,
    timezone: null,
    locale: null,
    phone_number: null,
    display_name: null,
    full_name: null,
  }),

  getters: {
    fullName(): string {
      return this.full_name || this.display_name || this.username || '访客用户';
    },

    userInitials(): string {
      if (this.display_name) return this.display_name.charAt(0).toUpperCase();
      if (this.username) return this.username.charAt(0).toUpperCase();
      return '访';
    },

    hasRole(): (role: string) => boolean {
      return (role: string) => this.roles.includes(role);
    },
  },

  actions: {
    setUser(userData: Partial<UserState>) {
      this.$patch(userData);
      if (userData.token) {
        this.isLoggedIn = true;
      }
    },

    async login(params: LoginParams) {
      try {
        // 1. 登录获取token
        const tokenRes = await apiLogin(params);

        // 更新token信息
        this.$patch({
          token: tokenRes.access_token,
          refresh_token: tokenRes.refresh_token,
          isLoggedIn: true,
        });

        // 2. 获取用户信息
        try {
          const userInfo = await getUserInfo();

          // 更新用户信息
          this.$patch({
            id: userInfo.id,
            username: userInfo.username,
            email: userInfo.email,
            avatar_url: userInfo.avatar_url || null,
            roles: userInfo.roles,
            is_active: userInfo.is_active,
            is_locked: userInfo.is_locked,
            account_status: userInfo.account_status,
            status_reason: userInfo.status_reason || null,
            create_time: userInfo.create_time,
            bio: userInfo.bio || null,
            timezone: userInfo.timezone,
            locale: userInfo.locale,
            phone_number: userInfo.phone_number || null,
            display_name: userInfo.display_name || null,
            full_name: userInfo.full_name || null,
          });

          return true;
        } catch (userError) {
          console.error('获取用户信息失败:', userError);
          // 即使获取用户信息失败，也认为登录成功了（因为已经有token）
          return true;
        }
      } catch (error) {
        console.error('登录失败:', error);
        return false;
      }
    },

    async checkAuth() {
      // 由于状态已经自动持久化，这里只需要检查token是否存在
      if (this.token) {
        try {
          // 从服务器获取最新的用户信息
          const userInfo = await getUserInfo();
          this.$patch({
            id: userInfo.id,
            username: userInfo.username,
            email: userInfo.email,
            avatar_url: userInfo.avatar_url || null,
            roles: userInfo.roles,
            is_active: userInfo.is_active,
            is_locked: userInfo.is_locked,
            account_status: userInfo.account_status,
            status_reason: userInfo.status_reason || null,
            create_time: userInfo.create_time,
            bio: userInfo.bio || null,
            timezone: userInfo.timezone,
            locale: userInfo.locale,
            phone_number: userInfo.phone_number || null,
            display_name: userInfo.display_name || null,
            full_name: userInfo.full_name || null,
          });
          return true;
        } catch (error) {
          console.error('用户信息获取失败:', error);
          this.$reset(); // Token可能已失效，清除用户信息
          return false;
        }
      }

      return false;
    },
  },

  persist: {
    key: 'user-store',
  },
});
