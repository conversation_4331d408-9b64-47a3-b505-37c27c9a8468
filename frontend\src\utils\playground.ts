import { CloudOutline, FlashOutline, RocketOutline } from '@vicons/ionicons5';

/**
 * 获取提供商图标组件
 */
export const getProviderIconComponent = (provider?: string) => {
  const icons: Record<string, any> = {
    openai: FlashOutline,
    anthropic: RocketOutline,
    google: CloudOutline,
    default: CloudOutline,
  };
  return icons[provider?.toLowerCase() || 'default'] || icons.default;
};

/**
 * 获取提供商颜色
 */
export const getProviderColor = (provider?: string) => {
  const colors: Record<string, string> = {
    openai: '#10B981',
    anthropic: '#8B5CF6',
    google: '#3B82F6',
    default: '#6B7280',
  };
  return colors[provider?.toLowerCase() || 'default'] || colors.default;
};

/**
 * 复制文本到剪贴板的通用函数
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('复制失败:', error);
    return false;
  }
};
