<template>
  <div class="markdown-content" v-html="renderedContent"></div>
</template>

<script lang="ts" setup>
import { computed, nextTick, onMounted } from 'vue';
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import DOMPurify from 'dompurify';
import highlightPlugin from 'markdown-it-highlightjs';
import { katex } from '@mdit/plugin-katex';
import { container } from '@mdit/plugin-container';
import { alert } from '@mdit/plugin-alert';
import { footnote } from '@mdit/plugin-footnote';
import 'highlight.js/styles/github.css';
import 'katex/dist/katex.min.css';
import { copyToClipboard } from '@/utils/playground';
import { useMessage } from 'naive-ui';

interface Props {
  content: string;
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
});

const message = useMessage();

// 配置 markdown-it
const md = new MarkdownIt({
  html: false,
  xhtmlOut: false,
  breaks: true,
  langPrefix: 'language-',
  linkify: true,
  typographer: true,
  quotes: '""\'\'',
})
  .use(highlightPlugin, { hljs })
  .use(katex, {
    delimiters: 'all',
    mathFence: true,
    throwOnError: false,
    errorColor: '#cc0000',
    strict: false,
    trust: true,
    macros: {
      '\\RR': '\\mathbb{R}',
      '\\CC': '\\mathbb{C}',
      '\\NN': '\\mathbb{N}',
      '\\ZZ': '\\mathbb{Z}',
      '\\QQ': '\\mathbb{Q}',
      '\\FF': '\\mathbb{F}',
      '\\PP': '\\mathbb{P}',
      '\\EE': '\\mathbb{E}',
      '\\bra': '\\langle',
      '\\ket': '\\rangle',
      '\\braket': '\\langle #1 \\rangle',
      '\\abs': '\\left| #1 \\right|',
      '\\norm': '\\left\\| #1 \\right\\|',
      '\\inner': '\\langle #1, #2 \\rangle',
      '\\tr': '\\operatorname{tr}',
      '\\rank': '\\operatorname{rank}',
      '\\diag': '\\operatorname{diag}',
      '\\span': '\\operatorname{span}',
      '\\dim': '\\operatorname{dim}',
      '\\ker': '\\operatorname{ker}',
      '\\im': '\\operatorname{im}',
      '\\deg': '\\operatorname{deg}',
      '\\gcd': '\\operatorname{gcd}',
      '\\lcm': '\\operatorname{lcm}',
      '\\max': '\\operatorname{max}',
      '\\min': '\\operatorname{min}',
      '\\sup': '\\operatorname{sup}',
      '\\inf': '\\operatorname{inf}',
      '\\limsup': '\\operatorname{lim sup}',
      '\\liminf': '\\operatorname{lim inf}',
      '\\argmax': '\\operatorname{arg max}',
      '\\argmin': '\\operatorname{arg min}',
      '\\det': '\\operatorname{det}',
      '\\Pr': '\\operatorname{Pr}',
      '\\Var': '\\operatorname{Var}',
      '\\Cov': '\\operatorname{Cov}',
      '\\Corr': '\\operatorname{Corr}',
      '\\E': '\\operatorname{E}',
      '\\Real': '\\operatorname{Re}',
      '\\Imag': '\\operatorname{Im}',
      '\\supp': '\\operatorname{supp}',
      '\\dom': '\\operatorname{dom}',
      '\\ran': '\\operatorname{ran}',
      '\\sgn': '\\operatorname{sgn}',
      '\\st': '\\text{ s.t. }',
      '\\given': '\\,|\\,',
      '\\half': '\\frac{1}{2}',
    },
  })
  // GitHub风格警示框
  .use(alert)
  // 脚注支持
  .use(footnote);

// 添加自定义容器（@mdit/plugin-container正确配置）
md.use(container, {
  name: 'info',
  openRender: (tokens: any, index: number) => {
    const info = tokens[index].info.trim().slice(4).trim();
    return `<div class="custom-container info">\n<p class="custom-container-title">${info || 'Info'}</p>\n`;
  },
})
  .use(container, {
    name: 'tip',
    openRender: (tokens: any, index: number) => {
      const info = tokens[index].info.trim().slice(3).trim();
      return `<div class="custom-container tip">\n<p class="custom-container-title">${info || 'Tip'}</p>\n`;
    },
  })
  .use(container, {
    name: 'warning',
    openRender: (tokens: any, index: number) => {
      const info = tokens[index].info.trim().slice(7).trim();
      return `<div class="custom-container warning">\n<p class="custom-container-title">${info || 'Warning'}</p>\n`;
    },
  })
  .use(container, {
    name: 'danger',
    openRender: (tokens: any, index: number) => {
      const info = tokens[index].info.trim().slice(6).trim();
      return `<div class="custom-container danger">\n<p class="custom-container-title">${info || 'Danger'}</p>\n`;
    },
  });

// 自定义链接渲染器
const defaultLinkRenderer =
  md.renderer.rules.link_open ||
  function (tokens: any, idx: number, options: any, env: any, renderer: any) {
    return renderer.renderToken(tokens, idx, options);
  };

md.renderer.rules.link_open = function (tokens: any, idx: number, options: any, env: any, renderer: any) {
  try {
    const token = tokens[idx];
    if (!token) return defaultLinkRenderer(tokens, idx, options, env, renderer);

    const href = token.attrGet('href');

    if (href && typeof href === 'string' && (href.startsWith('http://') || href.startsWith('https://') || href.startsWith('//'))) {
      token.attrSet('target', '_blank');
      token.attrSet('rel', 'noopener noreferrer');
    }

    return defaultLinkRenderer(tokens, idx, options, env, renderer);
  } catch (error) {
    console.error('链接渲染错误:', error);
    return defaultLinkRenderer(tokens, idx, options, env, renderer);
  }
};

// 检查是否为URL
const isURL = (text: string): boolean => {
  if (!text || typeof text !== 'string' || text.length > 2048) {
    return false;
  }

  try {
    const url = new URL(text.trim());
    // 只允许http和https协议
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch {
    return false;
  }
};

// SVG图标定义
const COPY_ICON = `<svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
  <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
  <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
</svg>`;

const SUCCESS_ICON = `<svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
  <polyline points="20,6 9,17 4,12"></polyline>
</svg>`;

// 添加复制按钮到代码块
const addCopyButtonToCodeBlocks = () => {
  nextTick(() => {
    const preElements = document.querySelectorAll('.markdown-content pre');
    preElements.forEach(pre => {
      // 避免重复添加按钮
      if (pre.querySelector('.copy-button')) return;

      const codeElement = pre.querySelector('code');
      if (!codeElement) return;

      // 创建复制按钮
      const copyButton = document.createElement('button');
      copyButton.className = 'copy-button';
      copyButton.innerHTML = COPY_ICON;
      copyButton.title = '复制代码';
      copyButton.setAttribute('aria-label', '复制代码');

      // 添加点击事件
      copyButton.addEventListener('click', async event => {
        event.preventDefault();
        event.stopPropagation();

        try {
          const text = codeElement.textContent || '';
          const success = await copyToClipboard(text);

          if (success) {
            message.success('代码已复制到剪贴板');
            // 短暂显示复制成功图标
            copyButton.innerHTML = SUCCESS_ICON;
            copyButton.disabled = true;

            setTimeout(() => {
              copyButton.innerHTML = COPY_ICON;
              copyButton.disabled = false;
            }, 2000);
          } else {
            message.error('复制失败，请手动复制');
          }
        } catch (error) {
          console.error('复制失败:', error);
          message.error('复制失败，请手动复制');
        }
      });

      pre.appendChild(copyButton);
    });
  });
};

// 处理渲染后的内容，添加复制按钮
const renderedContent = computed(() => {
  try {
    const text = props.content?.trim() || '';
    if (!text) return '';

    // 处理纯URL（简单URL不需要Markdown处理）
    if (isURL(text) && !text.includes(' ') && !text.includes('\n')) {
      return DOMPurify.sanitize(`<p><a href="${text}" target="_blank" rel="noopener noreferrer">${text}</a></p>`);
    }

    // 渲染Markdown
    const rendered = md.render(text);
    if (!rendered) return '';

    // 修复中文括号加粗问题
    const fixedRendered = rendered.replace(/(\*\*)([^*<>]*?)（([^）]*?)）([^*<>]*?)(\*\*)/g, '<strong>$2（$3）$4</strong>');

    // 延迟添加复制按钮，避免性能问题
    nextTick(() => {
      if (fixedRendered.includes('<pre>')) {
        addCopyButtonToCodeBlocks();
      }
    });

    return DOMPurify.sanitize(fixedRendered, {
      ADD_ATTR: ['target', 'rel', 'class', 'style'],
      ALLOWED_ATTR: [
        'href',
        'title',
        'target',
        'rel',
        'class',
        'id',
        'style',
        'alt',
        'src',
        'data-footnote-id',
        'data-footnote-backref',
        // SVG相关属性
        'viewBox',
        'xmlns',
        'width',
        'height',
        'd',
        'fill',
        'stroke',
        'stroke-width',
        'x',
        'y',
        'x1',
        'y1',
        'x2',
        'y2',
        'cx',
        'cy',
        'r',
        'rx',
        'ry',
        'points',
        'transform',
        'opacity',
        'clip-path',
        'preserveAspectRatio',
        // MathML相关属性
        'mathvariant',
        'mathsize',
        'mathcolor',
        'mathbackground',
        'displaystyle',
        'scriptlevel',
        'stretchy',
        'symmetric',
        'maxsize',
        'minsize',
        'form',
        'fence',
        'separator',
        'lspace',
        'rspace',
        'accent',
        'accentunder',
      ],
      ALLOWED_TAGS: [
        'p',
        'br',
        'strong',
        'em',
        'b',
        'i',
        'u',
        'del',
        's',
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'ul',
        'ol',
        'li',
        'blockquote',
        'pre',
        'code',
        'a',
        'img',
        'table',
        'thead',
        'tbody',
        'tr',
        'th',
        'td',
        'hr',
        'div',
        'span',
        'section',
        'details',
        'summary',
        // 新插件相关标签
        'sup',
        'sub',
        'ins',
        // KaTeX相关标签
        'math',
        'semantics',
        'mrow',
        'mi',
        'mo',
        'mn',
        'msup',
        'msub',
        'mfrac',
        'munder',
        'mover',
        'munderover',
        'mtext',
        'mspace',
        'annotation',
        'annotation-xml',
        'mathml',
        'mtable',
        'mtr',
        'mtd',
        'mpadded',
        'mphantom',
        'mfenced',
        'menclose',
        'mstyle',
        'mroot',
        'msqrt',
        'maction',
        // SVG相关标签（KaTeX根号等符号需要）
        'svg',
        'g',
        'path',
        'defs',
        'use',
        'clipPath',
        'rect',
        'line',
        'polygon',
        'polyline',
        'circle',
        'ellipse',
      ],
    });
  } catch (error) {
    console.error('Markdown渲染错误:', error);
    return '<p>内容渲染失败，请检查格式</p>';
  }
});

onMounted(addCopyButtonToCodeBlocks);
</script>

<style scoped>
/* === 基础样式 === */
.markdown-content {
  line-height: 1.5;
  color: #1f2937;
  font-size: 12px;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Inter', 'PingFang SC', 'Hiragino Sans GB', 'Noto Sans CJK SC', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  word-wrap: break-word;
  overflow-wrap: break-word;
  width: 100%;
  box-sizing: border-box;
  letter-spacing: 0.005em;
  font-feature-settings:
    'liga' 1,
    'kern' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dark .markdown-content {
  color: #f1f5f9;
}

/* === KaTeX数学公式样式 === */
/* 确保KaTeX样式不影响其他内容 */
.markdown-content :deep(.katex) {
  color: inherit;
  font-family: 'KaTeX_Main', 'Times New Roman', serif !important;
  line-height: 1.2;
}

/* 块级数学公式 */
.markdown-content :deep(.katex-display) {
  margin: 1.5rem 0;
  text-align: center;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 1rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(139, 92, 246, 0.02) 100%);
  border-radius: 0.75rem;
  border: 1px solid rgba(99, 102, 241, 0.1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.dark .markdown-content :deep(.katex-display) {
  background: linear-gradient(135deg, rgba(129, 140, 248, 0.05) 0%, rgba(167, 139, 250, 0.05) 100%);
  border-color: rgba(129, 140, 248, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.markdown-content :deep(.katex-display > .katex) {
  display: inline-block;
  white-space: nowrap;
  text-align: center;
  font-size: 1.1em;
}

/* 行内数学公式 */
.markdown-content :deep(.katex:not(.katex-display .katex)) {
  margin: 0 0.1em;
  vertical-align: baseline;
  display: inline-block;
}

/* 数学符号精确定位 */
.markdown-content :deep(.katex .mord),
.markdown-content :deep(.katex .mbin),
.markdown-content :deep(.katex .mrel),
.markdown-content :deep(.katex .mopen),
.markdown-content :deep(.katex .mclose),
.markdown-content :deep(.katex .mop),
.markdown-content :deep(.katex .mpunct) {
  color: inherit;
}

/* 上标下标位置修正 */
.markdown-content :deep(.katex .vlist-t) {
  display: inline-table;
  table-layout: fixed;
  border-collapse: collapse;
}

.markdown-content :deep(.katex .vlist-r) {
  display: table-row;
}

.markdown-content :deep(.katex .vlist) {
  display: table-cell;
  vertical-align: bottom;
  position: relative;
}

.markdown-content :deep(.katex .vlist > span) {
  display: block;
  position: relative;
}

/* 分数样式 */
.markdown-content :deep(.katex .mfrac) {
  text-align: center;
  display: inline-block;
  vertical-align: -0.25em;
}

.markdown-content :deep(.katex .mfrac .frac-line) {
  border-bottom-width: 0.04em;
  border-bottom-style: solid;
  border-bottom-color: currentColor;
  margin: 0.04em 0;
}

.markdown-content :deep(.katex .mfrac .frac-line:before) {
  content: '';
  display: block;
  width: 100%;
  border-top: 0.04em solid currentColor;
}

/* 根号样式 - 让KaTeX自己处理，不需要额外样式 */

/* 大型运算符 */
.markdown-content :deep(.katex .mop) {
  text-align: center;
  position: relative;
}

.markdown-content :deep(.katex .mop.op-symbol) {
  vertical-align: -0.25em;
}

.markdown-content :deep(.katex .mop.op-limits > .vlist-t) {
  text-align: center;
}

/* 括号和分隔符 */
.markdown-content :deep(.katex .mopen),
.markdown-content :deep(.katex .mclose) {
  margin: 0 0.05em;
}

.markdown-content :deep(.katex .mopen.delimcenter),
.markdown-content :deep(.katex .mclose.delimcenter) {
  vertical-align: -0.25em;
}

/* 量子力学符号特殊处理 */
.markdown-content :deep(.katex .mopen.delimcenter[style*='height']),
.markdown-content :deep(.katex .mclose.delimcenter[style*='height']) {
  vertical-align: middle;
}

/* 矩阵和数组 */
.markdown-content :deep(.katex .mord.mtable) {
  margin: 0.5em 0;
  text-align: center;
}

.markdown-content :deep(.katex .mtable .col-align-c > .vlist-t) {
  text-align: center;
}

.markdown-content :deep(.katex .mtable .col-align-l > .vlist-t) {
  text-align: left;
}

.markdown-content :deep(.katex .mtable .col-align-r > .vlist-t) {
  text-align: right;
}

/* 数学间距优化 */
.markdown-content :deep(.katex .mbin) {
  margin: 0 0.22222em;
}

.markdown-content :deep(.katex .mrel) {
  margin: 0 0.27778em;
}

.markdown-content :deep(.katex .mpunct) {
  margin: 0 0.16667em 0 0.16667em;
}

.markdown-content :deep(.katex .minner) {
  margin: 0 0.16667em;
}

/* 暗色主题适配 */
.dark .markdown-content :deep(.katex),
.dark .markdown-content :deep(.katex .mord),
.dark .markdown-content :deep(.katex .mbin),
.dark .markdown-content :deep(.katex .mrel),
.dark .markdown-content :deep(.katex .mopen),
.dark .markdown-content :deep(.katex .mclose),
.dark .markdown-content :deep(.katex .mop),
.dark .markdown-content :deep(.katex .mpunct),
.dark .markdown-content :deep(.katex .minner) {
  color: #f1f5f9;
}

/* 数学公式错误处理 */
.markdown-content :deep(.katex-error) {
  color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
  padding: 0.2em 0.4em;
  border-radius: 0.375rem;
  font-family: 'JetBrains Mono', 'Fira Code', monospace;
  font-size: 0.9em;
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.dark .markdown-content :deep(.katex-error) {
  color: #fca5a5;
  background: rgba(220, 38, 38, 0.15);
  border-color: rgba(220, 38, 38, 0.3);
}

/* === 标题样式 === */
.markdown-content :deep(h1, h2, h3, h4, h5, h6) {
  font-weight: 600;
  line-height: 1.3;
  margin: 0.5rem 0 0.25rem 0;
  color: #111827;
  letter-spacing: -0.025em;
  font-feature-settings:
    'liga' 1,
    'kern' 1;
  scroll-margin-top: 1rem;
}

.markdown-content :deep(h1) {
  font-size: 1.125rem;
  font-weight: 700;
  margin: 0.75rem 0 0.5rem 0;
  padding-bottom: 0.375rem;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.markdown-content :deep(h1::after) {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 1px;
  opacity: 0.3;
}

.markdown-content :deep(h2) {
  font-size: 1.0625rem;
  color: #374151;
  font-weight: 650;
}

.markdown-content :deep(h3) {
  font-size: 1rem;
  color: #4b5563;
}

.markdown-content :deep(h4, h5, h6) {
  font-size: 0.9rem;
  color: #6b7280;
}

/* 暗色主题标题 */
.dark .markdown-content :deep(h1) {
  background: linear-gradient(90deg, #818cf8 0%, #a78bfa 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark .markdown-content :deep(h1::after) {
  background: linear-gradient(90deg, #818cf8 0%, #a78bfa 100%);
}

.dark .markdown-content :deep(h2) {
  color: #e5e7eb;
}

.dark .markdown-content :deep(h3) {
  color: #d1d5db;
}

.dark .markdown-content :deep(h4, h5, h6) {
  color: #9ca3af;
}

/* === 段落和列表样式 === */
.markdown-content :deep(p) {
  margin: 0.375rem 0;
  word-break: break-word;
  line-height: 1.6;
  color: #374151;
}

.markdown-content :deep(p:first-child) {
  margin-top: 0;
}

.markdown-content :deep(p:last-child) {
  margin-bottom: 0;
}

.dark .markdown-content :deep(p) {
  color: #e5e7eb;
}

.markdown-content :deep(ul, ol) {
  margin: 0.375rem 0;
  padding-left: 1.125rem;
  word-break: break-word;
}

.markdown-content :deep(li) {
  margin: 0.1875rem 0;
  line-height: 1.5;
  word-break: break-word;
  color: #374151;
}

.dark .markdown-content :deep(li) {
  color: #e5e7eb;
}

.markdown-content :deep(ul li) {
  list-style-type: none;
  position: relative;
}

.markdown-content :deep(ul li::before) {
  content: '●';
  color: #6366f1;
  position: absolute;
  left: -0.875rem;
  font-weight: 600;
  font-size: 0.75em;
  top: 0.125em;
}

.dark .markdown-content :deep(ul li::before) {
  color: #818cf8;
}

.markdown-content :deep(ol li) {
  color: #374151;
}

.dark .markdown-content :deep(ol li) {
  color: #e5e7eb;
}

/* === 代码样式 === */
.markdown-content :deep(code:not(pre code)) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #dc2626;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 0.8em;
  font-weight: 500;
  word-break: break-all;
  font-feature-settings: 'liga' 0;
}

.dark .markdown-content :deep(code:not(pre code)) {
  background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  color: #fbbf24;
}

.markdown-content :deep(pre) {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-radius: 0.5rem;
  padding: 0.75rem;
  margin: 0.5rem 0;
  overflow-x: auto;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
}

.markdown-content :deep(pre::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 0.5rem 0.5rem 0 0;
}

/* 复制按钮样式 */
.markdown-content :deep(.copy-button) {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  color: #6b7280;
  z-index: 10;
}

.markdown-content :deep(pre:hover .copy-button) {
  opacity: 1;
}

.markdown-content :deep(.copy-button:hover) {
  background: rgba(255, 255, 255, 1);
  color: #374151;
  transform: scale(1.05);
}

.dark .markdown-content :deep(pre) {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

.dark .markdown-content :deep(pre::before) {
  background: linear-gradient(90deg, #818cf8 0%, #a78bfa 100%);
}

/* 暗色模式复制按钮样式 */
.dark .markdown-content :deep(.copy-button) {
  background: rgba(31, 41, 55, 0.9);
  color: #9ca3af;
}

.dark .markdown-content :deep(.copy-button:hover) {
  background: rgba(31, 41, 55, 1);
  color: #e5e7eb;
}

.markdown-content :deep(pre code) {
  background: none !important;
  color: inherit;
  padding: 0;
  border: none;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 10.5px;
  line-height: 1.5;
  white-space: pre;
  word-break: normal;
  font-feature-settings:
    'liga' 1,
    'calt' 1;
}

/* === 深色模式代码高亮样式 === */
/* 覆盖 highlight.js 的默认样式，为深色模式提供更好的对比度 */
.dark .markdown-content :deep(.hljs) {
  background: transparent !important;
  color: #e5e7eb !important;
}

/* 关键字 */
.dark .markdown-content :deep(.hljs-keyword),
.dark .markdown-content :deep(.hljs-selector-tag),
.dark .markdown-content :deep(.hljs-literal),
.dark .markdown-content :deep(.hljs-section),
.dark .markdown-content :deep(.hljs-link) {
  color: #c084fc !important; /* 紫色 */
}

/* 字符串 */
.dark .markdown-content :deep(.hljs-string),
.dark .markdown-content :deep(.hljs-title),
.dark .markdown-content :deep(.hljs-name),
.dark .markdown-content :deep(.hljs-type) {
  color: #34d399 !important; /* 绿色 */
}

/* 数字 */
.dark .markdown-content :deep(.hljs-number),
.dark .markdown-content :deep(.hljs-symbol),
.dark .markdown-content :deep(.hljs-bullet),
.dark .markdown-content :deep(.hljs-addition) {
  color: #fbbf24 !important; /* 黄色 */
}

/* 注释 */
.dark .markdown-content :deep(.hljs-comment),
.dark .markdown-content :deep(.hljs-quote),
.dark .markdown-content :deep(.hljs-deletion),
.dark .markdown-content :deep(.hljs-meta) {
  color: #9ca3af !important; /* 灰色 */
  font-style: italic;
}

/* 函数名 */
.dark .markdown-content :deep(.hljs-function),
.dark .markdown-content :deep(.hljs-title.function_),
.dark .markdown-content :deep(.hljs-title.class_),
.dark .markdown-content :deep(.hljs-title.class_.inherited__) {
  color: #60a5fa !important; /* 蓝色 */
}

/* 变量 */
.dark .markdown-content :deep(.hljs-variable),
.dark .markdown-content :deep(.hljs-template-variable),
.dark .markdown-content :deep(.hljs-attribute) {
  color: #f87171 !important; /* 红色 */
}

/* 操作符 */
.dark .markdown-content :deep(.hljs-operator),
.dark .markdown-content :deep(.hljs-punctuation) {
  color: #e5e7eb !important; /* 浅灰色 */
}

/* 标签属性 */
.dark .markdown-content :deep(.hljs-attr) {
  color: #fbbf24 !important; /* 黄色 */
}

/* 正则表达式 */
.dark .markdown-content :deep(.hljs-regexp) {
  color: #fb7185 !important; /* 粉色 */
}

/* 内置函数 */
.dark .markdown-content :deep(.hljs-built_in),
.dark .markdown-content :deep(.hljs-builtin-name) {
  color: #a78bfa !important; /* 浅紫色 */
}

/* 强调 */
.dark .markdown-content :deep(.hljs-emphasis) {
  font-style: italic;
}

.dark .markdown-content :deep(.hljs-strong) {
  font-weight: bold;
}

/* 特定语言的样式优化 */
/* JavaScript/TypeScript */
.dark .markdown-content :deep(.hljs-subst) {
  color: #e5e7eb !important;
}

/* Python */
.dark .markdown-content :deep(.hljs-decorator) {
  color: #fbbf24 !important;
}

/* CSS */
.dark .markdown-content :deep(.hljs-selector-id),
.dark .markdown-content :deep(.hljs-selector-class) {
  color: #34d399 !important;
}

.dark .markdown-content :deep(.hljs-selector-pseudo) {
  color: #c084fc !important;
}

/* HTML */
.dark .markdown-content :deep(.hljs-tag) {
  color: #60a5fa !important;
}

/* JSON */
.dark .markdown-content :deep(.hljs-property) {
  color: #60a5fa !important;
}

/* Shell/Bash */
.dark .markdown-content :deep(.hljs-params) {
  color: #e5e7eb !important;
}

/* 确保代码块内的文本在深色模式下可见 */
.dark .markdown-content :deep(pre code) {
  color: #e5e7eb !important;
}

/* 浅色模式下的代码高亮微调 */
.markdown-content :deep(.hljs) {
  background: transparent !important;
}

/* 确保浅色模式下的代码也有足够对比度 */
.markdown-content :deep(pre code) {
  color: #374151 !important;
}

/* 浅色模式下的注释颜色调整 */
.markdown-content :deep(.hljs-comment),
.markdown-content :deep(.hljs-quote) {
  color: #6b7280 !important;
}

/* 浅色模式下的字符串颜色调整 */
.markdown-content :deep(.hljs-string) {
  color: #059669 !important;
}

/* 浅色模式下的关键字颜色调整 */
.markdown-content :deep(.hljs-keyword) {
  color: #7c3aed !important;
}

/* === 链接样式 === */
.markdown-content :deep(a) {
  color: #6366f1;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  word-break: break-all;
  position: relative;
  border-bottom: 1px solid transparent;
}

.markdown-content :deep(a:hover) {
  color: #4f46e5;
  border-bottom-color: currentColor;
  transform: translateY(-0.5px);
}

.dark .markdown-content :deep(a) {
  color: #818cf8;
}

.dark .markdown-content :deep(a:hover) {
  color: #a78bfa;
}

/* === 引用样式 === */
.markdown-content :deep(blockquote) {
  margin: 0.5rem 0;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  border-left: 4px solid #6366f1;
  border-radius: 0 0.5rem 0.5rem 0;
  font-style: italic;
  color: #4b5563;
  font-size: 0.95em;
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.markdown-content :deep(blockquote::before) {
  content: '"';
  position: absolute;
  top: -0.25rem;
  left: 0.5rem;
  font-size: 2rem;
  color: #6366f1;
  opacity: 0.3;
  font-family: Georgia, serif;
}

.dark .markdown-content :deep(blockquote) {
  background: linear-gradient(135deg, rgba(129, 140, 248, 0.1) 0%, rgba(167, 139, 250, 0.1) 100%);
  color: #d1d5db;
  border-left-color: #818cf8;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark .markdown-content :deep(blockquote::before) {
  color: #818cf8;
}

/* === 表格样式 === */
.markdown-content :deep(table) {
  border-collapse: separate;
  border-spacing: 2px;
  margin: 0.5rem 0;
  width: 100%;
  border: 1px solid rgba(209, 213, 219, 0.5);
  font-size: 11px;
  display: table;
  overflow-x: auto;
  background-color: #d1d5db;
  border-radius: 0.25rem;
}

.dark .markdown-content :deep(table) {
  border: none;
  background-color: #6b7280;
}

.markdown-content :deep(th, td) {
  text-align: left;
  border: none;
  word-break: break-word;
}

.markdown-content :deep(th) {
  background-color: #f9fafb;
  color: #374151;
  font-weight: 600;
  font-size: 10px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.5rem 0.75rem;
  line-height: 1.4;
}

.dark .markdown-content :deep(th) {
  background-color: #374151;
  color: #e5e7eb;
}

.markdown-content :deep(td) {
  background-color: #ffffff;
  color: #374151;
  padding: 0.5rem 0.75rem;
  line-height: 1.4;
}

.dark .markdown-content :deep(td) {
  background-color: #1f2937;
  color: #e5e7eb;
}

/* === 其他元素样式 === */
.markdown-content :deep(hr) {
  margin: 1.25rem 0;
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
  border-radius: 1px;
}

.dark .markdown-content :deep(hr) {
  background: linear-gradient(90deg, transparent, rgba(129, 140, 248, 0.3), transparent);
}

.markdown-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 0.5rem 0;
  display: block;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.markdown-content :deep(img:hover) {
  transform: scale(1.02);
}

.markdown-content :deep(strong) {
  font-weight: 700;
  color: #111827;
  font-feature-settings: 'liga' 1;
}

.dark .markdown-content :deep(strong) {
  color: #f9fafb;
}

.markdown-content :deep(em) {
  font-style: italic;
  color: #4b5563;
  font-feature-settings: 'liga' 1;
}

.dark .markdown-content :deep(em) {
  color: #d1d5db;
}

.markdown-content :deep(del) {
  text-decoration: line-through;
  color: #9ca3af;
  opacity: 0.8;
}

.dark .markdown-content :deep(del) {
  color: #6b7280;
}

/* === 通用样式 === */
.markdown-content :deep(*) {
  max-width: 100%;
  box-sizing: border-box;
}

/* === 括号间距优化 === */
/* 中文括号间距 */
.markdown-content :deep(*) {
  /* 为中文括号前后添加适当间距 */
  text-spacing: ideograph-alpha ideograph-numeric;
}

/* 英文括号间距 */
.markdown-content :deep(p),
.markdown-content :deep(li),
.markdown-content :deep(td),
.markdown-content :deep(th),
.markdown-content :deep(blockquote) {
  /* 使用CSS伪元素为括号添加间距 */
  word-spacing: 0.05em;
}

/* 针对特定括号字符的间距调整 */
.markdown-content :deep(*:not(pre):not(code)) {
  /* 为括号前后添加微小间距 */
  font-variant-numeric: proportional-nums;
  font-feature-settings:
    'kern' 1,
    'liga' 1;
}

/* 使用CSS选择器为括号添加间距 */
.markdown-content :deep(p:not(pre p)),
.markdown-content :deep(li:not(pre li)),
.markdown-content :deep(td:not(pre td)),
.markdown-content :deep(th:not(pre th)) {
  /* 通过letter-spacing为文本添加适当间距 */
  letter-spacing: 0.01em;
}

/* 特殊处理：为包含括号的文本添加更好的间距 */
.markdown-content :deep(*:not(pre):not(code):not(.katex)) {
  /* 使用CSS的text-spacing属性（如果支持） */
  -webkit-text-spacing: ideograph-alpha ideograph-numeric;
  -moz-text-spacing: ideograph-alpha ideograph-numeric;
  text-spacing: ideograph-alpha ideograph-numeric;
}

/* 代码块中保持原有间距 */
.markdown-content :deep(pre),
.markdown-content :deep(code),
.markdown-content :deep(.katex) {
  letter-spacing: 0;
  word-spacing: 0;
  text-spacing: normal;
  -webkit-text-spacing: normal;
  -moz-text-spacing: normal;
}

.markdown-content :deep(::selection) {
  background: rgba(99, 102, 241, 0.2);
  color: inherit;
}

.dark .markdown-content :deep(::selection) {
  background: rgba(129, 140, 248, 0.3);
}

/* 滚动条美化 */
.markdown-content :deep(*::-webkit-scrollbar) {
  height: 6px;
  width: 6px;
}

.markdown-content :deep(*::-webkit-scrollbar-track) {
  background: rgba(243, 244, 246, 0.5);
  border-radius: 3px;
}

.markdown-content :deep(*::-webkit-scrollbar-thumb) {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.markdown-content :deep(*::-webkit-scrollbar-thumb:hover) {
  background: rgba(107, 114, 128, 0.7);
}

.dark .markdown-content :deep(*::-webkit-scrollbar-track) {
  background: rgba(31, 41, 55, 0.5);
}

.dark .markdown-content :deep(*::-webkit-scrollbar-thumb) {
  background: rgba(75, 85, 99, 0.5);
}

.dark .markdown-content :deep(*::-webkit-scrollbar-thumb:hover) {
  background: rgba(107, 114, 128, 0.7);
}

/* === 新插件样式 === */
/* GitHub风格警示框样式 */
.markdown-content :deep(.markdown-alert) {
  padding: 0.75rem 1rem;
  margin: 0.75rem 0;
  border: 1px solid transparent;
  border-radius: 0.5rem;
  position: relative;
  font-size: 12px;
}

/* 信息警示框 */
.markdown-content :deep(.markdown-alert-note) {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  color: #1e40af;
}

.dark .markdown-content :deep(.markdown-alert-note) {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.4);
  color: #93c5fd;
}

/* 提示警示框 */
.markdown-content :deep(.markdown-alert-tip) {
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.3);
  color: #15803d;
}

.dark .markdown-content :deep(.markdown-alert-tip) {
  background: rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.4);
  color: #86efac;
}

/* 重要警示框 */
.markdown-content :deep(.markdown-alert-important) {
  background: rgba(168, 85, 247, 0.1);
  border-color: rgba(168, 85, 247, 0.3);
  color: #7c3aed;
}

.dark .markdown-content :deep(.markdown-alert-important) {
  background: rgba(168, 85, 247, 0.15);
  border-color: rgba(168, 85, 247, 0.4);
  color: #c4b5fd;
}

/* 警告警示框 */
.markdown-content :deep(.markdown-alert-warning) {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
  color: #d97706;
}

.dark .markdown-content :deep(.markdown-alert-warning) {
  background: rgba(245, 158, 11, 0.15);
  border-color: rgba(245, 158, 11, 0.4);
  color: #fbbf24;
}

/* 警告警示框 */
.markdown-content :deep(.markdown-alert-caution) {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
  color: #dc2626;
}

.dark .markdown-content :deep(.markdown-alert-caution) {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.4);
  color: #fca5a5;
}

/* 自定义容器样式 */
.markdown-content :deep(.custom-container) {
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  border-left: 0.25rem solid;
  position: relative;
}

.markdown-content :deep(.custom-container-title) {
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  font-size: 13px;
}

/* 信息容器 */
.markdown-content :deep(.custom-container.info) {
  background: rgba(59, 130, 246, 0.1);
  border-left-color: #3b82f6;
}

.markdown-content :deep(.custom-container.info .custom-container-title) {
  color: #1e40af;
}

.dark .markdown-content :deep(.custom-container.info) {
  background: rgba(59, 130, 246, 0.15);
}

.dark .markdown-content :deep(.custom-container.info .custom-container-title) {
  color: #93c5fd;
}

/* 提示容器 */
.markdown-content :deep(.custom-container.tip) {
  background: rgba(34, 197, 94, 0.1);
  border-left-color: #22c55e;
}

.markdown-content :deep(.custom-container.tip .custom-container-title) {
  color: #15803d;
}

.dark .markdown-content :deep(.custom-container.tip) {
  background: rgba(34, 197, 94, 0.15);
}

.dark .markdown-content :deep(.custom-container.tip .custom-container-title) {
  color: #86efac;
}

/* 警告容器 */
.markdown-content :deep(.custom-container.warning) {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: #f59e0b;
}

.markdown-content :deep(.custom-container.warning .custom-container-title) {
  color: #d97706;
}

.dark .markdown-content :deep(.custom-container.warning) {
  background: rgba(245, 158, 11, 0.15);
}

.dark .markdown-content :deep(.custom-container.warning .custom-container-title) {
  color: #fbbf24;
}

/* 危险容器 */
.markdown-content :deep(.custom-container.danger) {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: #ef4444;
}

.markdown-content :deep(.custom-container.danger .custom-container-title) {
  color: #dc2626;
}

.dark .markdown-content :deep(.custom-container.danger) {
  background: rgba(239, 68, 68, 0.15);
}

.dark .markdown-content :deep(.custom-container.danger .custom-container-title) {
  color: #fca5a5;
}

/* 脚注样式 */
.markdown-content :deep(.footnote-ref) {
  color: #3b82f6;
  text-decoration: none;
  font-size: 0.8em;
  vertical-align: super;
  margin: 0 0.125rem;
}

.markdown-content :deep(.footnote-ref:hover) {
  text-decoration: underline;
}

.dark .markdown-content :deep(.footnote-ref) {
  color: #93c5fd;
}

.markdown-content :deep(.footnotes) {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  font-size: 11px;
}

.dark .markdown-content :deep(.footnotes) {
  border-top-color: #374151;
}

.markdown-content :deep(.footnotes ol) {
  padding-left: 1rem;
}

.markdown-content :deep(.footnotes li) {
  margin: 0.25rem 0;
}

.markdown-content :deep(.footnote-backref) {
  color: #6b7280;
  text-decoration: none;
  margin-left: 0.25rem;
}

.markdown-content :deep(.footnote-backref:hover) {
  color: #3b82f6;
}

.dark .markdown-content :deep(.footnote-backref) {
  color: #9ca3af;
}

.dark .markdown-content :deep(.footnote-backref:hover) {
  color: #93c5fd;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .markdown-content {
    font-size: 11px;
  }

  .markdown-content :deep(.katex-display) {
    margin: 1rem 0;
    padding: 0.75rem;
  }

  .markdown-content :deep(.katex-display > .katex) {
    font-size: 0.9em;
  }

  .markdown-content :deep(h1) {
    font-size: 1rem;
  }
  .markdown-content :deep(h2) {
    font-size: 0.9375rem;
  }
  .markdown-content :deep(h3) {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .markdown-content {
    font-size: 10px;
  }

  .markdown-content :deep(.katex-display) {
    margin: 0.75rem 0;
    padding: 0.5rem;
  }

  .markdown-content :deep(.katex-display > .katex) {
    font-size: 0.8em;
  }
}
</style>
