@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --transition-duration: 200ms;
  }

  html {
    @apply transition-colors duration-200 text-base;
  }

  body {
    @apply bg-light text-text-light transition-colors duration-200;
  }

  .dark body {
    @apply bg-dark text-text-dark;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-bold transition-colors duration-200;
  }

  h1 {
    @apply text-2xl;
  }

  h2 {
    @apply text-xl;
  }

  h3 {
    @apply text-lg;
  }

  section {
    @apply transition-colors duration-200;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-dark;
  }

  .dark .btn-primary {
    @apply bg-primary hover:bg-primary-light;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-light;
  }

  .btn-outline {
    @apply border border-primary bg-transparent text-primary hover:bg-primary hover:text-white;
  }

  .dark .btn-outline {
    @apply border-primary-light text-primary-light hover:bg-primary-light hover:text-dark;
  }

  .container {
    @apply mx-auto px-4 max-w-7xl;
  }

  .card {
    @apply bg-white rounded-md shadow-sm transition-all duration-200;
  }

  .dark .card {
    @apply bg-dark-secondary text-text-dark;
  }

  /* 移动端菜单动画 */
  .mobile-menu-enter-active,
  .mobile-menu-leave-active {
    @apply transition-all duration-200 ease-in-out;
  }

  .mobile-menu-enter-from,
  .mobile-menu-leave-to {
    @apply opacity-0 -translate-y-4;
  }

  .mobile-menu-enter-to,
  .mobile-menu-leave-from {
    @apply opacity-100 translate-y-0;
  }

  /* 扩展样式 - 带阴影的卡片 */
  .card-shadow {
    @apply shadow-sm hover:shadow-md transition-shadow duration-200;
  }

  /* 深色模式下卡片更清晰的边界 */
  .dark .card,
  .dark .feature-card {
    @apply border-gray-800;
  }

  /* 精细文字颜色样式 */
  .text-primary-content {
    @apply text-white;
  }

  .text-light-content {
    @apply text-gray-800;
  }

  .dark .text-light-content {
    @apply text-gray-100;
  }

  .text-muted {
    @apply text-gray-600 dark:text-gray-300;
  }

  /* 提高白色背景下文字可见性 */
  .bg-white,
  .bg-light,
  .bg-light-secondary {
    @apply text-text-light;
  }

  /* 提高深色背景下文字可见性 */
  .bg-dark,
  .bg-dark-secondary,
  .bg-primary,
  .bg-secondary {
    @apply text-text-dark;
  }
}
