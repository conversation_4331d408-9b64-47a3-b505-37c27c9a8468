import { defineStore } from 'pinia';

interface SettingsState {
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  layout: {
    sidebarCollapsed: boolean;
    contentWidth: 'full' | 'boxed';
    navbarFixed: boolean;
    footerFixed: boolean;
  };
  dateFormat: string;
  timeFormat: string;
}

export const useSettingsStore = defineStore('settings', {
  state: (): SettingsState => ({
    language: 'zh',
    notifications: {
      email: true,
      push: true,
      sms: false,
    },
    layout: {
      sidebarCollapsed: false,
      contentWidth: 'full',
      navbarFixed: true,
      footerFixed: false,
    },
    dateFormat: 'YYYY-MM-DD',
    timeFormat: 'HH:mm:ss',
  }),

  actions: {
    updateSetting<T extends keyof SettingsState>(key: T, value: SettingsState[T]) {
      this.$patch({ [key]: value });
    },

    toggleSidebar() {
      this.layout.sidebarCollapsed = !this.layout.sidebarCollapsed;
    },

    resetSettings() {
      this.$reset();
    },
  },

  persist: {
    key: 'settings-store',
    pick: ['language', 'notifications', 'layout', 'dateFormat', 'timeFormat'],
  },
});
