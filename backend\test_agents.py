#!/usr/bin/env python3
"""
测试 Agent API 功能的脚本
"""
import asyncio
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.selector import get_agent, get_available_agents, AgentType


async def test_agents():
    """测试所有可用的 agents"""
    print("🚀 开始测试 Agent API 功能...")

    # 测试获取可用 agents 列表
    print("\n📋 可用的 Agents:")
    available_agents = get_available_agents()
    for agent_id in available_agents:
        print(f"  - {agent_id}")

    # 测试每个 agent
    test_message = "你好，请简单介绍一下你自己。"

    for agent_type in AgentType:
        print(f"\n🤖 测试 {agent_type.value}...")
        try:
            # 创建 agent 实例
            agent = get_agent(
                model_id="gpt-4.1",
                agent_id=agent_type,
                user_id="test_user",
                session_id="test_session",
                debug_mode=True
            )

            print(f"✅ {agent_type.value} 创建成功")
            print(f"   名称: {agent.name}")
            print(f"   ID: {agent.agent_id}")

            # 注意：这里不实际运行 agent，因为需要数据库连接
            # response = await agent.arun(test_message, stream=False)
            # print(f"   响应: {response.content[:100]}...")

        except Exception as e:
            print(f"❌ {agent_type.value} 测试失败: {str(e)}")

    print("\n✨ 测试完成！")


if __name__ == "__main__":
    asyncio.run(test_agents())
