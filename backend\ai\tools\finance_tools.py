from agno.tools import Toolkit

from app.core.core_consts import IntervalType
from app.core.core_log import logger
from app.services import MarketDataService


class FinanceTools(Toolkit):
    """
    FinanceTools 是一个用于获取加密货币市场数据的工具包。

    参数:
        kline_data (bool): 是否启用K线数据获取功能
        latest_price (bool): 是否启用最新价格获取功能
        market_summary (bool): 是否启用市场概要获取功能
        exchange_info (bool): 是否启用交易所信息获取功能
        enable_all (bool): 是否启用所有功能
    """

    def __init__(
            self,
            kline_data: bool = True,
            check_symbol_has_kline_data: bool = True,
            enable_all: bool = True,
            **kwargs,
    ):
        super().__init__(name="finance_tools", **kwargs)

        self.market_data_service = MarketDataService()

        if kline_data or enable_all:
            self.register(self.get_kline_data)

        if check_symbol_has_kline_data or enable_all:
            self.register(self.market_data_service.is_kline_data_available)

    async def get_kline_data(self, symbol: str, market_type: str, exchange: str,
                             timeframe: IntervalType = "1d", limit: int = 1) -> str:
        f"""
        获取指定交易对的K线数据。

        参数:
            symbol (str): 交易对符号，例如 'BTCUSDT'
            market_type (str): 市场类型，例如 'spot'、'usdt_future'、'coin_future'
            exchange (str): 交易所名称，例如 'binance'
            timeframe (str): 时间周期，可选：{list(IntervalType)}, 默认请 '1d'
            limit (int): 返回的数据点数量，默认为1

        返回:
            str: JSON格式的K线数据

        PS：获取到的k线数据中，volume字段是成交额（单位：万$）
        """
        logger.info(f"获取{exchange}的{symbol} {timeframe} K线数据，数量：{limit}")
        df = await self.market_data_service.get_kline_data_by_symbol(
            symbol=symbol,
            market_type=market_type,
            exchange=exchange,
            timeframe=timeframe,
            limit=limit
        )
        if df.empty:
            return "没有查询到行情数据"
        else:
            return df.to_json(orient='records')
