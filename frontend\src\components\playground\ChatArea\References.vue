<template>
  <div class="flex flex-col gap-4">
    <div v-for="(referenceData, index) in references" :key="`${referenceData.query}-${index}`" class="flex flex-col gap-3">
      <div class="flex flex-wrap gap-3">
        <ReferenceItem v-for="(reference, refIndex) in referenceData.references" :key="`${reference.name}-${reference.meta_data.chunk}-${refIndex}`" :reference="reference" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { ReferenceData } from '@/types/playground';
import ReferenceItem from './ReferenceItem.vue';

interface Props {
  references: ReferenceData[];
}

defineProps<Props>();
</script>
