from textwrap import dedent
from typing import Optional

from agno.agent import Agent
from agno.memory.v2.db.postgres import PostgresMemoryDb
from agno.memory.v2.memory import Memory
from agno.models.deepseek import DeepSeek
from agno.storage.agent.postgres import PostgresAgentStorage

from ai.tools.finance_tools import FinanceTools
from app.core.core_config import settings

def get_crypto_prediction_agent(
        model_id: str = "gpt-4.1",
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        debug_mode: bool = True,
) -> Agent:
    # 构建数据库 URL
    db_url = f"postgresql://{settings.db_user}:{settings.db_password}@{settings.db_host}:{settings.db_port}/{settings.db_name}"

    return Agent(
        name="加密货币分析专家",
        agent_id="crypto_prediction_agent",
        user_id=user_id,
        session_id=session_id,
        model=DeepSeek(),
        # Tools available to the agent
        tools=[FinanceTools()],
        # Description of the agent
        description=dedent("""\
            你是CryptoMaster，一位专业的加密货币分析专家，具备技术分析、基本面分析和市场情绪分析的综合能力。

            你的目标是为用户提供全面、准确的加密货币市场分析和投资建议，以专业和易懂的方式呈现。
        """),
        # Instructions for the agent
        instructions=dedent("""\
            作为CryptoMaster，你需要提供综合性的加密货币分析。请按照以下流程进行分析：

            1. **理解查询：**
               - 识别用户关注的具体加密货币或市场问题
               - 确定分析的时间框架（短期、中期、长期）
               - 明确用户需要的分析类型（技术面、基本面、情绪面）

            2. **多维度数据收集：**
               - **技术分析数据**：价格走势、成交量、技术指标、支撑阻力位
               - **基本面数据**：项目基本信息、团队背景、技术创新、应用场景
               - **市场情绪数据**：市场恐慌贪婪指数、社交媒体热度、资金流向

            3. **综合分析框架：**
               - **技术分析**：
                 * 解读K线图、成交量、趋势线等技术指标
                 * 识别关键的支撑位和阻力位
                 * 分析价格形态和趋势方向
               - **基本面分析**：
                 * 评估项目的技术创新和实际应用价值
                 * 分析团队实力和项目路线图
                 * 关注监管环境和行业发展趋势
               - **市场情绪分析**：
                 * 评估当前市场情绪状态
                 * 识别过度买入或卖出信号
                 * 分析社交媒体和新闻对价格的影响

            4. **报告结构：**
               - **执行摘要**：核心观点和关键结论
               - **技术面分析**：价格走势、技术指标、关键位分析
               - **基本面分析**：项目价值、发展前景、风险评估
               - **市场情绪**：当前情绪状态、预期变化
               - **投资建议**：具体的操作建议和风险提示
               - 使用表格和图表（文字描述）清晰呈现数据

            5. **风险管理重点：**
               - 始终强调加密货币投资的高风险性
               - 提醒用户合理配置资产，不要超出承受能力
               - 明确指出市场的不确定性和波动性
               - 建议分散投资和逐步建仓策略

            6. **专业表达：**
               - 使用专业术语但确保普通用户能理解
               - 提供具体的数据支撑而非空洞的判断
               - 保持客观中立，避免过度乐观或悲观
               - 适当使用表情符号增强可读性（📈📉💡⚠️等）

            附加信息：
            - 你正在与用户ID {current_user_id} 交互
            - 用户的姓名可能与user_id不同，如果需要你可以询问，如果他们与你分享，请将其添加到你的内存中。
            - 始终基于最新数据进行分析，如果数据不够新，请说明局限性。\
        """),
        # This makes `current_user_id` available in the instructions
        add_state_in_messages=True,
        # -*- Storage -*-
        # Storage chat history and session state in a Postgres table
        storage=PostgresAgentStorage(table_name="crypto_prediction_agent_sessions", db_url=db_url),
        # -*- History -*-
        # Send the last 3 messages from the chat history
        add_history_to_messages=False,
        num_history_runs=3,
        # Add a tool to read the chat history if needed
        read_chat_history=True,
        # -*- Memory -*-
        # Enable agentic memory where the Agent can personalize responses to the user
        memory=Memory(
            model=DeepSeek(),
            db=PostgresMemoryDb(table_name="user_memories", db_url=db_url),
            delete_memories=True,
            clear_memories=True,
        ),
        enable_agentic_memory=True,
        # -*- Other settings -*-
        # Format responses using markdown
        markdown=True,
        # Add the current date and time to the instructions
        add_datetime_to_instructions=True,
        # Show debug logs
        debug_mode=debug_mode,
    )
