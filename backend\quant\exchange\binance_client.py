"""
币安交易所API客户端
整合REST API实现
"""
from typing import Dict, Any, Optional, List, Union, Callable
from urllib.parse import urlencode

import aiohttp

from app.core.core_log import get_logger
from app.models import ApiKey, Symbol
from quant.exchange.base import ExchangeApiInterface, MarketType
from quant.exchange.binance_endpoints import BinanceEndpoints
from quant.exchange.binance_ws import BinanceWebSocket
from quant.exchange.utils import HttpUtils

logger = get_logger("binance_client")


class BinanceClient(ExchangeApiInterface):
    """
    币安交易所客户端
    提供统一的API接口
    """

    def __init__(self, api: ApiKey = None, symbol: Symbol = None, market_type: str = "spot"):
        """
        初始化币安客户端
        
        Args:
            api: API密钥信息
            symbol: 交易对信息
            market_type: 市场类型 (spot, usdt_future, coin_future)
        """
        super().__init__(api, symbol)
        self.market_type = market_type
        # 初始化WebSocket客户端
        self.ws_client = BinanceWebSocket(market_type)

    async def _private_request(
            self, method: str, url: str, params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """发送私有请求（需要签名）"""
        if not self.api or not self.api.api_key or not self.api.api_secret:
            raise ValueError("缺少API密钥")

        # 准备请求参数
        query_params = params.copy() if params else {}
        query_params["timestamp"] = HttpUtils.get_timestamp()
        query_params["recvWindow"] = 5000  # 接收窗口

        # 生成签名
        query_string = urlencode(query_params)
        signature = HttpUtils.create_signature(self.api.api_secret, query_string)
        query_params["signature"] = signature

        # 准备请求头
        headers = {
            "X-MBX-APIKEY": self.api.api_key
        }

        # 发送请求
        async with aiohttp.ClientSession(headers=headers) as session:
            try:
                if method.upper() == "GET":
                    async with session.get(url, params=query_params, timeout=10) as response:
                        response.raise_for_status()
                        return await response.json()
                elif method.upper() == "POST":
                    async with session.post(url, data=query_params, timeout=10) as response:
                        response.raise_for_status()
                        return await response.json()
                elif method.upper() == "DELETE":
                    async with session.delete(url, params=query_params, timeout=10) as response:
                        response.raise_for_status()
                        return await response.json()
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")
            except Exception as e:
                logger.error(f"HTTP请求失败: {url}, 错误: {str(e)}")
                raise

    @staticmethod
    def get_market_type_url(market_type: Union[str, MarketType]) -> str:
        """获取市场类型URL"""
        if isinstance(market_type, MarketType):
            market_type = market_type.name.lower()

        if market_type == "spot":
            return BinanceEndpoints.SPOT_API_URL
        elif market_type == "usdt_future":
            return BinanceEndpoints.USDT_FUTURES_API_URL
        elif market_type == "coin_future":
            return BinanceEndpoints.COIN_FUTURES_API_URL
        else:
            raise ValueError(f"不支持的市场类型: {market_type}")

    async def get_symbols(self):
        """获取交易对列表"""
        url = BinanceEndpoints.get_market_endpoint("exchange_info", self.market_type)
        return await HttpUtils.public_request("GET", url)

    # REST API方法实现
    async def get_ticker(self, symbol: str) -> Dict[str, Any]:
        """获取市场行情"""
        url = BinanceEndpoints.get_market_endpoint("ticker_price")
        params = {"symbol": symbol}
        return await HttpUtils.public_request("GET", url, params)

    async def get_klines(self, symbol: str, interval: str, limit: int = 500) -> List[Dict[str, Any]]:
        """获取K线数据"""
        url = BinanceEndpoints.get_market_endpoint("klines")
        params = {
            "symbol": symbol,
            "interval": interval,
            "limit": limit
        }

        response = await HttpUtils.public_request("GET", url, params)

        # 格式化响应
        result = []
        for item in response:
            result.append({
                "open_time": item[0],
                "open": float(item[1]),
                "high": float(item[2]),
                "low": float(item[3]),
                "close": float(item[4]),
                "volume": float(item[5]),
                "close_time": item[6],
                "quote_volume": float(item[7]),
                "trades": item[8],
                "taker_buy_base_volume": float(item[9]),
                "taker_buy_quote_volume": float(item[10])
            })

        return result

    async def get_account(self) -> Dict[str, Any]:
        """获取账户信息"""
        url = BinanceEndpoints.get_account_endpoint("account")
        return await self._private_request("GET", url)

    async def place_order(
            self, symbol: str, side: str, order_type: str,
            quantity: float = None, price: float = None, **kwargs
    ) -> Dict[str, Any]:
        """下单"""
        url = BinanceEndpoints.get_trade_endpoint("order")

        params = {
            "symbol": symbol,
            "side": side,
            "type": order_type,
            **kwargs
        }

        if quantity:
            params["quantity"] = quantity

        if price and order_type != "MARKET":
            params["price"] = price

        return await self._private_request("POST", url, params)

    async def cancel_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """取消订单"""
        url = BinanceEndpoints.get_trade_endpoint("cancel_order")

        params = {
            "symbol": symbol,
            "orderId": order_id
        }

        return await self._private_request("DELETE", url, params)

    async def get_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """查询订单"""
        url = BinanceEndpoints.get_trade_endpoint("order")

        params = {
            "symbol": symbol,
            "orderId": order_id
        }

        return await self._private_request("GET", url, params)

    # WebSocket方法
    async def connect_ws(self) -> None:
        """连接WebSocket"""
        await self.ws_client.connect()

    async def disconnect_ws(self) -> None:
        """断开WebSocket连接"""
        await self.ws_client.disconnect()

    async def subscribe_ticker(self, symbols: List[str], callback: Callable) -> None:
        """订阅行情"""
        await self.ws_client.subscribe("ticker", symbols, callback)

    async def subscribe_kline(self, symbols: List[str], callback: Callable) -> None:
        """订阅K线"""
        await self.ws_client.subscribe("kline", symbols, callback)

    async def subscribe_depth(self, symbols: List[str], callback: Callable) -> None:
        """订阅深度"""
        await self.ws_client.subscribe("depth", symbols, callback)

    async def subscribe_trade(self, symbols: List[str], callback: Callable) -> None:
        """订阅成交"""
        await self.ws_client.subscribe("trade", symbols, callback)

    async def unsubscribe_ticker(self, symbols: List[str]) -> None:
        """取消订阅行情"""
        await self.ws_client.unsubscribe("ticker", symbols)

    async def unsubscribe_kline(self, symbols: List[str]) -> None:
        """取消订阅K线"""
        await self.ws_client.unsubscribe("kline", symbols)

    async def unsubscribe_depth(self, symbols: List[str]) -> None:
        """取消订阅深度"""
        await self.ws_client.unsubscribe("depth", symbols)

    async def unsubscribe_trade(self, symbols: List[str]) -> None:
        """取消订阅成交"""
        await self.ws_client.unsubscribe("trade", symbols)

    # 工具方法
    async def api_test(self) -> bool:
        """测试API连接"""
        try:
            await self.get_ticker("BTCUSDT")
            return True
        except Exception as e:
            logger.error(f"API测试失败: {str(e)}")
            return False
