/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./public/index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#333333', // 由紫色改为深灰色
          dark: '#1A1A1A', // 更深的灰色
          light: '#666666', // 浅灰色
        },
        secondary: {
          DEFAULT: '#2D2A37',
          dark: '#232029',
          light: '#42404C',
        },
        dark: {
          DEFAULT: '#000000', // 纯黑色
          secondary: '#0F0F0F', // 接近纯黑
          alt: '#141414', // 深黑变体
        },
        light: {
          DEFAULT: '#FFFFFF', // 纯白色
          secondary: '#F5F5F5', // 浅灰白色
        },
        accent: {
          DEFAULT: '#555555', // 更深的灰色，提高对比度
          secondary: '#666666', // 中深灰色
        },
        text: {
          light: '#333333', // 浅色背景上的文字颜色
          dark: '#F0F0F0', // 深色背景上的文字颜色
        },
      },
      fontSize: {
        xs: '0.8rem', // 增大
        sm: '0.9rem', // 增大
        base: '1rem', // 增大
        lg: '1.125rem', // 增大
        xl: '1.25rem', // 增大
        '2xl': '1.5rem', // 增大
      },
    },
  },
  plugins: [],
};
