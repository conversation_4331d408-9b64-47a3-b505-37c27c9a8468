<template>
  <n-config-provider :locale="naiveLocale" :theme="theme" :theme-overrides="themeOverrides">
    <n-loading-bar-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <n-message-provider>
            <div class="min-h-screen flex flex-col">
              <TheHeader v-if="route.meta?.showHeader !== false" />
              <div class="flex-1">
                <router-view />
              </div>
              <TheFooter v-if="route.meta?.showFooter !== false" />
            </div>
          </n-message-provider>
        </n-notification-provider>
      </n-dialog-provider>
    </n-loading-bar-provider>
  </n-config-provider>
  <StagewiseToolbar v-if="isDevelopment" :config="stagewiseConfig" />
</template>

<script lang="ts" setup>
import { computed, onBeforeMount, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';
import { darkTheme, enUS, GlobalThemeOverrides, NConfigProvider, NDialogProvider, NLoadingBarProvider, NMessageProvider, NNotificationProvider, zhCN } from 'naive-ui';
import { setLanguage, SupportedLocales } from './i18n';
import { initializeStores } from './stores';
import TheHeader from '@/components/layout/TheHeader.vue';
import TheFooter from '@/components/layout/TheFooter.vue';
import { StagewiseToolbar } from '@stagewise/toolbar-vue';

const { locale } = useI18n();
const route = useRoute();

// 初始化所有stores
const { settingsStore, themeStore } = initializeStores();

// 根据当前语言设置Naive UI的语言包
const naiveLocale = computed(() => (locale.value === 'zh-CN' ? zhCN : enUS));

// 根据主题设置计算当前使用的主题
const theme = computed(() => (themeStore.isDarkMode ? darkTheme : null));

// Stagewise配置
const stagewiseConfig = ref({
  plugins: [],
});

const isDevelopment = import.meta.env.MODE === 'development' && false;

// 自定义主题覆盖
const themeOverrides = computed<GlobalThemeOverrides>(() => {
  return {
    common: {
      primaryColor: themeStore.isDarkMode ? '#555555' : '#333333', // accent & primary from tailwind
      primaryColorHover: themeStore.isDarkMode ? '#666666' : '#555555', // accent-secondary & accent
      primaryColorPressed: themeStore.isDarkMode ? '#444444' : '#1A1A1A', // darker version & primary-dark
      borderRadius: '0.375rem', // Tailwind的rounded-md (0.375rem)
    },
    Button: {
      textColorPrimary: '#ffffff', // white
    },
    Card: {
      borderRadius: '0.5rem', // Tailwind的rounded-lg (0.5rem)
    },
  };
});

onBeforeMount(() => {
  // 根据设置初始化语言
  const language = settingsStore.language as SupportedLocales;
  setLanguage(language);

  // 设置文档语言
  document.documentElement.setAttribute('lang', locale.value);
  // themeStore的初始化已经在initializeStores中处理
});

// 监听语言变化，动态更新html lang属性
watch(locale, val => {
  document.documentElement.setAttribute('lang', val);
});
</script>

<style>
#app {
  font-family:
    'Inter',
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
}
</style>
