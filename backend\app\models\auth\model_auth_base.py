from sqlmodel import Field, SQLModel


# 用户状态历史基础模型
class UserStatusBase(SQLModel):
    """用户状态历史基础模型
    
    包含用户状态变更的基本信息
    """
    status: str = Field(..., title="状态")
    reason: str | None = Field(None, title="原因")


# 密码历史基础模型
class PasswordHistoryBase(SQLModel):
    """密码历史基础模型
    
    包含密码历史记录的基本信息
    """
    password_hash: str = Field(..., title="密码哈希")


# 身份认证提供者基础模型
class AuthProviderBase(SQLModel):
    """身份认证提供者基础模型
    
    包含身份认证提供者的基本信息
    """
    provider: str = Field(..., title="提供者")
    provider_user_id: str = Field(..., title="提供者用户ID")


# 登录记录基础模型
class LoginHistoryBase(SQLModel):
    """登录记录基础模型
    
    包含登录记录的基本信息
    """
    login_type: str = Field(..., title="登录类型")
    ip_address: str | None = Field(None, title="IP地址")
    user_agent: str | None = Field(None, title="用户代理")
    success: bool = Field(..., title="是否成功")
