<template>
  <div class="cursor-default rounded-md bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 px-2.5 py-1.5 text-xs transition-all duration-200">
    <p class="font-mono uppercase text-gray-800 dark:text-gray-300 font-medium">{{ tool.tool_name }}</p>
  </div>
</template>

<script lang="ts" setup>
import type { ToolCall } from '@/types/playground';

interface Props {
  tool: ToolCall;
}

defineProps<Props>();
</script>
