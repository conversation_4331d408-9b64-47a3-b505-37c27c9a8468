import Foundation

// 消息角色枚举
enum MessageRole: String, Codable {
    case user = "user"
    case agent = "agent"
    case system = "system"
}

// 工具调用结构
struct ToolCall: Codable, Identifiable {
    let id: String
    let name: String
    let arguments: [String: Any]
    let result: String?
    
    enum CodingKeys: String, CodingKey {
        case id, name, arguments, result
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        result = try container.decodeIfPresent(String.self, forKey: .result)
        
        // 处理arguments的动态类型
        if let argumentsData = try? container.decode([String: String].self, forKey: .arguments) {
            arguments = argumentsData
        } else {
            arguments = [:]
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encodeIfPresent(result, forKey: .result)
        // 简化处理，只编码字符串类型
        let stringArgs = arguments.compactMapValues { $0 as? String }
        try container.encode(stringArgs, forKey: .arguments)
    }
}

// 消息指标
struct MessageMetrics: Codable {
    let time: Double?
    let promptTokens: Int?
    let completionTokens: Int?
    let totalTokens: Int?
    
    enum CodingKeys: String, CodingKey {
        case time
        case promptTokens = "prompt_tokens"
        case completionTokens = "completion_tokens"
        case totalTokens = "total_tokens"
    }
}

// 消息结构
struct Message: Identifiable, Codable {
    let id: String
    let role: MessageRole
    var content: String
    var toolCalls: [ToolCall]?
    var images: [String]?
    var audio: [String]?
    var videos: [String]?
    var responseAudio: String?
    var metrics: MessageMetrics?
    var createdAt: TimeInterval
    var isStreaming: Bool
    var streamingError: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, role, content
        case toolCalls = "tool_calls"
        case images, audio, videos
        case responseAudio = "response_audio"
        case metrics
        case createdAt = "created_at"
        case isStreaming = "is_streaming"
        case streamingError = "streaming_error"
    }
    
    init(
        id: String = UUID().uuidString,
        role: MessageRole,
        content: String,
        toolCalls: [ToolCall]? = nil,
        images: [String]? = nil,
        audio: [String]? = nil,
        videos: [String]? = nil,
        responseAudio: String? = nil,
        metrics: MessageMetrics? = nil,
        createdAt: TimeInterval = Date().timeIntervalSince1970,
        isStreaming: Bool = false,
        streamingError: Bool = false
    ) {
        self.id = id
        self.role = role
        self.content = content
        self.toolCalls = toolCalls
        self.images = images
        self.audio = audio
        self.videos = videos
        self.responseAudio = responseAudio
        self.metrics = metrics
        self.createdAt = createdAt
        self.isStreaming = isStreaming
        self.streamingError = streamingError
    }
}

// 流式响应事件类型
enum StreamEventType: String, Codable {
    case runStarted = "RunStarted"
    case runResponse = "RunResponse"
    case runError = "RunError"
    case runCompleted = "RunCompleted"
    case toolCallStarted = "ToolCallStarted"
    case toolCallCompleted = "ToolCallCompleted"
    case reasoningStarted = "ReasoningStarted"
    case reasoningStep = "ReasoningStep"
    case reasoningCompleted = "ReasoningCompleted"
    case memoryUpdated = "MemoryUpdated"
    case workflowStarted = "WorkflowStarted"
    case workflowCompleted = "WorkflowCompleted"
}

// 流式响应数据结构
struct StreamResponse: Codable {
    let event: StreamEventType
    let sessionId: String?
    let content: String?
    let tools: [ToolCall]?
    let images: [String]?
    let audio: [String]?
    let videos: [String]?
    let responseAudio: String?
    let messages: [Message]?
    let createdAt: TimeInterval?
    
    enum CodingKeys: String, CodingKey {
        case event
        case sessionId = "session_id"
        case content, tools, images, audio, videos
        case responseAudio = "response_audio"
        case messages
        case createdAt = "created_at"
    }
    
    // 便利初始化方法，用于创建简单的内容响应
    init(content: String, isComplete: Bool = false, error: String? = nil) {
        self.event = isComplete ? .runCompleted : .runResponse
        self.sessionId = nil
        self.content = content
        self.tools = nil
        self.images = nil
        self.audio = nil
        self.videos = nil
        self.responseAudio = nil
        self.messages = nil
        self.createdAt = Date().timeIntervalSince1970
    }
    
    // 便利初始化方法，用于创建错误响应
    init(error: String) {
        self.event = .runError
        self.sessionId = nil
        self.content = error
        self.tools = nil
        self.images = nil
        self.audio = nil
        self.videos = nil
        self.responseAudio = nil
        self.messages = nil
        self.createdAt = Date().timeIntervalSince1970
    }
    
    // 完整的初始化方法
    init(
        event: StreamEventType,
        sessionId: String? = nil,
        content: String? = nil,
        tools: [ToolCall]? = nil,
        images: [String]? = nil,
        audio: [String]? = nil,
        videos: [String]? = nil,
        responseAudio: String? = nil,
        messages: [Message]? = nil,
        createdAt: TimeInterval? = nil
    ) {
        self.event = event
        self.sessionId = sessionId
        self.content = content
        self.tools = tools
        self.images = images
        self.audio = audio
        self.videos = videos
        self.responseAudio = responseAudio
        self.messages = messages
        self.createdAt = createdAt ?? Date().timeIntervalSince1970
    }
} 