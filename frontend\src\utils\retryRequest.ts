/**
 * 请求重试机制
 * 用于在网络不稳定或服务器暂时性错误时自动重试请求
 */
import axios from 'axios';
import service from './request';

// 声明一个更详细的AxiosError类型，避免TypeScript类型错误
interface AxiosErrorType {
  isAxiosError: boolean;
  response?: {
    status: number;
  };
  config?: any;
  code?: string;
  message?: string;
}

// 可重试的HTTP状态码
const RETRYABLE_STATUS_CODES = [408, 429, 500, 502, 503, 504];

// 默认重试配置
export interface RetryConfig {
  retry: number; // 重试次数
  retryDelay: number; // 重试延迟时间(ms)
  retryStatusCodes: number[]; // 需要重试的状态码
  // 重命名回调函数参数以消除未使用变量警告
  onRetry?: (_retryCount: number, _error: unknown) => void; // 重试回调
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  retry: 3, // 默认重试3次
  retryDelay: 1000, // 默认延迟1秒
  retryStatusCodes: RETRYABLE_STATUS_CODES,
};

/**
 * 判断是否为Axios错误
 */
function isAxiosError(error: unknown): error is AxiosErrorType {
  return axios.isAxiosError(error);
}

/**
 * 判断请求是否需要重试
 * @param error 错误对象
 * @param config 重试配置
 */
function isRetryable(error: unknown, config: RetryConfig): boolean {
  // 如果不是Axios错误，不进行重试
  if (!isAxiosError(error)) {
    return false;
  }

  // 如果请求被取消，不进行重试
  if (axios.isCancel(error)) {
    return false;
  }

  // 没有响应，可能是网络错误，可以重试
  if (!error.response) {
    return true;
  }

  // 检查状态码是否在可重试列表中
  return config.retryStatusCodes.includes(error.response.status);
}

/**
 * 请求重试装饰器
 * @param requestFn 请求函数
 * @param customConfig 自定义重试配置
 */
export function withRetry<T>(requestFn: () => Promise<T>, customConfig: Partial<RetryConfig> = {}): Promise<T> {
  // 合并配置
  const config: RetryConfig = {
    ...DEFAULT_RETRY_CONFIG,
    ...customConfig,
  };

  let retryCount = 0;

  // 创建重试函数
  const executeWithRetry = async (): Promise<T> => {
    try {
      return await requestFn();
    } catch (error) {
      // 判断是否可以重试
      if (retryCount < config.retry && isRetryable(error, config)) {
        retryCount++;

        // 触发回调
        if (config.onRetry) {
          config.onRetry(retryCount, error);
        }

        // 计算延迟时间（可以使用指数退避策略）
        const delay = config.retryDelay * Math.pow(2, retryCount - 1);

        // 延迟后重试
        await new Promise(resolve => setTimeout(resolve, delay));

        // 执行重试
        return executeWithRetry();
      }

      // 达到最大重试次数，或不需要重试，则抛出错误
      throw error;
    }
  };

  return executeWithRetry();
}

/**
 * 创建支持重试的请求方法
 */
export const retryRequest = {
  get<T = any>(url: string, params?: any, retryConfig?: Partial<RetryConfig>, axiosConfig?: any): Promise<T> {
    return withRetry<T>(() => service.get(url, { params, ...axiosConfig }).then(res => res.data), retryConfig);
  },

  post<T = any>(url: string, data?: any, retryConfig?: Partial<RetryConfig>, axiosConfig?: any): Promise<T> {
    return withRetry<T>(() => service.post(url, data, axiosConfig).then(res => res.data), retryConfig);
  },

  put<T = any>(url: string, data?: any, retryConfig?: Partial<RetryConfig>, axiosConfig?: any): Promise<T> {
    return withRetry<T>(() => service.put(url, data, axiosConfig).then(res => res.data), retryConfig);
  },

  delete<T = any>(url: string, retryConfig?: Partial<RetryConfig>, axiosConfig?: any): Promise<T> {
    return withRetry<T>(() => service.delete(url, axiosConfig).then(res => res.data), retryConfig);
  },
};

export default retryRequest;
