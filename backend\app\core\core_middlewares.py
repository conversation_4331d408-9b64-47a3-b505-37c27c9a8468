from fastapi import FastAPI, Request, status, Response
from fastapi.responses import JSONResponse
from pydantic import ValidationError
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.util import get_remote_address
from sqlalchemy.exc import SQLAlchemyError

from app.core.core_exceptions import BaseAppException
from app.core.core_log import logger

# 安全相关硬编码配置
SECURITY_STRICT_TRANSPORT = False  # 是否启用严格传输安全
SECURITY_HSTS_MAX_AGE = 31536000  # 严格传输安全的最大年龄（秒）
SECURITY_CSP_ENABLED = False  # 是否启用内容安全策略
SECURITY_CSP_POLICY = "default-src 'self'; img-src 'self' data:; script-src 'self'; style-src 'self';"  # 内容安全策略

# 限速器配置
limiter = Limiter(key_func=get_remote_address)


async def exception_handler_middleware(request: Request, call_next):
    """全局异常处理中间件"""
    try:
        return await call_next(request)
    except BaseAppException as exc:
        # 自定义异常处理
        logger.warning(f"应用异常: {exc.detail}")
        return JSONResponse(status_code=exc.status_code, content={"code": exc.status_code, "message": exc.detail})
    except ValidationError as exc:
        # Pydantic模型验证异常
        logger.warning(f"数据验证异常: {exc}")
        return JSONResponse(status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, content={"code": status.HTTP_422_UNPROCESSABLE_ENTITY, "message": "数据验证失败", "errors": exc.errors()})
    except SQLAlchemyError as exc:
        # 数据库异常
        logger.error(f"数据库异常: {exc}")
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content={"code": status.HTTP_500_INTERNAL_SERVER_ERROR, "message": "数据库操作失败"})
    except Exception as exc:
        # 未处理的异常
        logger.exception(f"未处理的异常: {exc}")
        return JSONResponse(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, content={"code": status.HTTP_500_INTERNAL_SERVER_ERROR, "message": "服务器内部错误"})


def setup_middlewares(app: FastAPI):
    """设置应用中间件
    
    Args:
        app: FastAPI应用实例
    """
    # 注册限速异常处理器
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

    # 注册全局异常处理中间件
    @app.middleware("http")
    async def global_exception_handler(request: Request, call_next):
        """全局异常处理中间件包装器"""
        return await exception_handler_middleware(request, call_next)

    # 注册其他自定义中间件
    @app.middleware("http")
    async def audit_log_middleware(request: Request, call_next):
        """审计日志中间件"""
        if _should_audit(request.url.path):
            logger.info(f"审计: {request.method} {request.url.path} - 客户端: {request.client.host}")

        response = await call_next(request)
        return response

    # 注册安全头部中间件
    @app.middleware("http")
    async def add_security_headers(request: Request, call_next):
        """添加安全响应头中间件"""
        response = await call_next(request)

        # 添加安全头部
        if isinstance(response, Response):
            response.headers["X-Content-Type-Options"] = "nosniff"
            response.headers["X-Frame-Options"] = "DENY"
            response.headers["X-XSS-Protection"] = "1; mode=block"

            if SECURITY_STRICT_TRANSPORT:
                response.headers["Strict-Transport-Security"] = f"max-age={SECURITY_HSTS_MAX_AGE}; includeSubDomains"

            if SECURITY_CSP_ENABLED:
                response.headers["Content-Security-Policy"] = SECURITY_CSP_POLICY

        return response


def _should_audit(path: str) -> bool:
    """判断是否需要审计记录
    
    Args:
        path: 请求路径
        
    Returns:
        bool: 是否需要审计
    """
    # 排除一些不需要审计的路径
    excluded_paths = ["/docs", "/redoc", "/openapi.json", "/health"]

    for excluded in excluded_paths:
        if path.startswith(excluded):
            return False

    # 如果是API路径且非GET请求，需要审计
    if path.startswith("/api") and path != "/api/":
        return True

    return False
