import time

from app.core.core_celery import celery_app
from app.core.core_log import logger


@celery_app.task(name="example.long_running_task")
def long_running_task(task_name: str, sleep_time: int = 10):
    """
    示例长时间运行任务
    
    Args:
        task_name: 任务名称
        sleep_time: 睡眠时间（秒）
    
    Returns:
        任务结果
    """
    logger.info(f"开始执行长时间运行任务: {task_name}")

    # 模拟长时间运行
    for i in range(sleep_time):
        time.sleep(1)
        logger.info(f"任务 {task_name} 进度: {(i + 1) / sleep_time:.0%}")

    logger.info(f"长时间运行任务 {task_name} 完成")
    return {"status": "completed", "task_name": task_name, "duration": sleep_time}


@celery_app.task(name="example.periodic_task")
def periodic_task():
    """
    示例定期执行任务
    """
    logger.info("执行定期任务")
    # 定期任务逻辑
    return {"status": "success", "timestamp": time.time()}
