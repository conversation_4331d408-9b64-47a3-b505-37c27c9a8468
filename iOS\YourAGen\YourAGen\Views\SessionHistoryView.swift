import SwiftUI

struct SessionHistoryView: View {
    let sessions: [Session]
    let onSessionSelected: (Session) -> Void
    
    @EnvironmentObject var languageManager: LanguageManager
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            Group {
                if sessions.isEmpty {
                    emptyStateView
                } else {
                    sessionsList
                }
            }
            .navigationTitle(languageManager.localized("chat_history"))
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(languageManager.localized("cancel")) {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "clock.arrow.circlepath")
                .font(.system(size: 60))
                .foregroundColor(.gray.opacity(0.6))
            
            VStack(spacing: 8) {
                Text("暂无聊天记录")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text("开始一个新对话来创建聊天记录")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var sessionsList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(sessions) { session in
                    SessionCard(
                        session: session,
                        onTap: {
                            onSessionSelected(session)
                        }
                    )
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 20)
        }
    }
}

struct SessionCard: View {
    let session: Session
    let onTap: () -> Void
    
    @EnvironmentObject var playgroundStore: PlaygroundStore
    @State private var isLoading = false
    
    var body: some View {
        Button(action: {
            // 避免重复点击
            if isLoading || playgroundStore.isLoading {
                return
            }
            
            isLoading = true
            onTap()
            
            // 延迟重置加载状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                isLoading = false
            }
        }) {
            VStack(alignment: .leading, spacing: 12) {
                // 头部信息
                HStack(spacing: 12) {
                    // 会话图标或加载指示器
                    ZStack {
                        if isLoading || (playgroundStore.isLoading && playgroundStore.currentSessionId == session.sessionId) {
                            ProgressView()
                                .scaleEffect(0.8)
                                .frame(width: 40, height: 40)
                        } else {
                            Image(systemName: playgroundStore.currentSessionId == session.sessionId ? "message.circle.fill" : "message.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(.white)
                                .frame(width: 40, height: 40)
                                .background(
                                    Circle()
                                        .fill(playgroundStore.currentSessionId == session.sessionId ? Color.green.gradient : Color.blue.gradient)
                                )
                        }
                    }
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text(session.title)
                            .font(.headline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                        
                        if let agentName = session.agentName {
                            HStack(spacing: 4) {
                                Image(systemName: "brain")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                Text(agentName)
                                    .font(.caption)
                                    .foregroundColor(.blue)
                            }
                        } else if let selectedAgent = playgroundStore.selectedAgent {
                            HStack(spacing: 4) {
                                Image(systemName: "brain")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                Text(selectedAgent.name)
                                    .font(.caption)
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text(formatDate(session.updatedAt))
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        if session.messageCount > 0 {
                            Text("\(session.messageCount) 条消息")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        
                        // 当前会话指示器
                        if playgroundStore.currentSessionId == session.sessionId {
                            HStack(spacing: 4) {
                                Circle()
                                    .fill(Color.green)
                                    .frame(width: 6, height: 6)
                                Text("当前")
                                    .font(.caption2)
                                    .foregroundColor(.green)
                            }
                        }
                    }
                }
                
                // 最后一条消息预览
                if let lastMessage = session.lastMessage, !lastMessage.isEmpty {
                    Text(lastMessage)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                        .padding(.leading, 52) // 对齐到图标右侧
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(playgroundStore.currentSessionId == session.sessionId ? Color(.systemGray6) : Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(playgroundStore.currentSessionId == session.sessionId ? Color.green.opacity(0.3) : Color.clear, lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isLoading || playgroundStore.isLoading)
        .opacity((isLoading || playgroundStore.isLoading) && playgroundStore.currentSessionId != session.sessionId ? 0.6 : 1.0)
    }
    
    private func formatDate(_ timestamp: TimeInterval) -> String {
        let date = Date(timeIntervalSince1970: timestamp)
        let now = Date()
        let calendar = Calendar.current
        
        if calendar.isDate(date, inSameDayAs: now) {
            let formatter = DateFormatter()
            formatter.timeStyle = .short
            return formatter.string(from: date)
        } else if let yesterday = calendar.date(byAdding: .day, value: -1, to: now),
                  calendar.isDate(date, inSameDayAs: yesterday) {
            return "昨天"
        } else if calendar.dateInterval(of: .weekOfYear, for: now)?.contains(date) == true {
            let formatter = DateFormatter()
            formatter.dateFormat = "EEEE"
            return formatter.string(from: date)
        } else {
            let formatter = DateFormatter()
            formatter.dateStyle = .short
            return formatter.string(from: date)
        }
    }
}

#Preview {
    SessionHistoryView(
        sessions: [],
        onSessionSelected: { _ in }
    )
    .environmentObject(LanguageManager())
} 