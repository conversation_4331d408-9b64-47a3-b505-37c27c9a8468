import type { PlaygroundChatMessage } from '@/types/playground';

export interface TokenStats {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

/**
 * 计算单条消息的token统计
 */
export function calculateMessageTokens(message: PlaygroundChatMessage): TokenStats {
  const metrics = message.metrics;

  if (!metrics) {
    return {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0,
    };
  }

  const prompt_tokens = (metrics.prompt_tokens || 0) + (metrics.input_tokens || 0);
  const completion_tokens = (metrics.completion_tokens || 0) + (metrics.output_tokens || 0);
  const total_tokens = metrics.total_tokens || prompt_tokens + completion_tokens;

  return {
    prompt_tokens,
    completion_tokens,
    total_tokens,
  };
}

/**
 * 计算多条消息的总token统计
 */
export function calculateTotalTokens(messages: PlaygroundChatMessage[]): TokenStats {
  return messages.reduce(
    (total, message) => {
      const messageTokens = calculateMessageTokens(message);
      return {
        prompt_tokens: total.prompt_tokens + messageTokens.prompt_tokens,
        completion_tokens: total.completion_tokens + messageTokens.completion_tokens,
        total_tokens: total.total_tokens + messageTokens.total_tokens,
      };
    },
    {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0,
    }
  );
}

/**
 * 格式化token数量显示
 */
export function formatTokenCount(count: number): string {
  if (count === 0) return '0';
  if (count < 1000) return count.toString();
  if (count < 1000000) return `${(count / 1000).toFixed(1)}K`;
  return `${(count / 1000000).toFixed(1)}M`;
}

/**
 * 获取token使用的颜色类
 */
export function getTokenUsageColor(tokens: number): string {
  if (tokens === 0) return 'text-gray-400';
  if (tokens < 1000) return 'text-green-400';
  if (tokens < 10000) return 'text-yellow-400';
  if (tokens < 50000) return 'text-orange-400';
  return 'text-red-400';
}
