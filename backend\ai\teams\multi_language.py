"""
多语言AI团队模块

该模块实现了一个多语言路由团队，可以根据用户输入的语言自动路由到相应的语言代理。
支持中文、英文、日文、西班牙文、法文和德文。
"""

from typing import Optional

from agno.agent import Agent
from agno.models.deepseek import DeepSeek
from agno.storage.postgres import PostgresStorage
from agno.team.team import Team

from ai.database import db_url
from ai.settings import team_settings

# 日语代理
japanese_agent = Agent(
    name="日语代理",
    agent_id="japanese-agent",
    role="你只能用日语回答问题",
    model=DeepSeek(),
)

# 中文代理
chinese_agent = Agent(
    name="中文代理",
    agent_id="chinese-agent",
    role="你只能用中文回答问题",
    model=DeepSeek(),
)

# 西班牙语代理
spanish_agent = Agent(
    name="西班牙语代理",
    agent_id="spanish-agent",
    role="你只能用西班牙语回答问题",
    model=DeepSeek(),
)

# 法语代理
french_agent = Agent(
    name="法语代理",
    agent_id="french-agent",
    role="你只能用法语回答问题",
    model=DeepSeek(),
)

# 德语代理
german_agent = Agent(
    name="德语代理",
    agent_id="german-agent",
    role="你只能用德语回答问题",
    model=DeepSeek(),
)


def get_multi_language_team(
        model_id: Optional[str] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        debug_mode: bool = True,
):
    """
    获取多语言团队实例
    
    Args:
        model_id: 模型ID，默认使用DeepSeek Chat
        user_id: 用户ID
        session_id: 会话ID
        debug_mode: 是否开启调试模式
        
    Returns:
        Team: 多语言团队实例
    """
    model_id = model_id or team_settings.default_model

    return Team(
        name="多语言团队",
        mode="route",
        team_id="multi-language-team",
        model=DeepSeek(),
        members=[
            spanish_agent,
            japanese_agent,
            french_agent,
            german_agent,
            chinese_agent,
        ],
        description="你是一个语言路由器，负责将问题导向合适的语言代理。",
        instructions=[
            "识别用户问题的语言，并将其导向相应的语言代理。",
            "让语言代理用用户问题的语言来回答问题。",
            "如果用户用英语提问，直接用英语回答。",
            "如果用户使用的语言不是英语，或者我们没有对应语言的代理成员，用英语回复：",
            "'我只能回答以下语言的问题：中文、英文、西班牙文、日文、法文和德文。请用其中一种语言提问。'",
            "在导向代理之前，请务必检查用户输入的语言。",
            "对于不支持的语言（如意大利语），用英语回复上述信息。",
        ],
        session_id=session_id,
        user_id=user_id,
        markdown=True,
        show_tool_calls=True,
        show_members_responses=True,
        storage=PostgresStorage(
            table_name="multi_language_team",
            db_url=db_url,
            mode="team",
            auto_upgrade_schema=True,
        ),
        debug_mode=debug_mode,
    )
