<template>
  <div class="space-y-2 flex flex-col h-full min-w-0 overflow-hidden mx-1">
    <div
      class="text-xs font-bold uppercase text-gray-900 dark:text-white tracking-wider px-1 flex items-center gap-2 min-w-0 overflow-hidden select-none"
      style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
    >
      <span class="truncate select-none text-gray-900 dark:text-white" style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none">{{
        $t('playground.sessions.title')
      }}</span>
      <div class="flex-1 h-px bg-gradient-to-r from-gray-400/60 dark:from-gray-600/30 to-transparent"></div>
      <!-- 会话数量指示器 -->
      <div
        v-if="sessionsData && sessionsData.length > 0"
        class="font-light px-1.5 py-0.5 rounded-full backdrop-blur-sm shrink-0 select-none bg-gray-100 text-gray-700 dark:bg-[#1a1a1a] dark:text-[#e0e0e0]"
        style="font-size: 10px; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
      >
        {{ sessionsData.length }}
      </div>
    </div>

    <div v-if="isSessionsLoading" class="flex justify-center py-6">
      <div class="flex flex-col items-center gap-3 select-none" style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none">
        <div class="relative">
          <div class="w-6 h-6 border-2 border-slate-300 border-t-primary rounded-full animate-spin dark:border-gray-600 dark:border-t-white"></div>
          <div class="absolute inset-0 w-6 h-6 border-2 border-transparent border-t-blue-400 rounded-full animate-spin" style="animation-delay: 0.15s"></div>
        </div>
        <span
          class="text-xs text-gray-700 dark:text-[#e0e0e0] font-medium select-none"
          style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
          >{{ $t('playground.sessions.loading') }}...</span
        >
      </div>
    </div>

    <div v-else-if="!sessionsData || sessionsData.length === 0" class="text-center py-12">
      <div
        class="flex flex-col items-center text-gray-500 dark:text-gray-300 select-none"
        style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
      >
        <div class="relative mb-4">
          <div class="flex h-12 w-12 items-center justify-center rounded-xl relative overflow-hidden bg-white dark:bg-[#1a1a1a]">
            <n-icon class="text-gray-600 dark:text-gray-300 relative z-10 select-none" size="24">
              <ChatboxOutline />
            </n-icon>
          </div>
          <!-- 装饰性光点 -->
          <div class="absolute -top-1 -right-1 w-3 h-3 bg-blue-400/20 rounded-full animate-pulse"></div>
          <div class="absolute -bottom-1 -left-1 w-2 h-2 bg-purple-400/20 rounded-full animate-pulse" style="animation-delay: 0.5s"></div>
        </div>
        <p
          class="text-sm font-semibold mb-2 text-gray-900 dark:text-white select-none"
          style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
        >
          {{ $t('playground.sessions.empty') }}
        </p>
        <p
          class="text-xs font-medium leading-relaxed select-none text-gray-600 dark:text-[#b0b0b0]"
          style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
        >
          {{ $t('playground.sessions.emptyHint') }}
        </p>
      </div>
    </div>

    <div v-else class="space-y-1 flex-1 overflow-y-auto overflow-x-hidden pt-1 pb-2 min-w-0">
      <TransitionGroup name="session-list" tag="div">
        <div
          v-for="session in sessionsData"
          :key="session.session_id"
          class="group relative px-2 py-1 rounded-md cursor-pointer mb-1 backdrop-blur-sm overflow-hidden min-w-0 select-none session-card"
          :class="{
            'selected-session': currentSessionId === session.session_id,
            'switching-session': switchingSessionId === session.session_id,
          }"
          @click="selectSession(session)"
          @mousedown.prevent.stop
          @selectstart.prevent
          @dragstart.prevent
        >
          <div class="flex items-start justify-between relative z-10 min-w-0">
            <div class="flex-1 min-w-0">
              <h4
                v-if="!session.isEditing"
                class="text-xs font-normal text-gray-900 dark:text-white truncate mb-0.5 group-hover:text-gray-800 dark:group-hover:text-gray-100 transition-colors duration-200 leading-tight select-none"
                style="font-size: 11px; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
                @mousedown.prevent.stop
                @selectstart.prevent
                @dragstart.prevent
              >
                {{ session.title || $t('playground.sidebar.newChat') }}
              </h4>
              <n-input
                v-else
                ref="editInputRefs"
                v-model:value="session.editTitle"
                class="text-xs mb-0.5"
                size="tiny"
                @blur="finishEditTitle(session)"
                @keydown.enter="finishEditTitle(session)"
                @keydown.esc="cancelEditTitle(session)"
              />
              <p
                class="text-xs font-extralight group-hover:text-gray-600 dark:group-hover:text-gray-400 transition-colors duration-200 leading-tight truncate select-none text-gray-600 dark:text-[#d0d0d0]"
                style="font-size: 9px; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none"
                @mousedown.prevent.stop
                @selectstart.prevent
                @dragstart.prevent
              >
                {{ formatDateTime(session.created_at) }}
              </p>
            </div>
          </div>

          <!-- 操作按钮 - 绝对定位悬浮覆盖 -->
          <div
            class="absolute top-0 right-0 bottom-0 flex items-center gap-0.5 pr-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-20 select-none action-buttons"
            @mousedown.prevent.stop
            @selectstart.prevent
            @dragstart.prevent
          >
            <button
              :title="$t('playground.sessions.rename')"
              class="flex h-4 w-4 items-center justify-center rounded transition-all duration-200 relative overflow-hidden select-none edit-button"
              @click.stop="startEditTitle(session)"
              @mousedown.prevent.stop
              @selectstart.prevent
              @dragstart.prevent
            >
              <n-icon class="text-gray-400 hover:text-white transition-colors duration-200 relative z-10 select-none" size="9">
                <CreateOutline />
              </n-icon>
            </button>
            <button
              :title="$t('playground.sessions.delete')"
              class="flex h-4 w-4 items-center justify-center rounded transition-all duration-200 relative overflow-hidden select-none delete-button"
              @click.stop="confirmDeleteSession(session.session_id)"
              @mousedown.prevent.stop
              @selectstart.prevent
              @dragstart.prevent
            >
              <n-icon class="text-red-500 hover:text-red-400 transition-colors duration-200 relative z-10 select-none" size="9">
                <TrashOutline />
              </n-icon>
            </button>
          </div>
        </div>
      </TransitionGroup>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, ref, onMounted, watch } from 'vue';
import { NIcon, NInput, useDialog, useMessage } from 'naive-ui';
import { ChatboxOutline, CreateOutline, TrashOutline } from '@vicons/ionicons5';
import { usePlaygroundStore } from '@/stores/playground';
import { useStreamHandler } from '@/composables/useStreamHandler';
import { request } from '@/utils/request';
import type { SessionEntry } from '@/types/playground';
import { formatDateTime } from '@/utils/time';
import { useRouter, useRoute } from 'vue-router';

// 扩展 SessionEntry 类型以支持编辑状态
interface EditableSessionEntry extends SessionEntry {
  isEditing?: boolean;
  editTitle?: string;
}

const playgroundStore = usePlaygroundStore();
const { fetchSessionData, getAgentApiPath } = useStreamHandler();
const message = useMessage();
const dialog = useDialog();
const router = useRouter();
const route = useRoute();
const editInputRefs = ref<any[]>([]);

// 添加会话切换状态
const switchingSessionId = ref<string | null>(null);

const sessionsData = computed(() => playgroundStore.sessionsData as EditableSessionEntry[] | null);
const isSessionsLoading = computed(() => playgroundStore.isSessionsLoading);
const selectedAgentId = computed(() => playgroundStore.selectedAgent);
const currentSessionId = computed(() => playgroundStore.currentSessionId);

// 在组件挂载时检查URL中的参数
onMounted(async () => {
  const urlAgentId = route.query['agent-id'] as string;
  const urlSessionId = route.query['session-id'] as string;

  if (urlAgentId && urlSessionId) {
    await handleUrlParams(urlAgentId, urlSessionId);
  }
});

// 监听sessionsData变化，如果数据加载完成且URL中有参数，则自动选择
watch(sessionsData, async (newSessions: any) => {
  if (newSessions && newSessions.length > 0) {
    const urlSessionId = route.query['session-id'] as string;
    if (urlSessionId && !currentSessionId.value) {
      const session = newSessions.find((s: any) => s.session_id === urlSessionId);
      if (session) {
        await selectSession(session, false);
      }
    }
  }
});

// 监听路由变化，当query参数变化时自动切换
watch(
  () => [route.query['agent-id'], route.query['session-id']],
  async newValues => {
    const [newAgentId, newSessionId] = newValues;
    const agentId = Array.isArray(newAgentId) ? newAgentId[0] : newAgentId;
    const sessionId = Array.isArray(newSessionId) ? newSessionId[0] : newSessionId;

    if (agentId && sessionId && currentSessionId.value !== sessionId) {
      await handleUrlParams(agentId, sessionId);
    } else if (!sessionId && currentSessionId.value) {
      // 如果URL中没有sessionId但当前有选中的会话，清空选中状态
      playgroundStore.setCurrentSessionId(null);
      playgroundStore.clearMessages();
    }
  }
);

// 处理URL中的agent-id和session-id参数
const handleUrlParams = async (agentId: string, sessionId: string) => {
  try {
    // 如果当前已经选中了这个智能体和会话，直接返回
    if (selectedAgentId.value === agentId && currentSessionId.value === sessionId) {
      return;
    }

    // 如果需要切换智能体
    if (selectedAgentId.value !== agentId) {
      // 检查智能体是否存在
      const agents = playgroundStore.agents;
      const targetAgent = agents.find(agent => agent.value === agentId);

      if (!targetAgent) {
        message.error(`智能体 ${agentId} 不存在`);
        // 清除URL参数
        await router.push({ name: 'Playground' });
        return;
      }

      // 切换智能体
      playgroundStore.setSelectedAgent(agentId);
      playgroundStore.clearMessages();
      playgroundStore.setCurrentSessionId(null);

      // 加载该智能体的会话列表
      const response = await request.get(`${getAgentApiPath(agentId)}/sessions`);
      const sessions = response.data || response;
      playgroundStore.setSessionsData(sessions);

      message.success(`已切换到智能体: ${targetAgent.label}`);
    }

    // 选择会话
    const sessions = sessionsData.value;
    if (sessions) {
      const targetSession = sessions.find((s: any) => s.session_id === sessionId);
      if (targetSession) {
        await selectSession(targetSession, false);
      } else {
        message.error(`会话 ${sessionId} 不存在`);
        // 只清除session-id参数，保留agent-id
        await router.push({
          name: 'Playground',
          query: { 'agent-id': agentId },
        });
      }
    }
  } catch (error) {
    console.error('处理URL参数时出错:', error);
    message.error('加载会话失败');
    await router.push({ name: 'Playground' });
  }
};

const selectSession = async (session: EditableSessionEntry, updateUrl = true) => {
  if (session.isEditing) return;

  try {
    if (!selectedAgentId.value) {
      message.error('请先选择一个智能体');
      return;
    }

    // 检查是否已经是当前会话，避免重复加载
    if (currentSessionId.value === session.session_id) {
      return;
    }

    // 检查是否正在切换到这个会话
    if (switchingSessionId.value === session.session_id) {
      return;
    }

    // 设置切换状态
    switchingSessionId.value = session.session_id;

    // 添加小延迟确保切换效果显示
    await new Promise(resolve => setTimeout(resolve, 100));

    // 先设置当前会话ID，避免按钮状态闪烁
    playgroundStore.setCurrentSessionId(session.session_id);

    // 更新URL参数
    if (updateUrl) {
      try {
        await router.push({
          name: 'Playground',
          query: {
            'agent-id': selectedAgentId.value,
            'session-id': session.session_id,
          },
        });
      } catch (error) {
        // 如果路由跳转失败，可能是因为已经在目标路由，忽略错误
        console.log('Router navigation handled:', error);
      }
    }

    const sessionData = await fetchSessionData(session.session_id, selectedAgentId.value);

    if (sessionData && sessionData.length > 0) {
      // 直接设置消息，不先清空
      playgroundStore.setMessages(sessionData);
      message.success(`已切换到会话: ${session.title || '新对话'}`);
    } else {
      // 如果没有数据，则清空消息
      playgroundStore.clearMessages();
      message.warning('该会话暂无对话记录');
    }
  } catch (error) {
    console.error('加载会话失败:', error);
    message.error('加载会话失败，请稍后重试');
  } finally {
    // 添加小延迟确保切换效果平滑结束
    await new Promise(resolve => setTimeout(resolve, 150));
    // 清除切换状态
    switchingSessionId.value = null;
  }
};

const confirmDeleteSession = (sessionId: string) => {
  dialog.warning({
    title: '确认删除',
    content: '确定要删除这个会话吗？此操作无法撤销。',
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      deleteSession(sessionId);
    },
  });
};

const deleteSession = async (sessionId: string) => {
  try {
    // 检查是否正在删除当前会话
    const isCurrentSession = currentSessionId.value === sessionId;

    // 从本地状态中移除会话
    const sessions = sessionsData.value;
    if (sessions) {
      const updatedSessions = sessions.filter((s: any) => s.session_id !== sessionId);
      playgroundStore.setSessionsData(updatedSessions);
    }

    // 调用API删除会话，使用正确的路径格式
    const selectedAgentId = playgroundStore.selectedAgent;
    if (selectedAgentId) {
      await request.delete(`${getAgentApiPath(selectedAgentId)}/sessions/${sessionId}`);
    }

    // 如果删除的是当前会话，需要处理URL和状态
    if (isCurrentSession) {
      // 清空当前会话状态
      playgroundStore.setCurrentSessionId(null);
      playgroundStore.clearMessages();

      // 返回到智能体页面（只保留agent-id参数）
      try {
        if (selectedAgentId) {
          await router.push({
            name: 'Playground',
            query: { 'agent-id': selectedAgentId },
          });
        } else {
          await router.push({ name: 'Playground' });
        }
      } catch (error) {
        console.log('Router navigation handled:', error);
      }
    }

    message.success('会话已删除');
  } catch (error) {
    console.error('删除会话失败:', error);
    message.error('删除会话失败');
  }
};

const startEditTitle = async (session: EditableSessionEntry) => {
  session.isEditing = true;
  session.editTitle = session.title || '新对话';

  await nextTick();
  // 聚焦输入框
  const inputElement = editInputRefs.value?.find(ref => ref?.$el);
  if (inputElement) {
    inputElement.focus();
  }
};

const finishEditTitle = async (session: EditableSessionEntry) => {
  if (!session.editTitle?.trim()) {
    cancelEditTitle(session);
    return;
  }

  const newTitle = session.editTitle.trim();

  try {
    // 调用API更新会话标题，使用正确的路径格式
    const selectedAgentId = playgroundStore.selectedAgent;
    if (selectedAgentId) {
      await request.put(`${getAgentApiPath(selectedAgentId)}/sessions/${session.session_id}`, {
        title: newTitle,
      });
    }

    session.title = newTitle;
    session.isEditing = false;
    delete session.editTitle;

    message.success('会话标题已更新');
  } catch (error) {
    console.error('更新会话标题失败:', error);
    message.error('更新会话标题失败');
    cancelEditTitle(session);
  }
};

const cancelEditTitle = (session: EditableSessionEntry) => {
  session.isEditing = false;
  delete session.editTitle;
};
</script>

<style scoped>
/* 会话列表过渡动画 */
.session-list-enter-active,
.session-list-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.session-list-enter-from {
  opacity: 0;
  transform: translateX(-30px) scale(0.95);
}

.session-list-leave-to {
  opacity: 0;
  transform: translateX(30px) scale(0.95);
}

.session-list-move {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 滚动条优化 - 短小抓手，只允许垂直滚动 */
.flex-1::-webkit-scrollbar {
  width: 4px;
}

.flex-1::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

.flex-1::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgb(75 85 99), rgb(55 65 81));
  border-radius: 2px;
  min-height: 20px;
  max-height: 40px;
}

.flex-1::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgb(107 114 128), rgb(75 85 99));
}

.flex-1::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条样式 */
.flex-1 {
  scrollbar-width: thin;
  scrollbar-color: rgb(75 85 99) transparent;
  overflow-x: hidden !important;
}

/* 会话卡片基础样式 */
.session-card {
  background-color: #ffffff;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.session-card:hover {
  background-color: #f8f9fa;
}

.dark .session-card {
  background-color: #2d2d2d;
}

.dark .session-card:hover {
  background-color: #3a3a3a;
}

/* 选中会话状态样式 */
.selected-session {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
}

.dark .selected-session {
  background: linear-gradient(135deg, #3a4553 0%, #2a3441 100%) !important;
}

/* 操作按钮区域样式 */
.action-buttons {
  background: linear-gradient(to left, #f8f9fa 60%, transparent 100%);
  transition: background 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.selected-session .action-buttons {
  background: linear-gradient(to left, #e5e7eb 60%, transparent 100%);
}

.dark .action-buttons {
  background: linear-gradient(to left, #3a3a3a 60%, transparent 100%);
}

.dark .selected-session .action-buttons {
  background: linear-gradient(to left, #2a3441 60%, transparent 100%);
}

/* 操作按钮样式 */
.edit-button {
  background-color: #f3f4f6;
  transition: background-color 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.edit-button:hover {
  background-color: #e5e7eb;
}

.selected-session .edit-button {
  background-color: #d1d5db;
}

.selected-session .edit-button:hover {
  background-color: #9ca3af;
}

.dark .edit-button {
  background-color: #404040;
}

.dark .edit-button:hover {
  background-color: #4a4a4a;
}

.dark .selected-session .edit-button {
  background-color: #3a4553;
}

.dark .selected-session .edit-button:hover {
  background-color: #2a3441;
}

.delete-button {
  background-color: #fed7d7;
  transition: background-color 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.delete-button:hover {
  background-color: #feb2b2;
}

.selected-session .delete-button {
  background-color: #fc8181;
}

.selected-session .delete-button:hover {
  background-color: #f56565;
}

.dark .delete-button {
  background-color: #4a2626;
}

.dark .delete-button:hover {
  background-color: #5a2d2d;
}

.dark .selected-session .delete-button {
  background-color: #4a3535;
}

.dark .selected-session .delete-button:hover {
  background-color: #5a3a3a;
}

/* 切换会话状态样式 */
.switching-session {
  filter: grayscale(0.5) brightness(0.8);
  pointer-events: none;
  transition: filter 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.switching-session:hover {
  filter: grayscale(0.5) brightness(0.8) !important;
}

/* 输入框样式统一 */
:deep(.n-input) {
  border-radius: 6px !important;
  background-color: rgb(255 255 255) !important;
  border: none !important;
  backdrop-filter: blur(8px) !important;
  box-shadow: none !important;
}

.dark :deep(.n-input) {
  background-color: rgb(17 24 39) !important;
  border: none !important;
  box-shadow: none !important;
}

:deep(.n-input .n-input__input-el) {
  font-size: 10px !important;
  font-weight: 400 !important;
  color: rgb(55 65 81) !important;
  background: transparent !important;
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif !important;
  letter-spacing: 0.01em !important;
}

.dark :deep(.n-input .n-input__input-el) {
  color: rgb(255 255 255) !important;
}

:deep(.n-input:hover) {
  box-shadow: none !important;
}

.dark :deep(.n-input:hover) {
  box-shadow: none !important;
}

:deep(.n-input.n-input--focus) {
  box-shadow: none !important;
  border: none !important;
  outline: none !important;
}
</style>
