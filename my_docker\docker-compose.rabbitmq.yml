services:

  rabbitmq:
    image: rabbitmq:management
    container_name: rabbitmq
    restart: always
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: johnxiong
      RABBITMQ_DEFAULT_PASS: johnxiong
      TZ: Asia/Shanghai
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    deploy:
      resources:
        limits:
          memory: 512M  # 根据需要调整内存限制

volumes:
  rabbitmq_data: