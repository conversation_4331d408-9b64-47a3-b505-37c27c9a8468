import SwiftUI

struct MessageBubble: View {
    let message: Message
    let agentIcon: String?
    @EnvironmentObject var languageManager: LanguageManager
    @Environment(\.colorScheme) var colorScheme
    @State private var showCopyAlert = false
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            if message.role == .user {
                Spacer()
                messageContent
                    .contextMenu {
                        Button(action: copyMessage) {
                            Label(languageManager.localized("copy"), systemImage: "doc.on.doc")
                        }
                    }
                userAvatar
            } else {
                agentAvatar
                messageContent
                    .contextMenu {
                        Button(action: copyMessage) {
                            Label(languageManager.localized("copy"), systemImage: "doc.on.doc")
                        }
                    }
                Spacer()
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 8)
        .alert("已复制到剪贴板", isPresented: $showCopyAlert) {
            Button("确定") { }
        }
    }
    
    private var messageContent: some View {
        VStack(alignment: message.role == .user ? .trailing : .leading, spacing: 8) {
            // 主要消息内容
            if message.content.isEmpty && message.isStreaming {
                // 流式响应时的动画效果
                streamingIndicator
            } else if !message.content.isEmpty {
                // 消息气泡
                messageBubble
            } else {
                // 空消息占位符
                emptyMessagePlaceholder
            }
            
            // 工具调用显示
            if let toolCalls = message.toolCalls, !toolCalls.isEmpty {
                toolCallsView(toolCalls)
            }
            
            // 错误状态显示
            if message.streamingError {
                errorView
            }
            
            // 消息指标（Token使用情况等）
            if let metrics = message.metrics, message.role == .agent {
                metricsView(metrics)
            }
            
            // 时间戳
            timestampView
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: message.role == .user ? .trailing : .leading)
    }
    
    private var messageBubble: some View {
        VStack(alignment: .leading, spacing: 0) {
            if message.role == .agent {
                // AI消息使用增强的Markdown渲染
                MarkdownText(
                    message.content,
                    fontSize: 14,
                    textColor: agentTextColor,
                    isInBubble: true
                )
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
            } else {
                // 用户消息使用普通文本
                Text(message.content)
                    .font(.system(size: 14))
                    .foregroundColor(userTextColor)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(messageBackgroundColor)
                .shadow(
                    color: shadowColor,
                    radius: message.role == .agent ? 8 : 4,
                    x: 0,
                    y: message.role == .agent ? 2 : 1
                )
        )
        .overlay(
            // AI消息添加渐变边框
            message.role == .agent ? 
            RoundedRectangle(cornerRadius: 20)
                .stroke(agentBorderGradient, lineWidth: 1)
                .opacity(0.3)
            : nil
        )
    }
    
    private var streamingIndicator: some View {
        HStack(spacing: 4) {
            ForEach(0..<3, id: \.self) { index in
                Circle()
                    .fill(streamingDotColor)
                    .frame(width: 6, height: 6)
                    .scaleEffect(message.isStreaming ? 1.0 : 0.5)
                    .animation(
                        Animation.easeInOut(duration: 0.6)
                            .repeatForever()
                            .delay(Double(index) * 0.2),
                        value: message.isStreaming
                    )
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(messageBackgroundColor)
                .shadow(color: shadowColor, radius: 4, x: 0, y: 1)
        )
    }
    
    private var emptyMessagePlaceholder: some View {
        Text(languageManager.localized("typing"))
            .font(.body)
            .foregroundColor(message.role == .user ? userTextColor : agentTextColor)
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(messageBackgroundColor)
                    .shadow(color: shadowColor, radius: 4, x: 0, y: 1)
            )
    }
    
    private func toolCallsView(_ toolCalls: [ToolCall]) -> some View {
        VStack(alignment: .leading, spacing: 6) {
            ForEach(toolCalls) { toolCall in
                HStack(spacing: 8) {
                    Image(systemName: "wrench.and.screwdriver")
                        .foregroundColor(.orange)
                        .font(.caption)
                    Text("调用工具: \(toolCall.name)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Spacer()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(toolCallBackgroundColor)
                        .shadow(color: shadowColor.opacity(0.3), radius: 2, x: 0, y: 1)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
            }
        }
    }
    
    private var errorView: some View {
        HStack(spacing: 8) {
            Image(systemName: "exclamationmark.triangle")
                .foregroundColor(.red)
                .font(.caption)
            Text(languageManager.localized("stream_error"))
                .font(.caption)
                .foregroundColor(.red)
            Spacer()
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(errorBackgroundColor)
                .shadow(color: shadowColor.opacity(0.3), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.red.opacity(0.3), lineWidth: 1)
        )
    }
    
    private func metricsView(_ metrics: MessageMetrics) -> some View {
        HStack(spacing: 12) {
            if let time = metrics.time {
                Label("\(String(format: "%.1f", time))s", systemImage: "clock")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            if let totalTokens = metrics.totalTokens {
                Label("\(totalTokens)", systemImage: "textformat.123")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(metricsBackgroundColor)
        )
    }
    
    private var timestampView: some View {
        Text(formatTimestamp(message.createdAt))
            .font(.caption2)
            .foregroundColor(.secondary)
            .padding(.horizontal, 4)
    }
    
    // MARK: - 颜色计算属性
    
    private var messageBackgroundColor: Color {
        if message.role == .user {
            return userBubbleColor
        } else {
            return agentBubbleColor
        }
    }
    
    private var userBubbleColor: Color {
        if colorScheme == .dark {
            return Color.blue.opacity(0.8)
        } else {
            return Color.blue
        }
    }
    
    private var agentBubbleColor: Color {
        if colorScheme == .dark {
            return Color(.systemGray5)
        } else {
            return Color(.systemGray6)
        }
    }
    
    private var userTextColor: Color {
        return .white
    }
    
    private var agentTextColor: Color {
        if colorScheme == .dark {
            return Color(.label)
        } else {
            return Color(.label)
        }
    }
    
    private var shadowColor: Color {
        if colorScheme == .dark {
            return Color.black.opacity(0.3)
        } else {
            return Color.black.opacity(0.1)
        }
    }
    
    private var streamingDotColor: Color {
        if message.role == .user {
            return Color.white.opacity(0.7)
        } else {
            return Color.gray
        }
    }
    
    private var agentBorderGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color.blue.opacity(0.3),
                Color.purple.opacity(0.3)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private var toolCallBackgroundColor: Color {
        if colorScheme == .dark {
            return Color.orange.opacity(0.15)
        } else {
            return Color.orange.opacity(0.1)
        }
    }
    
    private var errorBackgroundColor: Color {
        if colorScheme == .dark {
            return Color.red.opacity(0.15)
        } else {
            return Color.red.opacity(0.1)
        }
    }
    
    private var metricsBackgroundColor: Color {
        if colorScheme == .dark {
            return Color(.systemGray4)
        } else {
            return Color(.systemGray5)
        }
    }
    
    // MARK: - 头像组件
    
    private var userAvatar: some View {
        Circle()
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.7)]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .frame(width: 36, height: 36)
            .overlay(
                Image(systemName: "person.fill")
                    .foregroundColor(.white)
                    .font(.system(size: 18, weight: .medium))
            )
            .shadow(color: shadowColor, radius: 4, x: 0, y: 2)
    }
    
    private var agentAvatar: some View {
        Circle()
            .fill(
                LinearGradient(
                    gradient: Gradient(colors: [
                        Color.green,
                        Color.blue,
                        Color.purple
                    ]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
            .frame(width: 36, height: 36)
            .overlay(
                Image(systemName: agentIcon ?? "brain")
                    .foregroundColor(.white)
                    .font(.system(size: 18, weight: .medium))
            )
            .shadow(color: shadowColor, radius: 4, x: 0, y: 2)
    }
    
    // MARK: - 辅助方法
    
    private func copyMessage() {
        UIPasteboard.general.string = message.content
        showCopyAlert = true
    }
    
    private func formatTimestamp(_ timestamp: TimeInterval) -> String {
        let date = Date(timeIntervalSince1970: timestamp)
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

#Preview {
    VStack(spacing: 16) {
        MessageBubble(
            message: Message(
                role: .user,
                content: "你好，请帮我写一个Swift函数来计算斐波那契数列"
            ),
            agentIcon: "person.circle"
        )
        
        MessageBubble(
            message: Message(
                role: .agent,
                content: """
                当然可以！我来帮你写一个计算斐波那契数列的Swift函数。

                这里有几种不同的实现方式：

                ## 1. 递归实现（简单但效率低）

                ```swift
                func fibonacci(_ n: Int) -> Int {
                    if n <= 1 {
                        return n
                    }
                    return fibonacci(n - 1) + fibonacci(n - 2)
                }
                ```

                ## 2. 迭代实现（推荐）

                ```swift
                func fibonacciIterative(_ n: Int) -> Int {
                    guard n > 1 else { return n }
                    
                    var a = 0
                    var b = 1
                    
                    for _ in 2...n {
                        let temp = a + b
                        a = b
                        b = temp
                    }
                    
                    return b
                }
                ```

                **使用示例：**

                ```swift
                let result = fibonacciIterative(10)
                print("第10个斐波那契数是: \\(result)")
                ```

                迭代版本的时间复杂度是 `O(n)`，空间复杂度是 `O(1)`，比递归版本效率更高。

                你可以根据需要选择合适的实现方式！
                """,
                metrics: MessageMetrics(
                    time: 2.3,
                    promptTokens: 15,
                    completionTokens: 125,
                    totalTokens: 140
                )
            ),
            agentIcon: "brain"
        )
        
        MessageBubble(
            message: Message(
                role: .agent,
                content: "",
                isStreaming: true
            ),
            agentIcon: "brain"
        )
    }
    .padding()
    .environmentObject(LanguageManager())
} 