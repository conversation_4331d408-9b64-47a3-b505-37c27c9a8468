import os
from typing import Union, Optional

from cryptography.fernet import Fernet
from dotenv import load_dotenv


class CoreEncryptor:
    """加密解密工具类，使用Fernet对称加密算法"""

    def __init__(self, key: Optional[str] = None):
        """
        初始化加密器实例

        Args:
            key: Fernet密钥，如果不提供则从环境变量FERNET_KEY获取

        Raises:
            ValueError: 当未提供密钥且环境变量中不存在时抛出
        """
        if key is None:
            load_dotenv()
            key = os.getenv("FERNET_KEY")
            if key is None:
                raise ValueError("未提供加密密钥且环境变量FERNET_KEY未设置")
        self.fernet = Fernet(key)

    def encrypt(self, data: Union[str, bytes]) -> bytes:
        """加密字符串或字节数据"""
        if isinstance(data, str):
            data = data.encode()
        return self.fernet.encrypt(data)

    def decrypt(self, encrypted_data: bytes) -> bytes:
        """解密加密的字节数据"""
        return self.fernet.decrypt(encrypted_data)

    def encrypt_to_string(self, data: Union[str, bytes]) -> str:
        """加密后返回字符串形式"""
        encrypted = self.encrypt(data)
        return encrypted.decode()

    def decrypt_to_string(self, encrypted_data: Union[str, bytes]) -> str:
        """解密后返回字符串形式"""
        if isinstance(encrypted_data, str):
            encrypted_data = encrypted_data.encode()
        decrypted = self.decrypt(encrypted_data)
        return decrypted.decode()


def generate_key() -> bytes:
    """生成新的Fernet加密密钥"""
    return Fernet.generate_key()


if __name__ == "__main__":
    # 使用示例
    encryptor = CoreEncryptor()
    original_string = "Hello, World!"
    encrypted_string = encryptor.encrypt(original_string)
    decrypted_string = encryptor.decrypt(encrypted_string).decode()

    print("原始字符串:", original_string)
    print("加密字符串:", encrypted_string)
    print("解密字符串:", decrypted_string)

    # 生成新密钥示例
    print("新生成的密钥:", generate_key().decode())
