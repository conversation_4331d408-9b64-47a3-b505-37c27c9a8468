# 全局性能优化
worker_processes auto;
worker_rlimit_nofile 65535;
events {
    worker_connections 4096;
    multi_accept on;
}

# HTTP 配置块
http {
    # 基础设置
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # DNS解析器设置
    resolver 127.0.0.11 valid=30s;
    resolver_timeout 5s;

    # 缓冲区优化
    client_max_body_size 16M;
    client_body_buffer_size 128k;

    # MIME 类型
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    gzip off;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # 限速配置
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;

    # 主站点配置 - 只处理 HTTP，HTTPS 由 Traefik 处理
    server {
        listen 80;
        server_name aiquant.io www.aiquant.io;

        # 设置 X-Forwarded-Proto 头信息
        set_real_ip_from **********/12;
        real_ip_header X-Forwarded-For;
        real_ip_recursive on;

        # 静态文件缓存
        location /coin/img/ {
            alias /root/PycharmProjects/aiquant2022/coin_img/;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
        }

        location /image/ {
            alias /root/PycharmProjects/aiquant2022/image/;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
        }

        location / {
            root /home/<USER>/aiquant-frontend/dist;
            index index.html;
            try_files $uri $uri/ /index.html;
        }
    }

    # API 子域名配置 - 只处理 HTTP
    map $host $upstream {
        default **********:8888; # api.aiquant.io

        testapi.aiquant.io **********:8889;
        supervisor.aiquant.io **********:9001;
        airflow.aiquant.io **********:9081;
        grafana.aiquant.io **********:3000;
        mq.aiquant.io **********:15672;

        registry.aiquant.io **********:5002;
        portainer.aiquant.io **********:5003;

        bark.aiquant.io **********:5006;
        v.aiquant.io **********:5007;
        rabbitmq.aiquant.io **********:5008;
    }

    server {
        listen 80;
        server_name ~^(.*\.)?aiquant\.io$;

        # 设置实际客户端 IP
        set_real_ip_from **********/12;
        real_ip_header X-Forwarded-For;
        real_ip_recursive on;

        location / {
            # 动态解析上游地址
            set $backend_server http://$upstream;
            proxy_pass $backend_server;

            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $http_x_forwarded_proto;

            # 处理 WebSocket
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";

            # 超时设置
            proxy_read_timeout 300;
            proxy_connect_timeout 300;
            proxy_send_timeout 300;
        }
    }
}