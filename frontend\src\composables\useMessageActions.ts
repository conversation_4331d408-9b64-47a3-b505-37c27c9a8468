import { useMessage, useDialog } from 'naive-ui';
import type { PlaygroundChatMessage } from '@/types/playground';
import { usePlaygroundStore } from '@/stores/playground';
import { copyToClipboard } from '@/utils/playground';

export function useMessageActions() {
  const playgroundStore = usePlaygroundStore();
  const messageApi = useMessage();
  const dialog = useDialog();

  // 复制消息
  const copyMessage = async (content: string) => {
    const success = await copyToClipboard(content || '');
    messageApi[success ? 'success' : 'error'](success ? '消息已复制到剪贴板' : '复制失败，请手动复制');
  };

  // 删除消息
  const deleteMessage = (message: PlaygroundChatMessage, messageType: 'user' | 'agent' = 'user') => {
    const title = '确认删除';
    const content = messageType === 'user' ? '确定要删除这条用户消息吗？' : '确定要删除这条 AI 消息吗？';

    dialog.warning({
      title,
      content,
      positiveText: '删除',
      negativeText: '取消',
      onPositiveClick: () => {
        const messages = playgroundStore.messages;
        playgroundStore.setMessages(messages.filter(m => m !== message));
        messageApi.success('消息已删除');
      },
    });
  };

  return {
    copyMessage,
    deleteMessage,
  };
}
